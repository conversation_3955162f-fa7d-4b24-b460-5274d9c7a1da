import { defineConfig } from 'windicss/helpers';

export default defineConfig({
  scan: {
    dirs: ['./src', './admin', './app', 'node_modules/@vpscope'],
    exclude: ['node_modules', '.git', 'dist', 'public'],
  },
  extract: {
    include: [
      './src/**/*.{html,vue,jsx,tsx}',
      './admin/**/*.{html,vue,jsx,tsx}',
      './app/**/*.{html,vue,jsx,tsx}',
      'node_modules/@vpscope/**/*.{html,vue,jsx,tsx,svelte}',
    ],
  },
  theme: {
    screens: {
      '3xl': '1870px',
    },
    extend: {
      colors: {
        primary: '#6777ef',
      },
    },
  },
});
