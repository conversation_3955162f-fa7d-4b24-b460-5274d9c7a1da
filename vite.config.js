import { defineConfig, splitVendorChunkPlugin } from 'vite';
import vue from '@vitejs/plugin-vue2';
import Legacy from '@vitejs/plugin-legacy';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import WindiCSS from 'vite-plugin-windicss';
import vueSetupExtend from 'vite-plugin-vue-setup-extend';
import path from 'node:path';
import dotenv from 'dotenv';
import { replaceUtilsImportsPlugin } from '@vpscope/replace-utils-imports-plugin';

// 加载.env文件中的变量
dotenv.config();
process.env.SASS_PATH = 'quietDeps';
const APP_CODE = process.env.VITE_APP_CODE;

export default defineConfig({
  optimizeDeps: {
    include: ['@vpscope/dlib3-ui > qs'],
    force: true,
  },
  plugins: [
    vue(),
    vueSetupExtend(),
    splitVendorChunkPlugin(),
    WindiCSS(),
    // Legacy({
    //   targets: ['defaults', 'ie >= 11'],
    //   additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
    // }),
    Components({
      dirs: ['src/components'],
    }),
    AutoImport({
      imports: [
        'vue',
        '@vueuse/core',
        {
          // '@vpscope/dlib3-utils': [
          //   'APP_CODE',
          // ],
          '@vpscope/dlib3-ui': ['DMessage', 'DMessageBox'],
        },
      ],
    }),
    {
      name: 'singleHMR',
      handleHotUpdate({ modules }) {
        return modules.map((m) => ({
          ...m,
          importedModules: new Set(),
          importers: new Set(),
        }));
      },
    },
    replaceUtilsImportsPlugin(),
  ],
  css: {
    loaderOptions: {
      sass: {
        sassOptions: {
          quietDeps: true,
        },
      },
    },
  },
  build: {
    cssTarget: 'chrome61',
    rollupOptions: {
      input: {
        main: './index.html',
        // app: './app/index.html',
        // admin: './admin/index.html',
      },
      minify: 'terser', // 启用 terser 压缩
      terserOptions: {
        compress: {
          drop_console: true, // 删除所有 console
          drop_debugger: true,
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': path.join(__dirname, './src'),
      '~': path.join(__dirname, './'),
      vue: 'vue/dist/vue.esm.js',
    },
    extensions: ['.js', '.vue'],
    mainFiles: ['index', 'main', 'module'],
  },
  server: {
    port: 6001,
    proxy: {
      [`/${APP_CODE}`]: {
        target: 'http://localhost:6001',
        changeOrigin: true,
        rewrite: (path) => path.replace(`/${APP_CODE}`, ''),
      },
      '/cdn': {
        target: 'http://**************/cdn',
        // target: 'http://cqu.smartlibrary.demo.vipslib.com:8090/cdn',
        changeOrigin: true,
        rewrite: (path) => path.replace('/cdn', ''),
      },
    },
  },
  base: './',
});
