import{n as i,r}from"./element-variables-32ad61ab.js";import"./main-a563c162.js";const s=[{label:"数据库名称",prop:"dbname",width:400},{label:"语言",prop:"lan",width:180,align:"center"},{label:"采购状态",prop:"orderType",width:180,align:"center"},{label:"起始年",prop:"startYear",width:200,align:"center"},{label:"最新合同",prop:"filePath",width:400},{label:"最新合同金额（元）",prop:"amount",minWidth:155}];const p={__name:"index",setup(l){const a=r(""),e=r([{dbname:"jack"}]),t=r(0),n=r({pageIndex:1,pageSize:10});return{__sfc:!0,keyword:a,tableData:e,total:t,pagination:n,onCurrentPageChange:o=>{n.value.pageIndex=o},onPageSizeChange:o=>{n.value.pageSize=o},columns:s}}};var c=function(){var a=this,e=a._self._c,t=a._self._setupProxy;return e("div",[e("d-card",{attrs:{"content-class":"text-blue-500 ","title-class":"justify-between"},scopedSlots:a._u([{key:"title",fn:function(){return[e("div",{staticClass:"flex gap-20px"},[e("el-input",{attrs:{placeholder:"",size:"medium"},model:{value:t.keyword,callback:function(n){t.keyword=n},expression:"keyword"}}),e("d-button",[a._v("查找")])],1),e("div",[e("d-button",{attrs:{type:"primary-light",border:""}},[a._v(" 导出所选 ")]),e("d-button",{attrs:{type:"success-light",border:""}},[a._v(" 导出所有 ")])],1)]},proxy:!0}])},[e("d-table",{attrs:{data:t.tableData,columns:t.columns,selectable:"",pagination:{max:1/0,pageSizes:[10,20,50,100,150,200],total:t.total,pageSize:t.pagination.pageSize,currentPage:t.pagination.pageIndex,onCurrentPageChange:t.onCurrentPageChange,onPageSizeChange:t.onPageSizeChange}}})],1)],1)},d=[],u=i(p,c,d,!1,null,"529282e2",null,null);const h=u.exports;export{h as default};
