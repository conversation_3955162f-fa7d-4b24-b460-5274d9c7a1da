import{n as y,r,F as O,A as de,B as ve,c as v,s as I,G as fe}from"./element-variables-bb5f5d94.js";/* empty css                                                              */import{t as V,h as oe,d as X}from"./helpers-640ed53b.js";import"./main-e30dbd5a.js";const ge=[{label:"机构总览",value:"organization-overview"},{label:"馆藏数量评价",value:"collection-quantity"},{label:"重要收录保障评价",value:"key-collection"},{label:"成果数据保障评价",value:"achievement-data"},{label:"文献行为评价",value:"literature-behavior"},{label:"报告",value:"report"}];const me={__name:"card-chart",props:{title:{type:String,required:!0},describe:{type:String},showChart:{type:Boolean,default:!1},showTable:{type:Boolean,default:!1},showExport:{type:Boolean,default:!1},displayType:{type:String,default:"chart"}},emits:["toggle-type","export"],setup(s,{emit:t}){return{__sfc:!0,props:s,emits:t,handleShowChart:()=>{t("toggle-type","chart")},handleShowTable:()=>{t("toggle-type","table")},handleExport:()=>{t("export")}}}};var ye=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",{staticClass:"card-chart"},[a("div",{staticClass:"head flex items-center h-54px text-sm text-[#34395E] px-20px"},[a("div",{staticClass:"font-bold"},[t._v(t._s(t.title))]),a("div",{staticClass:"title-describe ml-10px"},[t._v(t._s(t.describe))])]),t.showChart||t.showTable||t.showExport||t.$slots["operation-left"]?a("div",{staticClass:"operation flex items-center justify-between px-20px h-36px mb-14px"},[a("div",{staticClass:"left"},[t._t("operation-left")],2),a("div",{staticClass:"right"},[a("div",{staticClass:"flex gap-10px"},[t.showChart||t.showTable?a("div",{staticClass:"flex items-center border border-[#D4D7DF] h-30px rounded-15px w-84px bg-[#F2F4F5]"},[t.showChart?a("div",{staticClass:"flex-1 flex items-center justify-center h-28px rounded-14px cursor-pointer hover:bg-white",class:{active:t.displayType==="chart"},on:{click:e.handleShowChart}},[a("d-icon",{staticClass:"text-[#2D3240]",attrs:{name:"el-icon-vip-tubiao"}})],1):t._e(),t.showTable?a("div",{staticClass:"flex-1 flex items-center justify-center h-28px rounded-14px cursor-pointer hover:bg-white",class:{active:t.displayType==="table"},on:{click:e.handleShowTable}},[a("d-icon",{staticClass:"text-[#2D3240]",attrs:{name:"el-icon-vip-biaoge1"}})],1):t._e()]):t._e(),t.showExport?a("div",{staticClass:"flex-1 flex items-center justify-center h-30px w-30px rounded-15px cursor-pointer hover:bg-white bg-[#F2F4F5]"},[a("d-icon",{staticClass:"text-[#6777EF]",attrs:{name:"el-icon-vip-fenxiang3"},on:{click:e.handleExport}})],1):t._e()])])]):t._e(),a("div",{staticClass:"w-full px-20px pb-20px"},[t._t("default")],2)])},he=[],xe=y(me,ye,he,!1,null,"8472e613",null,null);const T=xe.exports;const be={__name:"organization-info",setup(s){const t=r([]),a=r(!1),e=async()=>{var p,i;a.value=!0;const n=await O.organizationApi.getOrganizationInfo(),o=((i=(p=n==null?void 0:n.list)==null?void 0:p[0])==null?void 0:i.detail)||"";t.value=o.split("<br/>"),a.value=!1};return(async()=>await e())(),{__sfc:!0,organizationInfo:t,isOrganizationInfoLoading:a,getOrganizationInfo:e}}};var De=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isOrganizationInfoLoading,expression:"isOrganizationInfoLoading"}],staticClass:"text-sm text-[#3C4B5D]"},t._l(e.organizationInfo,function(n,o){return a("div",{key:o,staticClass:"indent-2em"},[t._v(" "+t._s(n)+" ")])}),0)},Ce=[],we=y(be,De,Ce,!1,null,"e23f28b2",null,null);const ke=we.exports;const Te={__name:"card-indicator",props:{data:{type:Object,required:!0}},setup(s){return{__sfc:!0,props:s,toThousandsSeparator:V}}};var Ae=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",{staticClass:"h-80px bg-[#F7F8FE] flex flex-col justify-center items-center rounded-3px"},[a("div",{staticClass:"indicator-value flex justify-center items-center"},[a("div",{staticClass:"text-[#34395E] font-bold text-2xl"},[t._v(t._s(e.toThousandsSeparator(t.data.indicator_value)))]),t.data.indicator_ratio?a("div",{staticClass:"text-[#404040] text-base ml-2px"},[t._v(" /"+t._s(t.data.indicator_ratio)+"% ")]):t._e()]),a("div",{staticClass:"w-full indicator-name text-[#404040] text-sm truncate flex items-center justify-center"},[a("span",[t._v(t._s(t.data.indicator_label)+"（"+t._s(t.data.unit)+"）")]),t.data.remark?a("el-tooltip",{scopedSlots:t._u([{key:"content",fn:function(){return[a("div",[t._v(t._s(t.data.remark))])]},proxy:!0}],null,!1,3667637481)},[a("d-icon",{staticClass:"text-primary",attrs:{name:"el-icon-vip-icon-tishi41"}})],1):t._e()],1)])},$e=[],Se=y(Te,Ae,$e,!1,null,"6753a59c",null,null);const Oe=Se.exports,Le=()=>{const s=r([]),t=r(!1),a=async()=>{try{t.value=!0;const d=await O.organizationApi.getCollectionCount();s.value=d.list,t.value=!1}catch(d){console.log(d)}},e=r([]),n=r(!1),o=async()=>{try{n.value=!0;const d=await O.organizationApi.getKeyCollection();e.value=d.list,n.value=!1}catch(d){console.log(d)}},p=r([]),i=r(!1),c=async()=>{try{i.value=!0;const d=await O.organizationApi.getAchievement();p.value=d.list,i.value=!1}catch(d){console.log(d)}},l=r([]),u=r(!1);return{collectionCountList:s,isCollectionCountListLoading:t,getCollectionCountList:a,keyCollectionList:e,isKeyCollectionListLoading:n,getKeyCollectionList:o,achievementList:p,isAchievementListLoading:i,getAchievementList:c,literatureBehaviorList:l,isLiteratureBehaviorListLoading:u,getLiteratureBehaviorList:async()=>{try{u.value=!0;const d=await O.organizationApi.getLiteratureBehavior();l.value=d.list,u.value=!1}catch(d){console.log(d)}}}};const Fe={__name:"index",setup(s){const{collectionCountList:t,isCollectionCountListLoading:a,getCollectionCountList:e,keyCollectionList:n,isKeyCollectionListLoading:o,getKeyCollectionList:p,achievementList:i,isAchievementListLoading:c,getAchievementList:l,literatureBehaviorList:u,isLiteratureBehaviorListLoading:_,getLiteratureBehaviorList:d}=Le();return(async()=>(e(),p(),l(),d()))(),{__sfc:!0,collectionCountList:t,isCollectionCountListLoading:a,getCollectionCountList:e,keyCollectionList:n,isKeyCollectionListLoading:o,getKeyCollectionList:p,achievementList:i,isAchievementListLoading:c,getAchievementList:l,literatureBehaviorList:u,isLiteratureBehaviorListLoading:_,getLiteratureBehaviorList:d,CardChart:T,CardIndicator:Oe}}};var Ye=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a("div",{staticClass:"organization-info"},[a(e.CardChart,{attrs:{title:"馆藏数量保障概况"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isCollectionCountListLoading,expression:"isCollectionCountListLoading"}],staticClass:"flex gap-5 flex-wrap"},t._l(e.collectionCountList,function(n,o){return a(e.CardIndicator,{key:o,staticClass:"responsive-width",attrs:{data:n}})}),1)]),a(e.CardChart,{attrs:{title:"重要收录保障概况"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isKeyCollectionListLoading,expression:"isKeyCollectionListLoading"}],staticClass:"flex gap-5 flex-wrap"},t._l(e.keyCollectionList,function(n,o){return a(e.CardIndicator,{key:o,staticClass:"responsive-width",attrs:{data:n}})}),1)]),a(e.CardChart,{attrs:{title:"成果保障概况"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isAchievementListLoading,expression:"isAchievementListLoading"}],staticClass:"flex gap-5 flex-wrap"},t._l(e.achievementList,function(n,o){return a(e.CardIndicator,{key:o,staticClass:"responsive-width",attrs:{data:n}})}),1)]),a(e.CardChart,{attrs:{title:"文献行为概况"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLiteratureBehaviorListLoading,expression:"isLiteratureBehaviorListLoading"}],staticClass:"flex gap-5 flex-wrap"},t._l(e.literatureBehaviorList,function(n,o){return a(e.CardIndicator,{key:o,staticClass:"responsive-width",attrs:{data:n}})}),1)])],1)])},Re=[],Pe=y(Fe,Ye,Re,!1,null,"cff1b2f7",null,null);const je=Pe.exports;function z(s,t={}){const{colors:a=["#6777EF","#B566FF","#36BDFF"],customConfig:e={}}=t,{xAxisData:n,lineData:o}=s,p=o.map((c,l)=>({smooth:!0,name:c.name,type:"line",data:c.data,color:a[l],showSymbol:!1,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:oe(a[l],.3)},{offset:1,color:oe(a[l],0)}]}}})),i={tooltip:{trigger:"axis"},legend:{data:o.map(c=>c.name),icon:"circle",bottom:0,itemGap:40,itemWidth:8,itemHeight:8},grid:{top:20,bottom:20,left:0,right:0,containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:n,axisLine:{lineStyle:{color:"#D7DADB",width:2}},axisTick:{show:!0,alignWithLabel:!0,inside:!0},axisLabel:{textStyle:{color:"#909399",fontSize:12}}},yAxis:{type:"value",axisLabel:{textStyle:{color:"#909399",fontSize:12}},splitLine:{lineStyle:{color:"#D7DADB",type:"dashed"}}},series:p};return X(i,e)}function W(s,t={}){const{colors:a=["#6777EF","#36BDFF","#F2A940","#62AC00"],horizontal:e=!1,customConfig:n={}}=t,{xAxisData:o,barData:p}=s,i=p.map((l,u)=>({name:l.name,type:"bar",data:l.data,color:a[u%a.length],barMaxWidth:60,itemStyle:{borderRadius:[5,5,5,5]},label:{show:!1,position:e?"right":"top",formatter:_=>V(_.value)},emphasis:{focus:"series"}})),c={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:p.map(l=>l.name),icon:"circle",itemWidth:8,itemHeight:8,bottom:0},grid:{top:10,bottom:30,left:0,right:0,containLabel:!0},xAxis:e?{type:"value",splitLine:{show:!0,lineStyle:{color:"#D7DADB",type:"dashed"}},axisTick:{show:!1},axisLabel:{formatter:l=>V(l),color:"#909399"}}:{type:"category",data:o,axisLine:{show:!0,lineStyle:{color:"#D7DADB",width:2}},axisTick:{show:!1},axisLabel:{rotate:o.length>10?45:0,textStyle:{color:"#909399",fontSize:12}}},yAxis:e?{type:"category",data:o,axisLine:{show:!0,lineStyle:{color:"#D7DADB",width:2}},axisTick:{show:!1},axisLabel:{color:"#909399"}}:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{formatter:l=>V(l),textStyle:{color:"#909399",fontSize:12}},splitLine:{show:!0,lineStyle:{color:"#D7DADB",type:"dashed"}}},series:i};return X(c,n)}function H(s,t={}){const{colors:a=["#6777EF","#36BDFF","#F2A940","#62AC00"],showTotal:e=!0,customConfig:n={}}=t,{pieData:o}=s,p=o.reduce((c,l)=>c+(l.value||0),0),i={tooltip:{trigger:"item",formatter:c=>`${c.data.name}: ${V(c.data.value)}`},legend:{data:o.map(c=>c.name),icon:"circle",itemWidth:8,itemHeight:8,bottom:0,itemGap:10,padding:[20,100],formatter:c=>{var l;return(l=o.find(u=>u.name===c))!=null&&l.value,`${c}`}},grid:{top:20,left:"center",containLabel:!0},series:[{type:"pie",radius:["40%","70%"],center:["50%","40%"],avoidLabelOverlap:!1,label:{show:!0,position:"outside",formatter:c=>`${c.name}: ${c.percent.toFixed(2)}%`},emphasis:{label:{show:!0,fontSize:14,fontWeight:"bold"}},labelLine:{show:!0,length:15,length2:10,smooth:!0},data:o,color:a}]};return e&&(i.title=[{text:"总计",x:"center",top:"32%",textStyle:{color:"#6C757D",fontSize:14}},{text:"............",x:"center",top:"36%",textStyle:{color:"#e5e5e5",fontSize:12}},{text:p,x:"center",top:"41%",textStyle:{fontSize:28,color:"#34395E",foontWeight:"800"}}]),X(i,n)}function Me(s,t={}){const{colors:a=["#6777EF","#36BDFF","#F2A940","#62AC00"],showSymbol:e=!0,symbolSize:n=10,minSymbolSize:o=6,maxSymbolSize:p=50,customConfig:i={}}=t,{scatterData:c}=s,l=[];c.forEach(k=>{k.data.forEach(B=>{Array.isArray(B)&&B.length>=2&&l.push(B[1])})});const u=Math.min(...l),d=Math.max(...l)-u,f=p-o,g=k=>d===0?(o+p)/2:o+f*(k-u)/d,m=c.map((k,B)=>({name:k.name,type:"scatter",data:k.data,symbolSize:d===0?n:function(E){return g(E[1])},symbol:e?"circle":"none",itemStyle:{borderColor:a[B%a.length],borderWidth:1,color:oe(a[B%a.length],.5)},emphasis:{focus:"series",itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.3)",borderWidth:2}}})),h='<span style="display:inline-block;margin-right:4px;border-radius:10px;width:8px;height:8px;background-color:rgba(35,85,236,0.2);border-width:1px;border-style:solid;border-color:rgba(35,85,236,0.5);" ></span>',x={tooltip:{trigger:"item",formatter:k=>(console.log(k),`${h}<span style="color:#909399;vertical-align:middle;">数据库</span>
                <span style="font-size:16px;font-weight:bold;color:#333;vertical-align:middle;">${k.seriesName}</span>
                <br/>
                <div style="width:100%;height:1px;background:#EBEEF5;margin-top:12px;margin-bottom:12px;"></div>
                ${h}<span style="color:#909399;vertical-align:middle;">不重复文献</span>
                <span style="font-size:16px;font-weight:bold;color:#333333;vertical-align:middle;">${V(k.value[1])}</span>`)},legend:{data:c.map(k=>k.name),icon:"circle",itemWidth:10,itemHeight:10,bottom:0},grid:{top:20,bottom:20,left:0,right:0,containLabel:!0},xAxis:{type:"category",scale:!0,axisLine:{show:!0,lineStyle:{color:"#D7DADB",width:2}},axisLabel:{textStyle:{color:"#909399"}},axisTick:{show:!0,inside:!0},splitLine:{lineStyle:{type:"dashed"}}},yAxis:{type:"value",scale:!0,axisLine:{show:!1},axisTick:{show:!1},axisLabel:{formatter:k=>V(k),textStyle:{color:"#909399",fontSize:12}},splitLine:{lineStyle:{type:"dashed"}}},series:m};return X(x,i)}function Be(s,t={}){const{colors:a=["#6777EF","#36BDFF","#F2A940","#62AC00"],sort:e="descending",gap:n=2,customConfig:o={}}=t,{funnelData:p}=s,i={tooltip:{trigger:"item",formatter:c=>`${c.data.name}: ${c.data.value}`},legend:{data:p.map(c=>c.name),icon:"circle",itemWidth:8,itemHeight:8,bottom:0},series:[{name:"漏斗图",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:Math.max(...p.map(c=>c.value))*1.1,minSize:"0%",maxSize:"100%",sort:e,gap:n,label:{show:!0,position:"inside",formatter:c=>`${c.data.name}: ${c.value}`,fontSize:14,color:"#fff"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{fontSize:16,fontWeight:"bold"}},data:p,color:a}]};return X(i,o)}function pe(s,t={}){const{colors:a=["#6777EF"],waveAnimation:e=!0,amplitude:n=20,waveSpeed:o=3e3,customConfig:p={}}=t,{value:i,text:c}=s,l=f=>typeof f!="number"?0:Math.max(0,Math.min(1,f)),u=Array.isArray(i)?i.map(l):[l(i)],_=Array.isArray(a)?a:[a],d={series:[{type:"liquidFill",radius:"80%",center:["50%","50%"],data:u.map((f,g)=>({value:f,itemStyle:{color:_[g%_.length],opacity:.8-g*.1},waveAnimation:e})),amplitude:n,waveAnimation:e,animationDuration:500,animationDurationUpdate:1e3,period:o,waveLength:"80%",waveHeight:30,wavesNumber:3,outline:{show:!0,borderDistance:5,itemStyle:{borderColor:_[0],borderWidth:2}},backgroundStyle:{color:"rgba(255, 255, 255, 0.1)"},label:{show:!0,position:["50%","50%"],formatter:c||function(f){return(f.value*100).toFixed(2)+"%"},fontSize:30,fontWeight:"bold",color:_[0]},animation:!0}]};return X(d,p)}function q(s,t={}){const{colors:a=["#6777EF","#00ABFF","#B566FF","#2067FF","#D8A500","#3877FF","#00BEBE","#636BFF"],showLabel:e=!0,levels:n=1,roam:o=!1,customConfig:p={}}=t,{treeData:i}=s,c={tooltip:{show:!0},series:[{type:"treemap",data:i.map((l,u)=>({...l,itemStyle:{color:a[u%a.length]}})),visibleMin:300,label:{show:e,formatter:"{b}: {c}",position:"inside",fontSize:14,color:"#fff"},emphasis:{label:{show:!0,fontSize:16,fontWeight:"bold"},itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}},roam:o,animationDuration:1e3,animationEasing:"quinticInOut"}]};return X(c,p)}function ce(s,t={}){const{colors:a=["#6777EF","#36BDFF","#F2A940","#62AC00"],sizeRange:e=[12,60],rotationRange:n=[-90,90],shape:o="pentagon",customConfig:p={}}=t,{wordcloudData:i}=s,c={series:[{type:"wordCloud",shape:o,left:"center",top:"center",width:"100%",height:"100%",textStyle:{fontWeight:"normal",color:function(){return a[Math.floor(Math.random()*a.length)]}},sizeRange:e,rotationRange:n,rotationStep:45,gridSize:8,drawOutOfBound:!1,layoutAnimation:!0,data:i.map(l=>({name:l.name,value:l.value,emphasis:{textStyle:{fontWeight:"bold",opacity:1}}}))}],tooltip:{show:!0,trigger:"item",formatter:function(l){return`${l.name}: ${l.value}`}},animation:!0,animationDuration:1e3,animationEasing:"cubicOut",animationDelay:function(l){return l*100}};return X(c,p)}const Ne=()=>{const s=r([]),t=r(!1);return{databaseData:s,isDatabaseDataLoading:t,getDatabaseData:async e=>{t.value=!0;try{const n={startTime:e[0],endTime:e[1]},o=await O.organizationApi.getDatabase(n);s.value=o.list}catch(n){console.log(n)}finally{t.value=!1}}}},ze=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"数据库数量（个）",prop:"dbcount",sortable:!0},{label:"数据库经费（万元）",prop:"dbamt",sortable:!0}],Je=[{label:"数据库",prop:"dbname",sortable:!0},{label:"文献类型",prop:"docname",sortable:!0},{label:"语言",prop:"language",sortable:!0,align:"center"},{label:"文献数量",prop:"indicator_total",sortable:!0},{label:"不重复文献",prop:"indicator_value",sortable:!0},{label:"不重复率",prop:"indicator_ratio",sortable:!0},{label:"重复文献",prop:"indicator_sec_value",sortable:!0},{label:"重复率",prop:"indicator_sec_ratio",sortable:!0}],Ee=[{label:"数据库",prop:"dbname",sortable:!0},{label:"文献类型",prop:"docname",sortable:!0},{label:"语言",prop:"language",sortable:!0,align:"center"},{label:"文献数量",prop:"indicator_total",sortable:!0},{label:"非OA文献",prop:"indicator_value",sortable:!0},{label:"非OA率",prop:"indicator_ratio",sortable:!0},{label:"OA文献",prop:"indicator_sec_value",sortable:!0},{label:"OA率",prop:"indicator_sec_ratio",sortable:!0}];var _e={exports:{}};(function(s,t){(function(a,e){s.exports=e()})(de,function(){var a=1e3,e=6e4,n=36e5,o="millisecond",p="second",i="minute",c="hour",l="day",u="week",_="month",d="quarter",f="year",g="date",m="Invalid Date",h=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,x=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,k={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(A){var C=["th","st","nd","rd"],b=A%100;return"["+A+(C[(b-20)%10]||C[b]||C[0])+"]"}},B=function(A,C,b){var w=String(A);return!w||w.length>=C?A:""+Array(C+1-w.length).join(b)+A},E={s:B,z:function(A){var C=-A.utcOffset(),b=Math.abs(C),w=Math.floor(b/60),D=b%60;return(C<=0?"+":"-")+B(w,2,"0")+":"+B(D,2,"0")},m:function A(C,b){if(C.date()<b.date())return-A(b,C);var w=12*(b.year()-C.year())+(b.month()-C.month()),D=C.clone().add(w,_),$=b-D<0,S=C.clone().add(w+($?-1:1),_);return+(-(w+(b-D)/($?D-S:S-D))||0)},a:function(A){return A<0?Math.ceil(A)||0:Math.floor(A)},p:function(A){return{M:_,y:f,w:u,d:l,D:g,h:c,m:i,s:p,ms:o,Q:d}[A]||String(A||"").toLowerCase().replace(/s$/,"")},u:function(A){return A===void 0}},ee="en",Q={};Q[ee]=k;var ie="$isDayjsObject",le=function(A){return A instanceof se||!(!A||!A[ie])},ne=function A(C,b,w){var D;if(!C)return ee;if(typeof C=="string"){var $=C.toLowerCase();Q[$]&&(D=$),b&&(Q[$]=b,D=$);var S=C.split("-");if(!D&&S.length>1)return A(S[0])}else{var F=C.name;Q[F]=C,D=F}return!w&&D&&(ee=D),D||!w&&ee},P=function(A,C){if(le(A))return A.clone();var b=typeof C=="object"?C:{};return b.date=A,b.args=arguments,new se(b)},L=E;L.l=ne,L.i=le,L.w=function(A,C){return P(A,{locale:C.$L,utc:C.$u,x:C.$x,$offset:C.$offset})};var se=function(){function A(b){this.$L=ne(b.locale,null,!0),this.parse(b),this.$x=this.$x||b.x||{},this[ie]=!0}var C=A.prototype;return C.parse=function(b){this.$d=function(w){var D=w.date,$=w.utc;if(D===null)return new Date(NaN);if(L.u(D))return new Date;if(D instanceof Date)return new Date(D);if(typeof D=="string"&&!/Z$/i.test(D)){var S=D.match(h);if(S){var F=S[2]-1||0,Y=(S[7]||"0").substring(0,3);return $?new Date(Date.UTC(S[1],F,S[3]||1,S[4]||0,S[5]||0,S[6]||0,Y)):new Date(S[1],F,S[3]||1,S[4]||0,S[5]||0,S[6]||0,Y)}}return new Date(D)}(b),this.init()},C.init=function(){var b=this.$d;this.$y=b.getFullYear(),this.$M=b.getMonth(),this.$D=b.getDate(),this.$W=b.getDay(),this.$H=b.getHours(),this.$m=b.getMinutes(),this.$s=b.getSeconds(),this.$ms=b.getMilliseconds()},C.$utils=function(){return L},C.isValid=function(){return this.$d.toString()!==m},C.isSame=function(b,w){var D=P(b);return this.startOf(w)<=D&&D<=this.endOf(w)},C.isAfter=function(b,w){return P(b)<this.startOf(w)},C.isBefore=function(b,w){return this.endOf(w)<P(b)},C.$g=function(b,w,D){return L.u(b)?this[w]:this.set(D,b)},C.unix=function(){return Math.floor(this.valueOf()/1e3)},C.valueOf=function(){return this.$d.getTime()},C.startOf=function(b,w){var D=this,$=!!L.u(w)||w,S=L.p(b),F=function(Z,N){var K=L.w(D.$u?Date.UTC(D.$y,N,Z):new Date(D.$y,N,Z),D);return $?K:K.endOf(l)},Y=function(Z,N){return L.w(D.toDate()[Z].apply(D.toDate("s"),($?[0,0,0,0]:[23,59,59,999]).slice(N)),D)},j=this.$W,M=this.$M,J=this.$D,U="set"+(this.$u?"UTC":"");switch(S){case f:return $?F(1,0):F(31,11);case _:return $?F(1,M):F(0,M+1);case u:var G=this.$locale().weekStart||0,te=(j<G?j+7:j)-G;return F($?J-te:J+(6-te),M);case l:case g:return Y(U+"Hours",0);case c:return Y(U+"Minutes",1);case i:return Y(U+"Seconds",2);case p:return Y(U+"Milliseconds",3);default:return this.clone()}},C.endOf=function(b){return this.startOf(b,!1)},C.$set=function(b,w){var D,$=L.p(b),S="set"+(this.$u?"UTC":""),F=(D={},D[l]=S+"Date",D[g]=S+"Date",D[_]=S+"Month",D[f]=S+"FullYear",D[c]=S+"Hours",D[i]=S+"Minutes",D[p]=S+"Seconds",D[o]=S+"Milliseconds",D)[$],Y=$===l?this.$D+(w-this.$W):w;if($===_||$===f){var j=this.clone().set(g,1);j.$d[F](Y),j.init(),this.$d=j.set(g,Math.min(this.$D,j.daysInMonth())).$d}else F&&this.$d[F](Y);return this.init(),this},C.set=function(b,w){return this.clone().$set(b,w)},C.get=function(b){return this[L.p(b)]()},C.add=function(b,w){var D,$=this;b=Number(b);var S=L.p(w),F=function(M){var J=P($);return L.w(J.date(J.date()+Math.round(M*b)),$)};if(S===_)return this.set(_,this.$M+b);if(S===f)return this.set(f,this.$y+b);if(S===l)return F(1);if(S===u)return F(7);var Y=(D={},D[i]=e,D[c]=n,D[p]=a,D)[S]||1,j=this.$d.getTime()+b*Y;return L.w(j,this)},C.subtract=function(b,w){return this.add(-1*b,w)},C.format=function(b){var w=this,D=this.$locale();if(!this.isValid())return D.invalidDate||m;var $=b||"YYYY-MM-DDTHH:mm:ssZ",S=L.z(this),F=this.$H,Y=this.$m,j=this.$M,M=D.weekdays,J=D.months,U=D.meridiem,G=function(N,K,ae,re){return N&&(N[K]||N(w,$))||ae[K].slice(0,re)},te=function(N){return L.s(F%12||12,N,"0")},Z=U||function(N,K,ae){var re=N<12?"AM":"PM";return ae?re.toLowerCase():re};return $.replace(x,function(N,K){return K||function(ae){switch(ae){case"YY":return String(w.$y).slice(-2);case"YYYY":return L.s(w.$y,4,"0");case"M":return j+1;case"MM":return L.s(j+1,2,"0");case"MMM":return G(D.monthsShort,j,J,3);case"MMMM":return G(J,j);case"D":return w.$D;case"DD":return L.s(w.$D,2,"0");case"d":return String(w.$W);case"dd":return G(D.weekdaysMin,w.$W,M,2);case"ddd":return G(D.weekdaysShort,w.$W,M,3);case"dddd":return M[w.$W];case"H":return String(F);case"HH":return L.s(F,2,"0");case"h":return te(1);case"hh":return te(2);case"a":return Z(F,Y,!0);case"A":return Z(F,Y,!1);case"m":return String(Y);case"mm":return L.s(Y,2,"0");case"s":return String(w.$s);case"ss":return L.s(w.$s,2,"0");case"SSS":return L.s(w.$ms,3,"0");case"Z":return S}return null}(N)||S.replace(":","")})},C.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},C.diff=function(b,w,D){var $,S=this,F=L.p(w),Y=P(b),j=(Y.utcOffset()-this.utcOffset())*e,M=this-Y,J=function(){return L.m(S,Y)};switch(F){case f:$=J()/12;break;case _:$=J();break;case d:$=J()/3;break;case u:$=(M-j)/6048e5;break;case l:$=(M-j)/864e5;break;case c:$=M/n;break;case i:$=M/e;break;case p:$=M/a;break;default:$=M}return D?$:L.a($)},C.daysInMonth=function(){return this.endOf(_).$D},C.$locale=function(){return Q[this.$L]},C.locale=function(b,w){if(!b)return this.$L;var D=this.clone(),$=ne(b,w,!0);return $&&(D.$L=$),D},C.clone=function(){return L.w(this.$d,this)},C.toDate=function(){return new Date(this.valueOf())},C.toJSON=function(){return this.isValid()?this.toISOString():null},C.toISOString=function(){return this.$d.toISOString()},C.toString=function(){return this.$d.toUTCString()},A}(),ue=se.prototype;return P.prototype=ue,[["$ms",o],["$s",p],["$m",i],["$H",c],["$W",l],["$M",_],["$y",f],["$D",g]].forEach(function(A){ue[A[1]]=function(C){return this.$g(C,A[0],A[1])}}),P.extend=function(A,C){return A.$i||(A(C,se,P),A.$i=!0),P},P.locale=ne,P.isDayjs=le,P.unix=function(A){return P(1e3*A)},P.en=Q[ee],P.Ls=Q,P.p={},P})})(_e);var Ie=_e.exports;const R=ve(Ie);const We={__name:"database",setup(s){const t=R(),a=R().subtract(5,"year"),e=r([a.format("YYYY-MM-DD"),t.format("YYYY-MM-DD")]),{databaseData:n,isDatabaseDataLoading:o,getDatabaseData:p}=Ne(),i=r("chart"),c=m=>{i.value=m},l=r({shortcuts:[{text:"近3年",onClick(m){const h=new Date,x=new Date;x.setFullYear(x.getFullYear()-3),m.$emit("pick",[x,h])}},{text:"近5年",onClick(m){const h=new Date,x=new Date;x.setFullYear(x.getFullYear()-5),m.$emit("pick",[x,h])}}]}),u=m=>{p(e.value)},_=v(()=>[{name:"数据库数量（个）",data:n.value.map(m=>{const h=Number(m.dbcount);return isNaN(h)?0:h})},{name:"数据库经费（万元）",data:n.value.map(m=>{const h=Number(m.dbamt);return isNaN(h)?0:h})}]),d=v(()=>n.value.map(m=>m.year)),f=v(()=>{const m={xAxisData:d.value,lineData:_.value};return z(m,{customConfig:{}})}),g=v(()=>n.value);return(async()=>p(e.value))(),{__sfc:!0,now:t,fiveYearsAgo:a,timeRange:e,databaseData:n,isDatabaseDataLoading:o,getDatabaseData:p,displayType:i,handleToggleType:c,pickerOptions:l,handleDateChange:u,chartData:_,xAxisData:d,lineChartData:f,databaseTableData:g,CardChart:T,databaseColumns:ze}}};var Ve=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"数据库",describe:"馆藏数据库数量以及经费","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.handleDateChange},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseDataLoading,expression:"isDatabaseDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseDataLoading,expression:"isDatabaseDataLoading"}],attrs:{data:e.databaseTableData,columns:e.databaseColumns,"null-text":"-"},scopedSlots:t._u([{key:"dbamt-th",fn:function(n){return[a("span",[t._v(t._s(n.column.label))]),a("el-tooltip",{scopedSlots:t._u([{key:"content",fn:function(){return[a("div",[t._v("数据库的合同金额")])]},proxy:!0}],null,!0)},[a("d-icon",{staticClass:"text-primary align-middle",attrs:{name:"el-icon-vip-icon-tishi41"}})],1)]}}],null,!1,2235703315)}):t._e()],1)},He=[],Ke=y(We,Ve,He,!1,null,"8f9fcc85",null,null);const Xe=Ke.exports,qe=()=>{const s=r([]),t=r(!1);return{procurementData:s,isProcurementDataLoading:t,getProcurementData:async({year:e,language:n})=>{t.value=!0;try{const o=await O.organizationApi.getProcurement({year:e,language:n});s.value=o.list}catch(o){console.log(o)}finally{t.value=!1}}}};const Qe={__name:"procurement",setup(s){const{procurementData:t,isProcurementDataLoading:a,getProcurementData:e}=qe(),n=I.useSelect(),o=new Date().getFullYear().toString(),p=r(o),i=r(""),c=g=>{f()},l=g=>{f()},u=v(()=>t.value),_=v(()=>u.value.map(g=>({name:g.name,value:g.value}))),d=v(()=>H({pieData:_.value},{showTotal:!1,customConfig:{series:[{radius:["0%","70%"],emphasis:{label:{show:!1}}}]}})),f=()=>{e(Object.fromEntries(Object.entries({year:p.value,language:i.value}).filter(([,g])=>g)))};return(async()=>f())(),{__sfc:!0,procurementData:t,isProcurementDataLoading:a,getProcurementData:e,selectStore:n,currentYear:o,year:p,language:i,handleYearChange:c,handleLanguageChange:l,sourceData:u,pieData:_,procurementStauts:d,getData:f,CardChart:T}}};var Ge=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"数据库采购状态分布",describe:"馆藏数据库采购状态的分布情况","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.handleYearChange},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.handleLanguageChange},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isProcurementDataLoading,expression:"isProcurementDataLoading"}],staticClass:"h-300px",attrs:{option:e.procurementStauts}})],1)},Ze=[],Ue=y(Qe,Ge,Ze,!1,null,"605708fc",null,null);const et=Ue.exports,tt=()=>{const s=r([]),t=r(!1);return{languageData:s,isLanguageDataLoading:t,getLanguageData:async e=>{t.value=!0;try{const n=await O.organizationApi.getLanguage({year:e});s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const at={__name:"language",setup(s){const{languageData:t,isLanguageDataLoading:a,getLanguageData:e}=tt(),n=new Date().getFullYear().toString(),o=r(n),p=()=>{e(o.value)},i=v(()=>t.value),c=v(()=>i.value.map(u=>({name:u.name,value:u.value}))),l=v(()=>H({pieData:c.value},{customConfig:{}}));return(async()=>e(o.value))(),{__sfc:!0,languageData:t,isLanguageDataLoading:a,getLanguageData:e,currentYear:n,year:o,handleYearChange:p,sourceData:i,pieData:c,procurementStauts:l,CardChart:T}}};var nt=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"数据库语言类型分布",describe:"馆藏数据库语言的分布情况","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("el-date-picker",{attrs:{type:"year","value-format":"yyyy",placeholder:"请选择年份",clearable:!1},on:{change:e.handleYearChange},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})]},proxy:!0}])},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.procurementStauts}})],1)},st=[],rt=y(at,nt,st,!1,null,"7898f968",null,null);const lt=rt.exports,ot=()=>{const s=r([]),t=r(!1);return{literatureData:s,isLiteratureDataLoading:t,getLiteratureData:async e=>{t.value=!0;try{const n=await O.organizationApi.getLiterature(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const ct={__name:"literature",setup(s){const t=I.useSelect(),{literatureData:a,isLiteratureDataLoading:e,getLiteratureData:n}=ot(),o=new Date().getFullYear().toString(),p=r(o),i=r(""),c=r(""),l=()=>{n(Object.fromEntries(Object.entries({year:p.value,language:c.value,orderType:i.value}).filter(([,f])=>f)))};(async()=>l())();const u=v(()=>a.value.map(f=>f.name)),_=v(()=>[{name:"数据库数量",data:a.value.map(f=>f.value)}]),d=v(()=>W({xAxisData:u.value,barData:_.value},{customConfig:{}}));return{__sfc:!0,selectStore:t,literatureData:a,isLiteratureDataLoading:e,getLiteratureData:n,currentYear:o,year:p,orderType:i,language:c,getData:l,categories:u,seriesData:_,verticalBarOption:d,CardChart:T}}};var it=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"数据库文献类型分布",describe:"馆藏数据库文献类型的分布情况","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择采购状态",clearable:""},on:{change:e.getData},model:{value:e.orderType,callback:function(n){e.orderType=n},expression:"orderType"}},t._l(e.selectStore.selects.order_type,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLiteratureDataLoading,expression:"isLiteratureDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)},ut=[],pt=y(ct,it,ut,!1,null,"2bf8587d",null,null);const _t=pt.exports,dt=()=>{const s=r([]),t=r(!1);return{subjectData:s,isSubjectDataLoading:t,getSubjectData:async e=>{t.value=!0;try{const n=await O.organizationApi.getSubject(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const vt={__name:"subject",setup(s){const t=I.useSelect(),{subjectData:a,isSubjectDataLoading:e,getSubjectData:n}=dt(),o=new Date().getFullYear().toString(),p=r(o),i=r(""),c=r(""),l=()=>{n(Object.fromEntries(Object.entries({year:p.value,language:c.value,orderType:i.value}).filter(([,f])=>f)))};(async()=>l())();const u=v(()=>a.value.map(f=>f.name)),_=v(()=>[{name:"数据库数量",data:a.value.map(f=>f.value)}]),d=v(()=>W({xAxisData:u.value,barData:_.value},{customConfig:{}}));return{__sfc:!0,selectStore:t,subjectData:a,isSubjectDataLoading:e,getSubjectData:n,currentYear:o,year:p,orderType:i,language:c,getData:l,categories:u,seriesData:_,verticalBarOption:d,CardChart:T}}};var ft=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"数据库学科类型分布",describe:"馆藏数据库教育部学科分类（门类级）的分布情况","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择采购状态",clearable:""},on:{change:e.getData},model:{value:e.orderType,callback:function(n){e.orderType=n},expression:"orderType"}},t._l(e.selectStore.selects.order_type,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)},gt=[],mt=y(vt,ft,gt,!1,null,"b9d4cc16",null,null);const yt=mt.exports,ht=()=>{const s=r([]),t=r(!1);return{nonRepetitiveData:s,isNonRepetitiveDataLoading:t,getNonRepetitiveData:async e=>{t.value=!0;try{const n=await O.organizationApi.getNonRepetitive(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const xt={__name:"non-repetitive",setup(s){const t=I.useSelect(),{nonRepetitiveData:a,isNonRepetitiveDataLoading:e,getNonRepetitiveData:n}=ht(),o=new Date().getFullYear().toString(),p=r(o),i=r("2"),c=r(""),l=r("chart"),u=g=>{l.value=g},_=()=>{n(Object.fromEntries(Object.entries({year:p.value,language:c.value,docType:i.value}).filter(([,g])=>g)))};(async()=>_())();const d=v(()=>[{name:"数据库",data:a.value.map(g=>[g.dbname,Number(g.indicator_value)])}]),f=v(()=>Me({scatterData:d.value},{colors:["#6777EF","#F2A940"],symbolSize:12,customConfig:{legend:{show:!1}}}));return{__sfc:!0,selectStore:t,nonRepetitiveData:a,isNonRepetitiveDataLoading:e,getNonRepetitiveData:n,currentYear:o,year:p,docType:i,language:c,displayType:l,handleToggleType:u,getData:_,scatterData:d,scatterOption:f,CardChart:T,nonRepetitiveColumns:Je}}};var bt=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"数据库不重复文献分析",describe:"分析馆藏不重复文献的情况，即在馆藏文献中，仅出现过1次。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择文献类型"},on:{change:e.getData},model:{value:e.docType,callback:function(n){e.docType=n},expression:"docType"}},t._l([{label:"期刊",value:"2"},{label:"期刊文献",value:"3"}],function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isNonRepetitiveDataLoading,expression:"isNonRepetitiveDataLoading"}],staticClass:"h-300px",attrs:{option:e.scatterOption}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isNonRepetitiveDataLoading,expression:"isNonRepetitiveDataLoading"}],attrs:{data:e.nonRepetitiveData,columns:e.nonRepetitiveColumns,"null-text":"-"},scopedSlots:t._u([{key:"indicator_ratio",fn:function(n){return[a("span",[t._v(t._s(n.row.indicator_ratio)+"%")])]}},{key:"indicator_sec_ratio",fn:function(n){return[a("span",[t._v(t._s(n.row.indicator_sec_ratio)+"%")])]}}],null,!1,254466313)}):t._e()],1)},Dt=[],Ct=y(xt,bt,Dt,!1,null,"e9f49134",null,null);const wt=Ct.exports,kt=()=>{const s=r([]),t=r(!1);return{oaData:s,isOaDataLoading:t,getOaData:async e=>{t.value=!0;try{const n=await O.organizationApi.getOa(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const Tt={__name:"oa",setup(s){const t=I.useSelect(),{oaData:a,isOaDataLoading:e,getOaData:n}=kt(),o=new Date().getFullYear().toString(),p=r(o),i=r("2"),c=r(""),l=r("chart"),u=h=>{l.value=h},_=()=>{n(Object.fromEntries(Object.entries({year:p.value,language:c.value,docType:i.value}).filter(([,h])=>h)))};(async()=>_())();const d=v(()=>a.value.map(h=>({name:h.dbname,value:Number(h.indicator_value)}))),f=v(()=>q({treeData:d.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),g=r(null);return{__sfc:!0,selectStore:t,oaData:a,isOaDataLoading:e,getOaData:n,currentYear:o,year:p,docType:i,language:c,displayType:l,handleToggleType:u,getData:_,basicTreeData:d,basicTreemapOption:f,clickInfo:g,handleTreemapClick:h=>{h.data&&(g.value={name:h.data.name,value:h.data.value,path:h.treePathInfo.map(x=>x.name).join(" > "),level:h.treePathInfo.length-1,rawData:h.data},console.log("点击了矩形树图节点:",h))},CardChart:T,oaColumns:Ee}}};var At=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"数据库OA文献分析",describe:"统计数据库内的OA文献数量，以数据库实际标记为准。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择文献类型"},on:{change:e.getData},model:{value:e.docType,callback:function(n){e.docType=n},expression:"docType"}},t._l([{label:"期刊",value:"2"},{label:"期刊文献",value:"3"}],function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isOaDataLoading,expression:"isOaDataLoading"}],staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isOaDataLoading,expression:"isOaDataLoading"}],attrs:{data:e.oaData,columns:e.oaColumns,"null-text":"-"},scopedSlots:t._u([{key:"indicator_ratio",fn:function(n){return[a("span",[t._v(t._s(n.row.indicator_ratio)+"%")])]}},{key:"indicator_sec_ratio",fn:function(n){return[a("span",[t._v(t._s(n.row.indicator_sec_ratio)+"%")])]}}],null,!1,254466313)}):t._e()],1)},$t=[],St=y(Tt,At,$t,!1,null,"21331e2e",null,null);const Ot=St.exports;const Lt={__name:"index",setup(s){return{__sfc:!0,Database:Xe,Procurement:et,Language:lt,Literature:_t,Subject:yt,NonRepetitive:wt,Oa:Ot}}};var Ft=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.Database,{staticClass:"mb-20px"}),a("div",{staticClass:"flex gap-20px mb-20px"},[a("div",{staticClass:"flex-1 min-w-300px"},[a(e.Procurement)],1),a("div",{staticClass:"flex-1 min-w-300px"},[a(e.Language)],1)]),a("div",{staticClass:"flex gap-20px mb-20px"},[a("div",{staticClass:"flex-1 min-w-300px"},[a(e.Literature)],1),a("div",{staticClass:"flex-1 min-w-300px"},[a(e.Subject)],1)]),a(e.NonRepetitive,{staticClass:"mb-20px"}),a(e.Oa)],1)},Yt=[],Rt=y(Lt,Ft,Yt,!1,null,"ba4fcab1",null,null);const Pt=Rt.exports,jt=()=>{const s=r([]),t=r(!1);return{journalData:s,isJournalDataLoading:t,getJournalData:async e=>{t.value=!0;try{const n=await fe.getJournal(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}},Mt=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"期刊种数",prop:"totalcate",sortable:!0},{label:"期刊册数",prop:"totalcount",sortable:!0},{label:"中文期刊种数",prop:"zhcate",sortable:!0},{label:"中文期刊册数",prop:"zhcount",sortable:!0},{label:"外文期刊种数",prop:"uncate",sortable:!0},{label:"外文期刊册数",prop:"uncount",sortable:!0}],Bt=[{label:"学科",prop:"domain_name",sortable:!0},{label:"期刊数量（种）",prop:"catecount",sortable:!0},{label:"期刊数量（册）",prop:"bookcount",sortable:!0}],Nt=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"期刊种数",prop:"totalcate",sortable:!0},{label:"现刊种数",prop:"xkcate",sortable:!0},{label:"过刊种数",prop:"gkcate",sortable:!0},{label:"停刊种数",prop:"tkcate",sortable:!0}];const zt={__name:"journal",setup(s){const{journalData:t,isJournalDataLoading:a,getJournalData:e}=jt(),n=R(),o=R().subtract(5,"year"),p=r([o.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),i=r("1"),c=r("1"),l=r("chart"),u=h=>{l.value=h},_=r({shortcuts:[{text:"近3年",onClick(h){const x=new Date,k=new Date;k.setFullYear(k.getFullYear()-3),h.$emit("pick",[k,x])}},{text:"近5年",onClick(h){const x=new Date,k=new Date;k.setFullYear(k.getFullYear()-5),h.$emit("pick",[k,x])}}]}),d=v(()=>[{name:"期刊数量（种）",data:t.value.map(h=>{const x=Number(h.totalcate);return isNaN(x)?0:x})},{name:"期刊数量（册）",data:t.value.map(h=>{const x=Number(h.totalcount);return isNaN(x)?0:x})}]),f=v(()=>t.value.map(h=>h.year)),g=v(()=>{const h={xAxisData:f.value,lineData:d.value};return z(h,{customConfig:{}})}),m=async()=>{await e({startTime:p.value[0],endTime:p.value[1],oaFlag:i.value,distinctFlag:c.value})};return(async()=>m())(),{__sfc:!0,journalData:t,isJournalDataLoading:a,getJournalData:e,now:n,fiveYearsAgo:o,timeRange:p,containOA:i,removeDuplicates:c,displayType:l,handleToggleType:u,pickerOptions:_,chartData:d,xAxisData:f,lineChartData:g,getData:m,CardChart:T,journalColumns:Mt}}};var Jt=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"期刊",describe:"馆藏期刊数量","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium",clearable:!1,"value-format":"yyyy-MM-dd"},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],attrs:{data:e.journalData,columns:e.journalColumns,"null-text":"-"}}):t._e()],1)},Et=[],It=y(zt,Jt,Et,!1,null,"c6fed1a8",null,null);const Wt=It.exports,Vt=()=>{const s=r([]),t=r(!1);return{languageData:s,isLanguageDataLoading:t,getLanguageData:async e=>{t.value=!0;try{const n=await O.organizationApi.getJournalLanguage(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const Ht={__name:"language",setup(s){const{languageData:t,isLanguageDataLoading:a,getLanguageData:e}=Vt(),n=new Date().getFullYear().toString(),o=r(n),p=r("1"),i=v(()=>t.value.filter(m=>m.showtype==="0")),c=v(()=>i.value.map(m=>({name:m.name,value:m.value||0}))),l=v(()=>H({pieData:c.value},{showTotal:!1,customConfig:{series:[{radius:["0%","70%"],emphasis:{label:{show:!1}}}]}})),u=v(()=>t.value.filter(m=>m.showtype==="1")),_=v(()=>u.value.map(m=>m.name)),d=v(()=>[{name:"期刊数量（种）",data:u.value.map(m=>m.value||0)}]),f=v(()=>W({xAxisData:_.value,barData:d.value},{customConfig:{}})),g=async()=>{await e({year:o.value,oaFlag:p.value})};return(async()=>g())(),{__sfc:!0,languageData:t,isLanguageDataLoading:a,getLanguageData:e,currentYear:n,year:o,containOA:p,sourceData:i,pieData:c,languagePieData:l,type1Data:u,categories:_,seriesData:d,verticalBarOption:f,getData:g,CardChart:T}}};var Kt=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"期刊语言分布",describe:"馆藏中文、外文期刊数量","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"选择年","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[a("div",{staticClass:"flex gap-20px"},[a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.languagePieData}})],1),a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)])])},Xt=[],qt=y(Ht,Kt,Xt,!1,null,"06d180e3",null,null);const Qt=qt.exports,Gt=()=>{const s=r([]),t=r(!1);return{carrierData:s,isCarrierDataLoading:t,getCarrierData:async e=>{t.value=!0;try{const n=await O.organizationApi.getJournalCarrier(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const Zt={__name:"carrier",setup(s){const{carrierData:t,isCarrierDataLoading:a,getCarrierData:e}=Gt(),n=new Date().getFullYear().toString(),o=r(n),p=r("1"),i=r("1"),c=v(()=>t.value),l=v(()=>c.value.filter(g=>g.showtype==="0")),u=v(()=>c.value.filter(g=>g.showtype==="1")),_=v(()=>H({pieData:l.value},{showTotal:!1,customConfig:{series:[{radius:["0%","70%"],emphasis:{label:{show:!1}}}]}})),d=v(()=>H({pieData:u.value},{customConfig:{series:[{radius:["40%","70%"],emphasis:{label:{show:!0}}}]}})),f=async()=>{await e({year:o.value,oaFlag:p.value,distinctFlag:i.value})};return(async()=>f())(),{__sfc:!0,carrierData:t,isCarrierDataLoading:a,getCarrierData:e,currentYear:n,year:o,containOA:p,removeDuplicates:i,sourceData:c,pieDataCate:l,pieDataCount:u,oaPieData:_,oaPieDataWithTotal:d,getData:f,CardChart:T}}};var Ut=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"期刊载体分布",describe:"纸质、电子馆藏期刊数量","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[a("div",{staticClass:"flex gap-20px"},[a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isCarrierDataLoading,expression:"isCarrierDataLoading"}],staticClass:"h-300px",attrs:{option:e.oaPieData}})],1),a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isCarrierDataLoading,expression:"isCarrierDataLoading"}],staticClass:"h-300px",attrs:{option:e.oaPieDataWithTotal}})],1)])])},ea=[],ta=y(Zt,Ut,ea,!1,null,"15e7bdd2",null,null);const aa=ta.exports,na=()=>{const s=r([]),t=r(!1);return{subjectData:s,isSubjectDataLoading:t,getSubjectData:async e=>{t.value=!0;try{const n=await O.organizationApi.getJournalSubject(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const sa={__name:"subject",setup(s){const t=I.useSelect(),{subjectData:a,isSubjectDataLoading:e,getSubjectData:n}=na(),o=new Date().getFullYear().toString(),p=r(o),i=r("1"),c=r(""),l=r("1"),u=r("1"),_=r("chart"),d=x=>{_.value=x},f=v(()=>a.value.map(x=>x.domain_name)),g=v(()=>[{name:"期刊数量",data:a.value.map(x=>x.bookcount)}]),m=v(()=>W({xAxisData:f.value,barData:g.value},{customConfig:{}})),h=()=>{n(Object.fromEntries(Object.entries({year:p.value,eduCategory:i.value,language:c.value,oaFlag:l.value,distinctFlag:u.value}).filter(([,x])=>x)))};return(async()=>h())(),{__sfc:!0,selectStore:t,subjectData:a,isSubjectDataLoading:e,getSubjectData:n,currentYear:o,year:p,eduCategory:i,language:c,containOA:l,removeDuplicates:u,displayType:_,handleToggleType:d,categories:f,seriesData:g,verticalBarOption:m,getData:h,CardChart:T,subjectColumns:Bt}}};var ra=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"期刊学科分布",describe:"馆藏不同学科分类下的期刊数量","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择学科分类",clearable:!1},on:{change:e.getData},model:{value:e.eduCategory,callback:function(n){e.eduCategory=n},expression:"eduCategory"}},t._l(e.selectStore.selects.edu_category,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],attrs:{data:e.subjectData,columns:e.subjectColumns,"null-text":"-"}}):t._e()],1)},la=[],oa=y(sa,ra,la,!1,null,"7cab680a",null,null);const ca=oa.exports,ia=()=>{const s=r([]),t=r(!1);return{oaData:s,isOaDataLoading:t,getOaData:async e=>{t.value=!0;try{const n=await O.organizationApi.getJournalOa(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const ua={__name:"oa",setup(s){const t=I.useSelect(),{oaData:a,isOaDataLoading:e,getOaData:n}=ia(),o=new Date().getFullYear().toString(),p=r(o),i=r(""),c=r("1"),l=v(()=>a.value.length===0?0:a.value[0].value),u=v(()=>{const d=(l.value/100).toFixed(4);return pe({value:Number(d),text:"OA期刊占比"},{customConfig:{series:[{label:{formatter:function(f){return`OA期刊占比

`+(f.value*100).toFixed(2)+"%"}}}]}})}),_=()=>{n(Object.fromEntries(Object.entries({year:p.value,language:i.value,distinctFlag:c.value}).filter(([,d])=>d)))};return(async()=>_())(),{__sfc:!0,selectStore:t,oaData:a,isOaDataLoading:e,getOaData:n,currentYear:o,year:p,language:i,removeDuplicates:c,oaPercentage:l,customTextLiquidOption:u,getData:_,CardChart:T}}};var pa=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"OA期刊",describe:"Open Access Journal，公开获取（免费）的期刊。","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isOaDataLoading,expression:"isOaDataLoading"}],staticClass:"h-300px",attrs:{option:e.customTextLiquidOption}})],1)},_a=[],da=y(ua,pa,_a,!1,null,"54a50b6c",null,null);const va=da.exports,fa=()=>{const s=r([]),t=r(!1);return{statusData:s,isStatusDataLoading:t,getStatusData:async e=>{t.value=!0;try{const n=await O.organizationApi.getJournalStatus(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const ga={__name:"status",setup(s){const t=I.useSelect(),{statusData:a,isStatusDataLoading:e,getStatusData:n}=fa(),o=R(),p=R().subtract(5,"year"),i=r([p.format("YYYY-MM-DD"),o.format("YYYY-MM-DD")]),c=r(""),l=r("1"),u=r("1"),_=r("chart"),d=k=>{_.value=k},f=r({shortcuts:[{text:"近3年",onClick(k){const B=new Date,E=new Date;E.setFullYear(E.getFullYear()-3),k.$emit("pick",[E,B])}},{text:"近5年",onClick(k){const B=new Date,E=new Date;E.setFullYear(E.getFullYear()-5),k.$emit("pick",[E,B])}}]}),g=v(()=>[{name:"现刊（种）",data:a.value.map(k=>k.xkcate??0)},{name:"过刊（种）",data:a.value.map(k=>k.gkcate??0)},{name:"停刊（种）",data:a.value.map(k=>k.tkcate??0)}]),m=v(()=>a.value.map(k=>k.year)),h=v(()=>{const k={xAxisData:m.value,lineData:g.value};return z(k,{customConfig:{}})}),x=()=>{n(Object.fromEntries(Object.entries({startTime:i.value[0],endTime:i.value[1],language:c.value,oaFlag:l.value,distinctFlag:u.value}).filter(([,k])=>k)))};return(async()=>x())(),{__sfc:!0,selectStore:t,statusData:a,isStatusDataLoading:e,getStatusData:n,now:o,fiveYearsAgo:p,timeRange:i,language:c,containOA:l,removeDuplicates:u,displayType:_,handleToggleType:d,pickerOptions:f,chartData:g,xAxisData:m,lineChartData:h,getData:x,CardChart:T,statusColumns:Nt}}};var ma=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"期刊状态分布",describe:"统计现刊、过刊、停刊数量。现刊：非停刊期刊，可访问年份是最近2年的，反之过刊。停刊：注销刊号的期刊。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium",clearable:!1,"value-format":"yyyy-MM-dd"},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isStatusDataLoading,expression:"isStatusDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isStatusDataLoading,expression:"isStatusDataLoading"}],attrs:{data:e.statusData,columns:e.statusColumns,"null-text":"-"},scopedSlots:t._u([{key:"xkcate-th",fn:function(n){return[a("span",[t._v(t._s(n.column.label))]),a("el-tooltip",{scopedSlots:t._u([{key:"content",fn:function(){return[a("div",[t._v(" 指期刊的可访问年份是最近2年的（ 假设当前日期为2025年3月21日，期刊在2024年1月1日起出版过，并且非停刊） ")])]},proxy:!0}],null,!0)},[a("d-icon",{staticClass:"text-primary align-middle",attrs:{name:"el-icon-vip-icon-tishi41"}})],1)]}},{key:"gkcate-th",fn:function(n){return[a("span",[t._v(t._s(n.column.label))]),a("el-tooltip",{scopedSlots:t._u([{key:"content",fn:function(){return[a("div",[t._v(" 指期刊的可访问年份是最近2年的（ 假设当前日期为2025年3月21日，期刊在2024年1月1日起未出版过，并且非停刊） ")])]},proxy:!0}],null,!0)},[a("d-icon",{staticClass:"text-primary align-middle",attrs:{name:"el-icon-vip-icon-tishi41"}})],1)]}},{key:"tkcate-th",fn:function(n){return[a("span",[t._v(t._s(n.column.label))]),a("el-tooltip",{scopedSlots:t._u([{key:"content",fn:function(){return[a("div",[t._v("已经注销刊号的期刊")])]},proxy:!0}],null,!0)},[a("d-icon",{staticClass:"text-primary align-middle",attrs:{name:"el-icon-vip-icon-tishi41"}})],1)]}}],null,!1,832403308)}):t._e()],1)},ya=[],ha=y(ga,ma,ya,!1,null,"da8a774f",null,null);const xa=ha.exports;const ba={__name:"index",setup(s){return{__sfc:!0,Journal:Wt,Language:Qt,Carrier:aa,Subject:ca,Oa:va,Status:xa}}};var Da=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.Journal,{staticClass:"mb-5"}),a(e.Language,{staticClass:"mb-5"}),a(e.Carrier,{staticClass:"mb-5"}),a(e.Subject,{staticClass:"mb-5"}),a("div",{staticClass:"flex gap-5 mb-5"},[a("div",{staticClass:"w-550px max-w-700px"},[a(e.Oa)],1),a("div",{staticClass:"flex-1 min-w-860px"},[a(e.Status)],1)])],1)},Ca=[],wa=y(ba,Da,Ca,!1,null,"54cd0286",null,null);const ka=wa.exports,Ta=()=>{const s=r([]),t=r(!1);return{bookData:s,isBookDataLoading:t,getBookData:async e=>{t.value=!0;try{const n=await O.organizationApi.getBookCount(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}},Aa=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"图书种数",prop:"totalcate",sortable:!0},{label:"图书册数",prop:"totalcount",sortable:!0},{label:"中文图书种数",prop:"zhcate",sortable:!0},{label:"中文图书册数",prop:"zhcount",sortable:!0},{label:"外文图书种数",prop:"uncate",sortable:!0},{label:"外文图书册数",prop:"uncount",sortable:!0}];const $a={__name:"book",setup(s){const{bookData:t,isBookDataLoading:a,getBookData:e}=Ta(),n=R(),o=R().subtract(5,"year"),p=r([o.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),i=r("1"),c=r("chart"),l=m=>{c.value=m},u=r({shortcuts:[{text:"近3年",onClick(m){const h=new Date,x=new Date;x.setFullYear(x.getFullYear()-3),m.$emit("pick",[x,h])}},{text:"近5年",onClick(m){const h=new Date,x=new Date;x.setFullYear(x.getFullYear()-5),m.$emit("pick",[x,h])}}]}),_=v(()=>[{name:"图书种数",data:t.value.map(m=>m.totalcate||0)},{name:"图书册数",data:t.value.map(m=>m.totalcount||0)}]),d=v(()=>t.value.map(m=>m.year)),f=v(()=>{const m={xAxisData:d.value,lineData:_.value};return z(m,{customConfig:{}})}),g=async()=>{await e({startTime:p.value[0],endTime:p.value[1],distinctFlag:i.value})};return(async()=>g())(),{__sfc:!0,bookData:t,isBookDataLoading:a,getBookData:e,now:n,fiveYearsAgo:o,timeRange:p,removeDuplicates:i,displayType:c,handleToggleType:l,pickerOptions:u,chartData:_,xAxisData:d,lineChartData:f,getData:g,CardChart:T,bookColumns:Aa}}};var Sa=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"图书",describe:"馆藏图书数量","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isBookDataLoading,expression:"isBookDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isBookDataLoading,expression:"isBookDataLoading"}],attrs:{data:e.bookData,columns:e.bookColumns,"null-text":"-"}}):t._e()],1)},Oa=[],La=y($a,Sa,Oa,!1,null,"33e148e3",null,null);const Fa=La.exports,Ya=()=>{const s=r([]),t=r(!1);return{languageData:s,isLanguageDataLoading:t,getLanguageData:async e=>{t.value=!0;try{const n=await O.organizationApi.getBookLanguage(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const Ra={__name:"language",setup(s){const{languageData:t,isLanguageDataLoading:a,getLanguageData:e}=Ya(),n=new Date().getFullYear().toString(),o=r(n),p=v(()=>t.value.filter(g=>g.showtype==="0")),i=v(()=>p.value.map(g=>({name:g.name,value:g.value||0}))),c=v(()=>H({pieData:i.value},{showTotal:!1,customConfig:{series:[{radius:["0%","60%"],emphasis:{label:{show:!1}}}]}})),l=v(()=>t.value.filter(g=>g.showtype==="1")),u=v(()=>l.value.map(g=>g.name)),_=v(()=>[{name:"图书数量（种）",data:l.value.map(g=>g.value||0)}]),d=v(()=>W({xAxisData:u.value,barData:_.value},{customConfig:{}})),f=async()=>{await e({year:o.value})};return(async()=>f())(),{__sfc:!0,languageData:t,isLanguageDataLoading:a,getLanguageData:e,currentYear:n,year:o,sourceData:p,pieData:i,languagePieData:c,type1Data:l,categories:u,seriesData:_,verticalBarOption:d,getData:f,CardChart:T}}};var Pa=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"图书语种分布",describe:"中文、外文馆藏图书数量","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)]},proxy:!0}])},[a("div",{staticClass:"flex gap-20px"},[a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.languagePieData}})],1),a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)])])},ja=[],Ma=y(Ra,Pa,ja,!1,null,"fdc4b101",null,null);const Ba=Ma.exports,Na=()=>{const s=r([]),t=r(!1);return{carrierData:s,isCarrierDataLoading:t,getCarrierData:async e=>{t.value=!0;try{const n=await O.organizationApi.getBookCarrier(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const za={__name:"carrier",setup(s){const{carrierData:t,isCarrierDataLoading:a,getCarrierData:e}=Na(),n=new Date().getFullYear().toString(),o=r(n),p=r("1"),i=v(()=>t.value.filter(d=>d.showtype==="0").map(d=>({name:d.name,value:d.value}))),c=v(()=>t.value.filter(d=>d.showtype==="1").map(d=>({name:d.name,value:d.value}))),l=v(()=>H({pieData:i.value},{showTotal:!1,customConfig:{series:[{radius:["0%","60%"],emphasis:{label:{show:!1}}}]}})),u=v(()=>H({pieData:c.value},{customConfig:{series:[{radius:["30%","60%"],emphasis:{label:{show:!0}}}]}})),_=async()=>{await e({year:o.value,distinctFlag:p.value})};return(async()=>_())(),{__sfc:!0,carrierData:t,isCarrierDataLoading:a,getCarrierData:e,currentYear:n,year:o,removeDuplicates:p,pieData0:i,pieData1:c,oaPieData:l,oaPieDataWithTotal:u,getData:_,CardChart:T}}};var Ja=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"图书载体分布",describe:"纸质、电子馆藏图书数量","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[a("div",{staticClass:"flex gap-20px"},[a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isCarrierDataLoading,expression:"isCarrierDataLoading"}],staticClass:"h-300px",attrs:{option:e.oaPieData}})],1),a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isCarrierDataLoading,expression:"isCarrierDataLoading"}],staticClass:"h-300px",attrs:{option:e.oaPieDataWithTotal}})],1)])])},Ea=[],Ia=y(za,Ja,Ea,!1,null,"c6f55041",null,null);const Wa=Ia.exports;const Va={__name:"index",setup(s){return{__sfc:!0,Book:Fa,Language:Ba,Carrier:Wa}}};var Ha=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.Book,{staticClass:"mb-5"}),a(e.Language,{staticClass:"mb-5"}),a(e.Carrier)],1)},Ka=[],Xa=y(Va,Ha,Ka,!1,null,"654c6c52",null,null);const qa=Xa.exports,Qa=()=>{const s=r([]),t=r(!1);return{journalArticleData:s,isJournalArticleDataLoading:t,getJournalArticleData:async e=>{t.value=!0;try{const n=await O.organizationApi.getArticleCount(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}},Ga=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"期刊文献（篇）",prop:"totalcount",sortable:!0},{label:"中文期刊文献",prop:"zhcount",sortable:!0},{label:"外文期刊文献",prop:"uncount",sortable:!0}],Za=[{label:"学科",prop:"domain_name",sortable:!0},{label:"期刊文献（篇）",prop:"bookcount",sortable:!0}];const Ua={__name:"journal-article",setup(s){const{journalArticleData:t,isJournalArticleDataLoading:a,getJournalArticleData:e}=Qa(),n=R(),o=R().subtract(5,"year"),p=r([o.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),i=r("1"),c=r("1"),l=r("chart"),u=h=>{l.value=h},_=r({shortcuts:[{text:"近3年",onClick(h){const x=new Date,k=new Date;k.setFullYear(k.getFullYear()-3),h.$emit("pick",[k,x])}},{text:"近5年",onClick(h){const x=new Date,k=new Date;k.setFullYear(k.getFullYear()-5),h.$emit("pick",[k,x])}}]}),d=v(()=>[{name:"期刊文献（篇）",data:t.value.map(h=>h.totalcount||0)}]),f=v(()=>t.value.map(h=>h.year)),g=v(()=>{const h={xAxisData:f.value,lineData:d.value};return z(h,{customConfig:{}})}),m=async()=>{await e({startTime:p.value[0],endTime:p.value[1],oaFlag:i.value,distinctFlag:c.value})};return(async()=>m())(),{__sfc:!0,journalArticleData:t,isJournalArticleDataLoading:a,getJournalArticleData:e,now:n,fiveYearsAgo:o,timeRange:p,containOA:i,removeDuplicates:c,displayType:l,handleToggleType:u,pickerOptions:_,chartData:d,xAxisData:f,lineChartData:g,getData:m,CardChart:T,journalArticleColumns:Ga}}};var en=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"期刊文献",describe:"馆藏电子期刊文献数量。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalArticleDataLoading,expression:"isJournalArticleDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalArticleDataLoading,expression:"isJournalArticleDataLoading"}],attrs:{data:e.journalArticleData,columns:e.journalArticleColumns,"null-text":"-"}}):t._e()],1)},tn=[],an=y(Ua,en,tn,!1,null,"83e6bed6",null,null);const nn=an.exports,sn=()=>{const s=r([]),t=r(!1);return{languageData:s,isLanguageDataLoading:t,getLanguageData:async e=>{t.value=!0;try{const n=await O.organizationApi.getArticleLanguage(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const rn={__name:"language",setup(s){const{languageData:t,isLanguageDataLoading:a,getLanguageData:e}=sn(),n=new Date().getFullYear().toString(),o=r(n),p=r("1"),i=r("1"),c=v(()=>t.value.filter(m=>m.showtype==="0").map(m=>({name:m.name,value:m.value}))),l=v(()=>H({pieData:c.value},{showTotal:!1,customConfig:{series:[{radius:["0%","60%"],emphasis:{label:{show:!1}}}]}})),u=v(()=>t.value.filter(m=>m.showtype==="1")),_=v(()=>u.value.map(m=>m.name)),d=v(()=>[{name:"期刊文献（篇）",data:u.value.map(m=>m.value)}]),f=v(()=>W({xAxisData:_.value,barData:d.value},{customConfig:{}})),g=async()=>{await e(Object.fromEntries(Object.entries({year:o.value,oaFlag:p.value,distinctFlag:i.value}).filter(([,m])=>m)))};return g(),{__sfc:!0,languageData:t,isLanguageDataLoading:a,getLanguageData:e,currentYear:n,year:o,containOA:p,removeDuplicates:i,pieData0:c,languagePieData:l,type1Data:u,categories:_,seriesData:d,verticalBarOption:f,getData:g,CardChart:T}}};var ln=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"期刊文献语言分布",describe:"馆藏中文、外文电子期刊文献数量。","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1,"value-format":"yyyy"},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[a("div",{staticClass:"flex gap-20px"},[a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.languagePieData}})],1),a("div",{staticClass:"flex-1 min-w-300px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)])])},on=[],cn=y(rn,ln,on,!1,null,"91800f47",null,null);const un=cn.exports,pn=()=>{const s=r([]),t=r(!1);return{subjectData:s,isSubjectDataLoading:t,getSubjectData:async e=>{t.value=!0;try{const n=await O.organizationApi.getArticleSubject(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const _n={__name:"subject",setup(s){const t=I.useSelect(),{subjectData:a,isSubjectDataLoading:e,getSubjectData:n}=pn(),o=new Date().getFullYear().toString(),p=r(o),i=r("1"),c=r(""),l=r("1"),u=r("1"),_=r("chart"),d=x=>{_.value=x},f=v(()=>a.value.map(x=>x.domain_name)),g=v(()=>[{name:"期刊文献（篇）",data:a.value.map(x=>x.bookcount)}]),m=v(()=>W({xAxisData:f.value,barData:g.value},{customConfig:{}})),h=async()=>{await n({year:p.value,eduCategory:i.value,language:c.value,oaFlag:l.value,distinctFlag:u.value})};return h(),{__sfc:!0,selectStore:t,subjectData:a,isSubjectDataLoading:e,getSubjectData:n,currentYear:o,year:p,subject:i,language:c,containOA:l,removeDuplicates:u,displayType:_,handleToggleType:d,categories:f,seriesData:g,verticalBarOption:m,getData:h,CardChart:T,subjectColumns:Za}}};var dn=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"期刊文献学科分布",describe:"馆藏不同学科分类下的电子期刊文献数量。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择学科分类",clearable:!1},on:{change:e.getData},model:{value:e.subject,callback:function(n){e.subject=n},expression:"subject"}},t._l(e.selectStore.selects.edu_category,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],attrs:{data:e.subjectData,columns:e.subjectColumns,"null-text":"-"}}):t._e()],1)},vn=[],fn=y(_n,dn,vn,!1,null,"df9f7f7b",null,null);const gn=fn.exports,mn=()=>{const s=r([]),t=r(!1);return{oaData:s,isOaDataLoading:t,getOaData:async e=>{t.value=!0;try{const n=await O.organizationApi.getArticleOa(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const yn={__name:"oa",setup(s){const{oaData:t,isOaDataLoading:a,getOaData:e}=mn(),n=I.useSelect(),o=new Date().getFullYear().toString(),p=r(o),i=r(""),c=r("1"),l=v(()=>t.value.length===0?0:t.value[0].value),u=v(()=>{const d=(l.value/100).toFixed(4);return console.log(d,"v"),pe({value:Number(d),text:"OA期刊文献收录率"},{customConfig:{series:[{label:{fontSize:20,formatter:function(f){return`OA期刊文献收录率
`+(f.value*100).toFixed(2)+"%"}}}]}})}),_=async()=>{await e(Object.fromEntries(Object.entries({year:p.value,language:i.value,distinctFlag:c.value}).filter(([,d])=>d)))};return _(),{__sfc:!0,oaData:t,isOaDataLoading:a,getOaData:e,selectStore:n,currentYear:o,year:p,language:i,removeDuplicates:c,oaPercentage:l,customTextLiquidOption:u,getData:_,CardChart:T}}};var hn=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"OA期刊文献",describe:"Open Access Journal，公开获取（免费）的电子期刊文献。","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},t._l(e.selectStore.selects.lan,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("去除重复")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isOaDataLoading,expression:"isOaDataLoading"}],staticClass:"h-300px",attrs:{option:e.customTextLiquidOption}})],1)},xn=[],bn=y(yn,hn,xn,!1,null,"3ab5b896",null,null);const Dn=bn.exports;const Cn={__name:"index",setup(s){return{__sfc:!0,JournalArticle:nn,Language:un,Subject:gn,Oa:Dn}}};var wn=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.JournalArticle,{staticClass:"mb-5"}),a(e.Language,{staticClass:"mb-5"}),a(e.Subject,{staticClass:"mb-5"}),a("div",{staticClass:"flex gap-20px mb-20px"},[a("div",{staticClass:"flex-1 min-w-300px"},[a(e.Oa)],1),a("div",{staticClass:"flex-1 min-w-300px"})])],1)},kn=[],Tn=y(Cn,wn,kn,!1,null,"cdd23f78",null,null);const An=Tn.exports;const $n={__name:"index",setup(s){const t=r([{name:"1",label:"数据库"},{name:"2",label:"期刊"},{name:"3",label:"图书"},{name:"4",label:"期刊文献"}]),a=r("1");return{__sfc:!0,tabs:t,currentTabName:a,handleTabClick:n=>{a.value=n.name},TabDatabase:Pt,TabJournal:ka,TabBook:qa,TabJournalArticle:An}}};var Sn=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a("div",{staticClass:"h-11 flex gap-10px"},t._l(e.tabs,function(n){return a("div",{key:n.name,staticClass:"tab-item cursor-pointer h-9 leading-9 bg-[#F0F4FF] text-[#697FFA] px-4 rounded-3px text-sm",class:{active:n.name===e.currentTabName},on:{click:function(o){return e.handleTabClick(n)}}},[t._v(" "+t._s(n.label)+" ")])}),0),e.currentTabName==="1"?a(e.TabDatabase,{staticClass:"mt-5"}):t._e(),e.currentTabName==="2"?a(e.TabJournal,{staticClass:"mt-5"}):t._e(),e.currentTabName==="3"?a(e.TabBook,{staticClass:"mt-5"}):t._e(),e.currentTabName==="4"?a(e.TabJournalArticle,{staticClass:"mt-5"}):t._e()],1)},On=[],Ln=y($n,Sn,On,!1,null,"220d247e",null,null);const Fn=Ln.exports;const Yn={__name:"collection-info",setup(s){return{__sfc:!0}}};var Rn=function(){var t=this;return t._self._c,t._self._setupProxy,t._m(0)},Pn=[function(){var s=this,t=s._self._c;return s._self._setupProxy,t("div",{staticClass:"text-sm text-[#3C4B5D]"},[t("div",{staticClass:"indent-2em"},[s._v(" 从馆藏数量维度进行统计分析，展示本机构馆藏文献的客观数据量，您可以在这里查看到数据库、期刊、期刊文献的馆藏数量变化趋势以及分布情况。 ")])])}],jn=y(Yn,Rn,Pn,!1,null,"1eef17d8",null,null);const Mn=jn.exports,Bn=()=>{const s=r([]),t=r(!1);return{journalData:s,isJournalDataLoading:t,getJournalData:async e=>{t.value=!0;try{const n=await O.organizationApi.getImportantJournal(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}},Nn=[{label:"重要收录",prop:"indicator_label",sortable:!0},{label:"版本",prop:"data_version",sortable:!0},{label:"期刊总量",prop:"indicator_total",sortable:!0},{label:"期刊保障量",prop:"indicator_value",sortable:!0},{label:"期刊保障率",prop:"indicator_ratio",sortable:!0},{label:"未保障量",prop:"indicator_sec_value",sortable:!0}],zn=[{label:"重要收录",prop:"data_version",sortable:!0},{label:"期刊总量",prop:"indicator_total",sortable:!0},{label:"馆藏保障量",prop:"indicator_value",sortable:!0},{label:"保障率",prop:"indicator_ratio",sortable:!0},{label:"未保障量",prop:"indicator_sec_ratio",sortable:!0}],Jn=[{label:"数据库",prop:"dbname",sortable:!0},{label:"未保障重要收录期刊收录量（种）",prop:"indicator_value",sortable:!0}],En=[{label:"重要收录",prop:"indicator_name",sortable:!0},{label:"版本",prop:"data_version",sortable:!0},{label:"期刊文献总量",prop:"indicator_total",sortable:!0},{label:"馆藏保障量",prop:"indicator_value",sortable:!0},{label:"保障率",prop:"indicator_ratio",sortable:!0}];const In={__name:"journal",setup(s){const{journalData:t,isJournalDataLoading:a,getJournalData:e}=Bn(),n=new Date().getFullYear().toString(),o=r(n),p=r("chart"),i=d=>{p.value=d},c=v(()=>t.value.map(d=>d.indicator_label)),l=v(()=>[{name:"保障率",data:t.value.map(d=>d.indicator_ratio||0)}]),u=v(()=>W({xAxisData:c.value,barData:l.value},{horizontal:!0,customConfig:{tooltip:{formatter:d=>{let f=`${d[0].axisValue}<br/>`;return d.forEach(g=>{f+=`${g.marker} ${g.seriesName}: ${g.value}%<br/>`}),f}},xAxis:{axisLabel:{formatter:d=>V(d)+"%",color:"#909399"}}}})),_=async()=>{await e({year:o.value})};return _(),{__sfc:!0,journalData:t,isJournalDataLoading:a,getJournalData:e,currentYear:n,year:o,displayType:p,handleToggleType:i,categories:c,seriesData:l,verticalBarOption:u,getData:_,CardChart:T,journalColumns:Nn}}};var Wn=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"重要收录期刊保障情况",describe:"常见中外文重要收录期刊的保障量、保障率。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1,"value-format":"yyyy"},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],staticClass:"h-800px",attrs:{option:e.verticalBarOption}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],attrs:{data:e.journalData,columns:e.journalColumns,"null-text":"-"},scopedSlots:t._u([{key:"indicator_ratio",fn:function(n){return[a("span",[t._v(t._s(n.row.indicator_ratio)+"%")])]}}],null,!1,331873059)}):t._e()],1)},Vn=[],Hn=y(In,Wn,Vn,!1,null,"10706c4a",null,null);const Kn=Hn.exports,Xn=()=>{const s=r([]),t=r(!1);return{partitionData:s,isPartitionDataLoading:t,getPartitionData:async e=>{t.value=!0;try{const n=await O.organizationApi.getImportantPartition(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const qn={__name:"partition",setup(s){const t=I.useSelect(),{partitionData:a,isPartitionDataLoading:e,getPartitionData:n}=Xn(),o=new Date().getFullYear().toString(),p=r(o),i=r("1"),c=v(()=>a.value.filter(f=>f.data_type==="0")),l=v(()=>a.value.filter(f=>f.data_type==="1")),u=v(()=>[...c.value.map(f=>({name:f.data_version,value:f.indicator_ratio})),...l.value.map(f=>({name:f.data_version,value:f.indicator_ratio}))]),_=v(()=>Be({funnelData:u.value},{colors:["#6777EF","#36BDFF","#F2A940","#62AC00","#53B666"],sort:"descending",gap:4})),d=async()=>{await n({year:p.value,quartileCategory:i.value})};return d(),{__sfc:!0,selectStore:t,partitionData:a,isPartitionDataLoading:e,getPartitionData:n,currentYear:o,year:p,partition:i,topData:c,otherData:l,currentPeriodData:u,compareFunnelOption:_,getData:d,CardChart:T,partitionColumns:zn}}};var Qn=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"重要收录期刊分区保障分析",describe:"常见中外文重要收录期刊分区保障情况。"},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择分区"},on:{change:e.getData},model:{value:e.partition,callback:function(n){e.partition=n},expression:"partition"}},t._l(e.selectStore.selects.core_mark_quartile_category,function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)])]},proxy:!0}])},[a("div",{staticClass:"flex gap-20px w-full"},[a("div",{staticClass:"flex-1 max-w-300-px"},[a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isPartitionDataLoading,expression:"isPartitionDataLoading"}],staticClass:"h-300px",attrs:{option:e.compareFunnelOption}})],1),a("div",{staticClass:"flex-1"},[a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isPartitionDataLoading,expression:"isPartitionDataLoading"}],attrs:{data:e.partitionData,columns:e.partitionColumns,"null-text":"-"},scopedSlots:t._u([{key:"indicator_ratio",fn:function(n){return[a("span",[t._v(t._s(n.row.indicator_ratio)+"%")])]}}])})],1)])])},Gn=[],Zn=y(qn,Qn,Gn,!1,null,"ee731bee",null,null);const Un=Zn.exports,es=()=>{const s=r([]),t=r(!1);return{databaseTop10Data:s,isDatabaseTop10DataLoading:t,getDatabaseTop10Data:async e=>{t.value=!0;try{const n=await O.organizationApi.getImportantDatabaseTop10(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const ts={__name:"database-top10",setup(s){const{databaseTop10Data:t,isDatabaseTop10DataLoading:a,getDatabaseTop10Data:e}=es(),n=new Date().getFullYear().toString(),o=r(n),p=r("1"),i=r("chart"),c=g=>{i.value=g},l=v(()=>t.value.map(g=>({name:g.dbname,value:Number(g.indicator_value)}))),u=v(()=>q({treeData:l.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),_=r(null),d=g=>{g.data&&(_.value={name:g.data.name,value:g.data.value,path:g.treePathInfo.map(m=>m.name).join(" > "),level:g.treePathInfo.length-1,rawData:g.data},console.log("点击了矩形树图节点:",g))},f=async()=>{await e({year:o.value,oaFlag:p.value})};return f(),{__sfc:!0,databaseTop10Data:t,isDatabaseTop10DataLoading:a,getDatabaseTop10Data:e,currentYear:n,year:o,containOA:p,displayType:i,handleToggleType:c,basicTreeData:l,basicTreemapOption:u,clickInfo:_,handleTreemapClick:d,getData:f,CardChart:T,databaseTop10Columns:Jn}}};var as=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"未保障重要收录期刊数据库收录TOP10",describe:"收录未保障重要收录期刊数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseTop10DataLoading,expression:"isDatabaseTop10DataLoading"}],staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseTop10DataLoading,expression:"isDatabaseTop10DataLoading"}],attrs:{data:e.databaseTop10Data,columns:e.databaseTop10Columns,"null-text":"-"}}):t._e()],1)},ns=[],ss=y(ts,as,ns,!1,null,"46487dd9",null,null);const rs=ss.exports,ls=()=>{const s=r([]),t=r(!1);return{journalArticleData:s,isJournalArticleDataLoading:t,getJournalArticleData:async e=>{t.value=!0;try{const n=await O.organizationApi.getImportantArticle(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}};const os={__name:"journal-article",setup(s){const{journalArticleData:t,isJournalArticleDataLoading:a,getJournalArticleData:e}=ls(),n=new Date().getFullYear().toString(),o=r(n),p=r("chart"),i=d=>{p.value=d},c=v(()=>t.value.map(d=>d.indicator_name)),l=v(()=>[{name:"保障率",data:t.value.map(d=>d.indicator_ratio)}]),u=v(()=>W({xAxisData:c.value,barData:l.value},{horizontal:!0,customConfig:{tooltip:{formatter:d=>{let f=`${d[0].axisValue}<br/>`;return d.forEach(g=>{f+=`${g.marker} ${g.seriesName}: ${g.value}%<br/>`}),f}},xAxis:{axisLabel:{formatter:d=>V(d)+"%",color:"#909399"}}}})),_=async()=>{await e({year:o.value})};return _(),{__sfc:!0,journalArticleData:t,isJournalArticleDataLoading:a,getJournalArticleData:e,currentYear:n,year:o,displayType:p,handleToggleType:i,categories:c,seriesData:l,verticalBarOption:u,getData:_,CardChart:T,journalArticleColumns:En}}};var cs=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"重要收录期刊文献保障情况",describe:"常见中外文重要数据库收录期刊文献的保障量、保障率。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalArticleDataLoading,expression:"isJournalArticleDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalArticleDataLoading,expression:"isJournalArticleDataLoading"}],attrs:{data:e.journalArticleData,columns:e.journalArticleColumns,"null-text":"-"},scopedSlots:t._u([{key:"indicator_ratio",fn:function(n){return[a("span",[t._v(t._s(n.row.indicator_ratio)+"%")])]}}],null,!1,331873059)}):t._e()],1)},is=[],us=y(os,cs,is,!1,null,"f7303adc",null,null);const ps=us.exports;const _s={__name:"index",setup(s){return{__sfc:!0,Journal:Kn,Partition:Un,DatabaseTop10:rs,JournalArticle:ps}}};var ds=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.Journal,{staticClass:"mb-5"}),a(e.Partition,{staticClass:"mb-5"}),a(e.DatabaseTop10,{staticClass:"mb-5"}),a(e.JournalArticle,{staticClass:"mb-5"})],1)},vs=[],fs=y(_s,ds,vs,!1,null,"51502a33",null,null);const gs=fs.exports;const ms={__name:"key-collection-info",setup(s){return{__sfc:!0}}};var ys=function(){var t=this;return t._self._c,t._self._setupProxy,t._m(0)},hs=[function(){var s=this,t=s._self._c;return s._self._setupProxy,t("div",{staticClass:"text-sm text-[#3C4B5D]"},[t("div",{staticClass:"indent-2em"},[s._v(" 从馆藏文献的重要收录保障情况进行统计分析，评估本机构馆藏文献。重要收录保障评价仅支持期刊和期刊文献，并且数据范围仅限于：JCR期刊、Scopus来源刊、EI来源刊、ESI来源刊、北大核心期刊、南大核心期刊、中科院分区表来源刊、CSCD来源刊、WOS-A&HCI来源刊、WOS-SSCI来源刊、WOS-SCIE来源刊、WOS-ESCI来源刊\\WOS-A&HCI期刊文献、WOS-SSCI期刊文献、WOS-SCIE期刊文献、WOS-ESCI期刊文献。 ")])])}],xs=y(ms,ys,hs,!1,null,"7cc041d5",null,null);const bs=xs.exports,Ds=()=>{const s=r([]),t=r(!1);return{academicAchievementsData:s,isAcademicAchievementsDataLoading:t,getAcademicAchievementsData:async e=>{t.value=!0;try{const n=await O.organizationApi.getAchievementData(e);s.value=n.list}catch(n){console.log(n)}finally{t.value=!1}}}},Cs=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"学术成果（篇）",prop:"indicator_total",sortable:!0},{label:"馆藏保障量（篇）",prop:"indicator_value",sortable:!0},{label:"保障率",prop:"indicator_ratio",sortable:!0},{label:"未保障量（篇）",prop:"indicator_sec_value",sortable:!0}];const ws={__name:"academic-achievements",setup(s){const{academicAchievementsData:t,isAcademicAchievementsDataLoading:a,getAcademicAchievementsData:e}=Ds(),n=R(),o=R().subtract(5,"year"),p=r([o.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),i=r("1"),c=r("chart"),l=m=>{c.value=m},u=r({shortcuts:[{text:"近3年",onClick(m){const h=new Date,x=new Date;x.setFullYear(x.getFullYear()-3),m.$emit("pick",[x,h])}},{text:"近5年",onClick(m){const h=new Date,x=new Date;x.setFullYear(x.getFullYear()-5),m.$emit("pick",[x,h])}}]}),_=v(()=>[{name:"学术成果（篇）",data:t.value.map(m=>m.indicator_total??0)},{name:"学术成果保障量（篇）",data:t.value.map(m=>m.indicator_value??0)},{name:"学术成果保障率 （%）",data:t.value.map(m=>m.indicator_ratio??0)}]),d=v(()=>t.value.map(m=>m.year)),f=v(()=>{const m={xAxisData:d.value,lineData:_.value};return z(m,{customConfig:{tooltip:{formatter:h=>h.length>2?(h[2],h.map((x,k)=>k===2?`${x.marker} ${x.seriesName}: ${Number(x.value).toFixed(1)}%`:`${x.marker} ${x.seriesName}: ${x.value}`).join("<br/>")):h.map(x=>`${x.marker} ${x.seriesName}: ${x.value}`).join("<br/>")}}})}),g=async()=>{await e({startTime:p.value[0],endTime:p.value[1],oaFlag:i.value})};return g(),{__sfc:!0,academicAchievementsData:t,isAcademicAchievementsDataLoading:a,getAcademicAchievementsData:e,now:n,fiveYearsAgo:o,timeRange:p,containOA:i,displayType:c,handleToggleType:l,pickerOptions:u,chartData:_,xAxisData:d,lineChartData:f,getData:g,CardChart:T,academicAchievementsColumns:Cs}}};var ks=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"学术成果",describe:"机构学术成果数量及保障情况。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isAcademicAchievementsDataLoading,expression:"isAcademicAchievementsDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isAcademicAchievementsDataLoading,expression:"isAcademicAchievementsDataLoading"}],attrs:{data:e.academicAchievementsData,columns:e.academicAchievementsColumns,"null-text":"-"},scopedSlots:t._u([{key:"indicator_ratio",fn:function(n){return[a("span",[t._v(t._s(n.row.indicator_ratio??0)+"%")])]}}],null,!1,3405306803)}):t._e()],1)},Ts=[],As=y(ws,ks,Ts,!1,null,"a0ca2d41",null,null);const $s=As.exports;const Ss={__name:"achievements-top10",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=l=>{e.value=l},o=r([{name:"图书",value:6e3},{name:"期刊",value:4e3},{name:"数据库",value:3e3},{name:"多媒体",value:2e3},{name:"报纸",value:1e3}]),p=v(()=>q({treeData:o.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),i=r(null);return{__sfc:!0,year:t,containOA:a,displayType:e,handleToggleType:n,basicTreeData:o,basicTreemapOption:p,clickInfo:i,handleTreemapClick:l=>{l.data&&(i.value={name:l.data.name,value:l.data.value,path:l.treePathInfo.map(u=>u.name).join(" > "),level:l.treePathInfo.length-1,rawData:l.data},console.log("点击了矩形树图节点:",l))},CardChart:T}}};var Os=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"学术成果收录TOP10（数据库）",describe:"收录机构学术成果数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Ls=[],Fs=y(Ss,Os,Ls,!1,null,"3dbda27a",null,null);const Ys=Fs.exports;const Rs={__name:"journal",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=l=>{e.value=l},o=r({shortcuts:[{text:"近3年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-3),l.$emit("pick",[_,u])}},{text:"近5年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-5),l.$emit("pick",[_,u])}}]}),p=r([{name:"访问量",data:[1e3,2e3]},{name:"成交量",data:[500,1500]}]),i=r(["周一","周二"]),c=v(()=>{const l={xAxisData:i.value,lineData:p.value};return z(l,{customConfig:{}})});return{__sfc:!0,timeRange:t,containOA:a,displayType:e,handleToggleType:n,pickerOptions:o,chartData:p,xAxisData:i,lineChartData:c,CardChart:T}}};var Ps=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"发文期刊",describe:"机构学术成果发文期刊的数量以及保障情况。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},js=[],Ms=y(Rs,Ps,js,!1,null,"dd0e3eab",null,null);const Bs=Ms.exports;const Ns={__name:"journal-top10",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=l=>{e.value=l},o=r([{name:"图书",value:6e3},{name:"期刊",value:4e3},{name:"数据库",value:3e3},{name:"多媒体",value:2e3},{name:"报纸",value:1e3}]),p=v(()=>q({treeData:o.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),i=r(null);return{__sfc:!0,year:t,containOA:a,displayType:e,handleToggleType:n,basicTreeData:o,basicTreemapOption:p,clickInfo:i,handleTreemapClick:l=>{l.data&&(i.value={name:l.data.name,value:l.data.value,path:l.treePathInfo.map(u=>u.name).join(" > "),level:l.treePathInfo.length-1,rawData:l.data},console.log("点击了矩形树图节点:",l))},CardChart:T}}};var zs=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"发文期刊收录TOP10（数据库）",describe:"收录机构发文期刊数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Js=[],Es=y(Ns,zs,Js,!1,null,"c39149f5",null,null);const Is=Es.exports;const Ws={__name:"journal-top20",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=c=>{e.value=c},o=r([{name:"Nature",value:1200},{name:"Science",value:1e3},{name:"Cell",value:900},{name:"PNAS",value:850},{name:"NEJM",value:800},{name:"Lancet",value:750},{name:"JAMA",value:700},{name:"BMJ",value:650},{name:"PLoS ONE",value:600},{name:"Scientific Reports",value:550},{name:"IEEE Transactions",value:500},{name:"Advanced Materials",value:450},{name:"Chemical Reviews",value:400},{name:"Angewandte Chemie",value:350},{name:"ACS Nano",value:300},{name:"Journal of Biological Chemistry",value:250},{name:"Physical Review Letters",value:200},{name:"Nucleic Acids Research",value:150},{name:"Journal of the American Chemical Society",value:100},{name:"Advanced Functional Materials",value:50}]),p=v(()=>ce({wordcloudData:o.value},{colors:["#6777EF","#36BDFF","#F2A940","#62AC00","#FC544B"],sizeRange:[14,50],rotationRange:[0,0],shape:"circle",customConfig:{}}));return{__sfc:!0,year:t,containOA:a,displayType:e,handleToggleType:n,journalData:o,wordCloudOption:p,handleWordCloudClick:c=>{c.data&&console.log("点击了词云节点:",c.data.name,c.data.value)},CardChart:T}}};var Vs=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"发文期刊TOP20",describe:"发文量前20的期刊。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.wordCloudOption,events:{click:e.handleWordCloudClick}}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Hs=[],Ks=y(Ws,Vs,Hs,!1,null,"ca3f4c25",null,null);const Xs=Ks.exports;const qs={__name:"index",setup(s){return{__sfc:!0,AcademicAchievements:$s,AchievementsTop10:Ys,Journal:Bs,JournalTop10:Is,JournalTop20:Xs}}};var Qs=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.AcademicAchievements,{staticClass:"mb-20px"}),a(e.AchievementsTop10,{staticClass:"mb-20px"}),a(e.Journal,{staticClass:"mb-20px"}),a(e.JournalTop10,{staticClass:"mb-20px"}),a(e.JournalTop20,{staticClass:"mb-20px"})],1)},Gs=[],Zs=y(qs,Qs,Gs,!1,null,"1468ac80",null,null);const Us=Zs.exports;const er={__name:"references",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=l=>{e.value=l},o=r({shortcuts:[{text:"近3年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-3),l.$emit("pick",[_,u])}},{text:"近5年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-5),l.$emit("pick",[_,u])}}]}),p=r([{name:"访问量",data:[1e3,2e3]},{name:"成交量",data:[500,1500]}]),i=r(["周一","周二"]),c=v(()=>{const l={xAxisData:i.value,lineData:p.value};return z(l,{customConfig:{}})});return{__sfc:!0,timeRange:t,containOA:a,displayType:e,handleToggleType:n,pickerOptions:o,chartData:p,xAxisData:i,lineChartData:c,CardChart:T}}};var tr=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"参考文献",describe:"机构学术成果参考文献数量及保障情况。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},ar=[],nr=y(er,tr,ar,!1,null,"4df1b386",null,null);const sr=nr.exports;const rr={__name:"references-top20",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=l=>{e.value=l},o=r({shortcuts:[{text:"近3年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-3),l.$emit("pick",[_,u])}},{text:"近5年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-5),l.$emit("pick",[_,u])}}]}),p=r([{name:"Nature",value:1200},{name:"Science",value:1e3},{name:"Cell",value:900},{name:"PNAS",value:850},{name:"NEJM",value:800},{name:"Lancet",value:750},{name:"JAMA",value:700},{name:"BMJ",value:650},{name:"PLoS ONE",value:600},{name:"Scientific Reports",value:550},{name:"IEEE Transactions",value:500},{name:"Advanced Materials",value:450},{name:"Chemical Reviews",value:400},{name:"Angewandte Chemie",value:350},{name:"ACS Nano",value:300},{name:"Journal of Biological Chemistry",value:250},{name:"Physical Review Letters",value:200},{name:"Nucleic Acids Research",value:150},{name:"Journal of the American Chemical Society",value:100},{name:"Advanced Functional Materials",value:50}]),i=v(()=>ce({wordcloudData:p.value},{colors:["#6777EF","#36BDFF","#F2A940","#62AC00","#FC544B"],sizeRange:[14,50],rotationRange:[0,0],shape:"circle",customConfig:{}}));return{__sfc:!0,year:t,containOA:a,displayType:e,handleToggleType:n,pickerOptions:o,journalData:p,wordCloudOption:i,handleWordCloudClick:l=>{l.data&&console.log("点击了词云节点:",l.data.name,l.data.value)},CardChart:T}}};var lr=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"参考文献TOP20",describe:"参考次数前20的期刊文献。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[a("dv-chart",{staticClass:"h-300px",attrs:{option:e.wordCloudOption,events:{click:e.handleWordCloudClick}}})],1)},or=[],cr=y(rr,lr,or,!1,null,"f5ed86e3",null,null);const ir=cr.exports;const ur={__name:"references-top10",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=u=>{e.value=u},o=r({shortcuts:[{text:"近3年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-3),u.$emit("pick",[d,_])}},{text:"近5年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-5),u.$emit("pick",[d,_])}}]}),p=r([{name:"图书",value:6e3},{name:"期刊",value:4e3},{name:"数据库",value:3e3},{name:"多媒体",value:2e3},{name:"报纸",value:1e3}]),i=v(()=>q({treeData:p.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),c=r(null);return{__sfc:!0,timeRange:t,containOA:a,displayType:e,handleToggleType:n,pickerOptions:o,basicTreeData:p,basicTreemapOption:i,clickInfo:c,handleTreemapClick:u=>{u.data&&(c.value={name:u.data.name,value:u.data.value,path:u.treePathInfo.map(_=>_.name).join(" > "),level:u.treePathInfo.length-1,rawData:u.data},console.log("点击了矩形树图节点:",u))},CardChart:T}}};var pr=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"参考文献收录TOP10（数据库）",describe:"收录机构参考文献数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:t.year,callback:function(n){t.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},_r=[],dr=y(ur,pr,_r,!1,null,"37639b12",null,null);const vr=dr.exports;const fr={__name:"journal",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=l=>{e.value=l},o=r({shortcuts:[{text:"近3年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-3),l.$emit("pick",[_,u])}},{text:"近5年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-5),l.$emit("pick",[_,u])}}]}),p=r([{name:"访问量",data:[1e3,2e3]},{name:"成交量",data:[500,1500]}]),i=r(["周一","周二"]),c=v(()=>{const l={xAxisData:i.value,lineData:p.value};return z(l,{customConfig:{}})});return{__sfc:!0,timeRange:t,containOA:a,displayType:e,handleToggleType:n,pickerOptions:o,chartData:p,xAxisData:i,lineChartData:c,CardChart:T}}};var gr=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"参考期刊",describe:"机构学术成果参考期刊的数量以及保障情况。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},mr=[],yr=y(fr,gr,mr,!1,null,"1ed66d1e",null,null);const hr=yr.exports;const xr={__name:"journal-top10",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=l=>{e.value=l},o=r([{name:"图书",value:6e3},{name:"期刊",value:4e3},{name:"数据库",value:3e3},{name:"多媒体",value:2e3},{name:"报纸",value:1e3}]),p=v(()=>q({treeData:o.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),i=r(null);return{__sfc:!0,year:t,containOA:a,displayType:e,handleToggleType:n,basicTreeData:o,basicTreemapOption:p,clickInfo:i,handleTreemapClick:l=>{l.data&&(i.value={name:l.data.name,value:l.data.value,path:l.treePathInfo.map(u=>u.name).join(" > "),level:l.treePathInfo.length-1,rawData:l.data},console.log("点击了矩形树图节点:",l))},CardChart:T}}};var br=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"参考期刊收录TOP10（数据库）",describe:"收录机构参考期刊数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Dr=[],Cr=y(xr,br,Dr,!1,null,"e886102a",null,null);const wr=Cr.exports;const kr={__name:"journal-top20",setup(s){const t=r(""),a=r(!0),e=r("chart"),n=l=>{e.value=l},o=r({shortcuts:[{text:"近3年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-3),l.$emit("pick",[_,u])}},{text:"近5年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-5),l.$emit("pick",[_,u])}}]}),p=r([{name:"Nature",value:1200},{name:"Science",value:1e3},{name:"Cell",value:900},{name:"PNAS",value:850},{name:"NEJM",value:800},{name:"Lancet",value:750},{name:"JAMA",value:700},{name:"BMJ",value:650},{name:"PLoS ONE",value:600},{name:"Scientific Reports",value:550},{name:"IEEE Transactions",value:500},{name:"Advanced Materials",value:450},{name:"Chemical Reviews",value:400},{name:"Angewandte Chemie",value:350},{name:"ACS Nano",value:300},{name:"Journal of Biological Chemistry",value:250},{name:"Physical Review Letters",value:200},{name:"Nucleic Acids Research",value:150},{name:"Journal of the American Chemical Society",value:100},{name:"Advanced Functional Materials",value:50}]),i=v(()=>ce({wordcloudData:p.value},{colors:["#6777EF","#36BDFF","#F2A940","#62AC00","#FC544B"],sizeRange:[14,50],rotationRange:[0,0],shape:"circle",customConfig:{}}));return{__sfc:!0,year:t,containOA:a,displayType:e,handleToggleType:n,pickerOptions:o,journalData:p,wordCloudOption:i,handleWordCloudClick:l=>{l.data&&console.log("点击了词云节点:",l.data.name,l.data.value)},CardChart:T}}};var Tr=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"参考期刊TOP20",describe:"参考次数前20的期刊。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("div",[a("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[t._v("含OA资源")]),a("el-switch",{model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.wordCloudOption,events:{click:e.handleWordCloudClick}}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Ar=[],$r=y(kr,Tr,Ar,!1,null,"cd5b87e8",null,null);const Sr=$r.exports;const Or={__name:"index",setup(s){return{__sfc:!0,References:sr,ReferencesTop20:ir,ReferencesTop10:vr,Journal:hr,JournalTop10:wr,JournalTop20:Sr}}};var Lr=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.References,{staticClass:"mb-20px"}),a(e.ReferencesTop20,{staticClass:"mb-20px"}),a(e.ReferencesTop10,{staticClass:"mb-20px"}),a(e.Journal,{staticClass:"mb-20px"}),a(e.JournalTop10,{staticClass:"mb-20px"}),a(e.JournalTop20,{staticClass:"mb-20px"})],1)},Fr=[],Yr=y(Or,Lr,Fr,!1,null,"cb7a97cc",null,null);const Rr=Yr.exports;const Pr={__name:"index",setup(s){const t=r([{name:"1",label:"学术成果"},{name:"2",label:"参考文献"}]),a=r("1");return{__sfc:!0,tabs:t,currentTabName:a,handleTabClick:n=>{a.value=n.name},TabAcademicAchievements:Us,TabReferences:Rr}}};var jr=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a("div",{staticClass:"h-11 flex gap-10px"},t._l(e.tabs,function(n){return a("div",{key:n.name,staticClass:"tab-item cursor-pointer h-9 leading-9 bg-[#F0F4FF] text-[#697FFA] px-4 rounded-3px text-sm",class:{active:n.name===e.currentTabName},on:{click:function(o){return e.handleTabClick(n)}}},[t._v(" "+t._s(n.label)+" ")])}),0),e.currentTabName==="1"?a(e.TabAcademicAchievements):t._e(),e.currentTabName==="2"?a(e.TabReferences):t._e()],1)},Mr=[],Br=y(Pr,jr,Mr,!1,null,"3be0cda8",null,null);const Nr=Br.exports;const zr={__name:"achievement-info",setup(s){return{__sfc:!0}}};var Jr=function(){var t=this;return t._self._c,t._self._setupProxy,t._m(0)},Er=[function(){var s=this,t=s._self._c;return s._self._setupProxy,t("div",{staticClass:"text-sm text-[#3C4B5D]"},[t("div",{staticClass:"indent-2em"},[s._v(" 从机构学术成果保障情况以及参考文献保障情况进行统计分析，评估本机构馆藏文献。学出成果仅支持期刊文献，并且数据范围仅限于：WOS（SCI-EXPANDED、SSCI、A&HCI、ESCI、IC、CCR-EXPANDED）、EI期刊、ScienceDirect期刊、Scopus 期刊、IEEE 期刊、Springer期刊、PubMed期刊、PMC期刊、Wiley期刊、美国化学学会期刊、美国计算机协会期刊、cell 、nature、science 、CNKI、维普中文科技期刊、万方中文期刊、CSCD 、CSSCI。参考文献数据范围仅WOS和维普中文期刊。 ")])])}],Ir=y(zr,Jr,Er,!1,null,"3d0fbb7e",null,null);const Wr=Ir.exports;const Vr={__name:"click-count",setup(s){const t=R(),a=R().subtract(5,"year"),e=r([a.format("YYYY-MM-DD"),t.format("YYYY-MM-DD")]),n=r("chart"),o=u=>{n.value=u},p=r({shortcuts:[{text:"近3年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-3),u.$emit("pick",[d,_])}},{text:"近5年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-5),u.$emit("pick",[d,_])}}]}),i=r([{name:"访问量",data:[1e3,2e3]},{name:"成交量",data:[500,1500]}]),c=r(["周一","周二"]),l=v(()=>{const u={xAxisData:c.value,lineData:i.value};return z(u,{customConfig:{}})});return{__sfc:!0,now:t,fiveYearsAgo:a,timeRange:e,displayType:n,handleToggleType:o,pickerOptions:p,chartData:i,xAxisData:c,lineChartData:l,CardChart:T}}};var Hr=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"数据库点击量",describe:"机构学者点击数据库的次数。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Kr=[],Xr=y(Vr,Hr,Kr,!1,null,"3be7ed6a",null,null);const qr=Xr.exports;const Qr={__name:"click-count-top10",setup(s){const t=r(""),a=r(!0);return{__sfc:!0,year:t,containOA:a,CardChart:T}}};var Gr=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"数据库点击量(TOP10)",describe:"点击次数前10的数据库。","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)]},proxy:!0}])},[t._v(" TODO:表格 ")])},Zr=[],Ur=y(Qr,Gr,Zr,!1,null,"51b56c22",null,null);const el=Ur.exports;const tl={__name:"index",setup(s){return{__sfc:!0,ClickCount:qr,ClickCountTop10:el}}};var al=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.ClickCount,{staticClass:"mb-20px"}),a(e.ClickCountTop10,{staticClass:"mb-20px"})],1)},nl=[],sl=y(tl,al,nl,!1,null,"a53c1728",null,null);const rl=sl.exports;const ll={__name:"document-delivery",setup(s){const t=R(),a=R().subtract(5,"year"),e=r([a.format("YYYY-MM-DD"),t.format("YYYY-MM-DD")]),n=r("chart"),o=u=>{n.value=u},p=r({shortcuts:[{text:"近3年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-3),u.$emit("pick",[d,_])}},{text:"近5年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-5),u.$emit("pick",[d,_])}}]}),i=r([{name:"访问量",data:[1e3,2e3]},{name:"成交量",data:[500,1500]}]),c=r(["周一","周二"]),l=v(()=>{const u={xAxisData:c.value,lineData:i.value};return z(u,{customConfig:{}})});return{__sfc:!0,now:t,fiveYearsAgo:a,timeRange:e,displayType:n,handleToggleType:o,pickerOptions:p,chartData:i,xAxisData:c,lineChartData:l,CardChart:T}}};var ol=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"文献传递量",describe:"机构学者传递文献的次数。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},cl=[],il=y(ll,ol,cl,!1,null,"82a40544",null,null);const ul=il.exports;const pl={__name:"delivery-article-top10",setup(s){const t=r(""),a=r({shortcuts:[{text:"近3年",onClick(e){const n=new Date,o=new Date;o.setFullYear(o.getFullYear()-3),e.$emit("pick",[o,n])}},{text:"近5年",onClick(e){const n=new Date,o=new Date;o.setFullYear(o.getFullYear()-5),e.$emit("pick",[o,n])}}]});return{__sfc:!0,year:t,pickerOptions:a,CardChart:T}}};var _l=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"文献传递TOP10（文献）",describe:"传递次数前10的文献。","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)]},proxy:!0}])},[t._v(" TODO:表格 ")])},dl=[],vl=y(pl,_l,dl,!1,null,"75900e0b",null,null);const fl=vl.exports;const gl={__name:"delivery-database-top10",setup(s){const t=r(""),a=r("chart"),e=c=>{a.value=c},n=r([{name:"图书",value:6e3},{name:"期刊",value:4e3},{name:"数据库",value:3e3},{name:"多媒体",value:2e3},{name:"报纸",value:1e3}]),o=v(()=>q({treeData:n.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),p=r(null);return{__sfc:!0,year:t,displayType:a,handleToggleType:e,basicTreeData:n,basicTreemapOption:o,clickInfo:p,handleTreemapClick:c=>{c.data&&(p.value={name:c.data.name,value:c.data.value,path:c.treePathInfo.map(l=>l.name).join(" > "),level:c.treePathInfo.length-1,rawData:c.data},console.log("点击了矩形树图节点:",c))},CardChart:T}}};var ml=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"文献传递TOP10（数据库）",describe:"传递次数前10的数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},yl=[],hl=y(gl,ml,yl,!1,null,"dbe23f9d",null,null);const xl=hl.exports;const bl={__name:"delivery-subject",setup(s){const t=r(""),a=r(""),e=r(!0),n=r(!0),o=r("chart"),p=u=>{o.value=u},i=r(["一月","二月","三月","四月","五月","六月"]),c=r([{name:"收入",data:[320,332,301,334,390,330]},{name:"支出",data:[120,132,101,134,90,230]}]),l=v(()=>W({xAxisData:i.value,barData:c.value},{customConfig:{}}));return{__sfc:!0,year:t,subject:a,containOA:e,removeDuplicates:n,displayType:o,handleToggleType:p,categories:i,seriesData:c,verticalBarOption:l,CardChart:T}}};var Dl=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"传递文献学科分布",describe:"不同学科分类下的文献传递数量。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择学科"},model:{value:e.subject,callback:function(n){e.subject=n},expression:"subject"}},t._l([{label:"中文",value:"1"},{label:"外文",value:"2"}],function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Cl=[],wl=y(bl,Dl,Cl,!1,null,"35ee5ea4",null,null);const kl=wl.exports;const Tl={__name:"accepted-volume",setup(s){const t=R(),a=R().subtract(5,"year"),e=r([a.format("YYYY-MM-DD"),t.format("YYYY-MM-DD")]),n=r("chart"),o=u=>{n.value=u},p=r({shortcuts:[{text:"近3年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-3),u.$emit("pick",[d,_])}},{text:"近5年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-5),u.$emit("pick",[d,_])}}]}),i=r([{name:"访问量",data:[1e3,2e3]},{name:"成交量",data:[500,1500]}]),c=r(["周一","周二"]),l=v(()=>{const u={xAxisData:c.value,lineData:i.value};return z(u,{customConfig:{}})});return{__sfc:!0,now:t,fiveYearsAgo:a,timeRange:e,displayType:n,handleToggleType:o,pickerOptions:p,chartData:i,xAxisData:c,lineChartData:l,CardChart:T}}};var Al=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"文献接收量",describe:"机构学者接收文献的次数。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},$l=[],Sl=y(Tl,Al,$l,!1,null,"115b9628",null,null);const Ol=Sl.exports;const Ll={__name:"accepted-article-top10",setup(s){const t=r(""),a=r({shortcuts:[{text:"近3年",onClick(e){const n=new Date,o=new Date;o.setFullYear(o.getFullYear()-3),e.$emit("pick",[o,n])}},{text:"近5年",onClick(e){const n=new Date,o=new Date;o.setFullYear(o.getFullYear()-5),e.$emit("pick",[o,n])}}]});return{__sfc:!0,timeRange:t,pickerOptions:a,CardChart:T}}};var Fl=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"文献接收TOP10（文献）",describe:"接收次数前10的文献。","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})],1)]},proxy:!0}])},[t._v(" TODO:表格 ")])},Yl=[],Rl=y(Ll,Fl,Yl,!1,null,"47eaca35",null,null);const Pl=Rl.exports;const jl={__name:"accepted-database-top10",setup(s){const t=r(""),a=r("chart"),e=c=>{a.value=c},n=r([{name:"图书",value:6e3},{name:"期刊",value:4e3},{name:"数据库",value:3e3},{name:"多媒体",value:2e3},{name:"报纸",value:1e3}]),o=v(()=>q({treeData:n.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),p=r(null);return{__sfc:!0,year:t,displayType:a,handleToggleType:e,basicTreeData:n,basicTreemapOption:o,clickInfo:p,handleTreemapClick:c=>{c.data&&(p.value={name:c.data.name,value:c.data.value,path:c.treePathInfo.map(l=>l.name).join(" > "),level:c.treePathInfo.length-1,rawData:c.data},console.log("点击了矩形树图节点:",c))},CardChart:T}}};var Ml=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"文献接收TOP10（数据库）",describe:"接收次数前10的数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Bl=[],Nl=y(jl,Ml,Bl,!1,null,"35f8ffac",null,null);const zl=Nl.exports;const Jl={__name:"accepted-subject",setup(s){const t=r(""),a=r(""),e=r(!0),n=r(!0),o=r("chart"),p=_=>{o.value=_},i=r({shortcuts:[{text:"近3年",onClick(_){const d=new Date,f=new Date;f.setFullYear(f.getFullYear()-3),_.$emit("pick",[f,d])}},{text:"近5年",onClick(_){const d=new Date,f=new Date;f.setFullYear(f.getFullYear()-5),_.$emit("pick",[f,d])}}]}),c=r(["一月","二月","三月","四月","五月","六月"]),l=r([{name:"收入",data:[320,332,301,334,390,330]},{name:"支出",data:[120,132,101,134,90,230]}]),u=v(()=>W({xAxisData:c.value,barData:l.value},{customConfig:{}}));return{__sfc:!0,year:t,subject:a,containOA:e,removeDuplicates:n,displayType:o,handleToggleType:p,pickerOptions:i,categories:c,seriesData:l,verticalBarOption:u,CardChart:T}}};var El=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"接收文献学科分布",describe:"不同学科分类下的文献接收数量。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),a("el-select",{attrs:{placeholder:"请选择学科"},model:{value:e.subject,callback:function(n){e.subject=n},expression:"subject"}},t._l([{label:"中文",value:"1"},{label:"外文",value:"2"}],function(n){return a("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)])]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Il=[],Wl=y(Jl,El,Il,!1,null,"4e3cbaf4",null,null);const Vl=Wl.exports;const Hl={__name:"index",setup(s){return{__sfc:!0,DocumentDelivery:ul,DeliveryArticleTop10:fl,DeliveryDatabaseTop10:xl,DeliverySubject:kl,AcceptedVolume:Ol,AcceptedArticleTop10:Pl,AcceptedDatabaseTop10:zl,AcceptedSubject:Vl}}};var Kl=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.DocumentDelivery,{staticClass:"mb-20px"}),a(e.DeliveryArticleTop10,{staticClass:"mb-20px"}),a(e.DeliveryDatabaseTop10,{staticClass:"mb-20px"}),a(e.DeliverySubject,{staticClass:"mb-20px"}),a(e.AcceptedVolume,{staticClass:"mb-20px"}),a(e.AcceptedArticleTop10,{staticClass:"mb-20px"}),a(e.AcceptedDatabaseTop10,{staticClass:"mb-20px"}),a(e.AcceptedSubject,{staticClass:"mb-20px"})],1)},Xl=[],ql=y(Hl,Kl,Xl,!1,null,"2b8a61c6",null,null);const Ql=ql.exports;const Gl={__name:"book-borrowing",setup(s){const t=R(),a=R().subtract(5,"year"),e=r([a.format("YYYY-MM-DD"),t.format("YYYY-MM-DD")]),n=r("chart"),o=u=>{n.value=u},p=r({shortcuts:[{text:"近3年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-3),u.$emit("pick",[d,_])}},{text:"近5年",onClick(u){const _=new Date,d=new Date;d.setFullYear(d.getFullYear()-5),u.$emit("pick",[d,_])}}]}),i=r([{name:"访问量",data:[1e3,2e3]},{name:"成交量",data:[500,1500]}]),c=r(["周一","周二"]),l=v(()=>{const u={xAxisData:c.value,lineData:i.value};return z(u,{customConfig:{}})});return{__sfc:!0,now:t,fiveYearsAgo:a,timeRange:e,displayType:n,handleToggleType:o,pickerOptions:p,chartData:i,xAxisData:c,lineChartData:l,CardChart:T}}};var Zl=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"纸质图书借阅量",describe:"机构学者借阅纸质图书的次数。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?a("dv-chart",{staticClass:"h-300px",attrs:{option:e.lineChartData}}):t._e(),e.displayType==="table"?a("d-table",{attrs:{data:t.tableData,columns:t.columns}}):t._e()],1)},Ul=[],eo=y(Gl,Zl,Ul,!1,null,"9a7ada5d",null,null);const to=eo.exports;const ao={__name:"book-borrowing-top10",setup(s){const t=r(""),a=r({shortcuts:[{text:"近3年",onClick(e){const n=new Date,o=new Date;o.setFullYear(o.getFullYear()-3),e.$emit("pick",[o,n])}},{text:"近5年",onClick(e){const n=new Date,o=new Date;o.setFullYear(o.getFullYear()-5),e.$emit("pick",[o,n])}}]});return{__sfc:!0,year:t,pickerOptions:a,CardChart:T}}};var no=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a(e.CardChart,{attrs:{title:"文献传递TOP10（文献）",describe:"传递次数前10的文献。","show-export":""},scopedSlots:t._u([{key:"operation-left",fn:function(){return[a("div",{staticClass:"flex gap-20px items-center"},[a("div",{staticClass:"flex gap-10px"},[a("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)])]},proxy:!0}])},[t._v(" TODO:表格 ")])},so=[],ro=y(ao,no,so,!1,null,"26797ee7",null,null);const lo=ro.exports;const oo={__name:"index",setup(s){return{__sfc:!0,BookBorrowing:to,BookBorrowingTop10:lo}}};var co=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a(e.BookBorrowing,{staticClass:"mb-20px"}),a(e.BookBorrowingTop10,{staticClass:"mb-20px"})],1)},io=[],uo=y(oo,co,io,!1,null,"c59bfd68",null,null);const po=uo.exports;const _o={__name:"index",setup(s){const t=r([{name:"1",label:"数据库"},{name:"3",label:"纸书借阅"}]),a=r("1");return{__sfc:!0,tabs:t,currentTabName:a,handleTabClick:n=>{a.value=n.name},TabDatabase:rl,TabDocumentDelivery:Ql,TabBookBorrowing:po}}};var vo=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a("div",[a("div",{staticClass:"h-11 flex gap-10px"},t._l(e.tabs,function(n){return a("div",{key:n.name,staticClass:"tab-item cursor-pointer h-9 leading-9 bg-[#F0F4FF] text-[#697FFA] px-4 rounded-3px text-sm",class:{active:n.name===e.currentTabName},on:{click:function(o){return e.handleTabClick(n)}}},[t._v(" "+t._s(n.label)+" ")])}),0),e.currentTabName==="1"?a(e.TabDatabase):t._e(),e.currentTabName==="2"?a(e.TabDocumentDelivery):t._e(),e.currentTabName==="3"?a(e.TabBookBorrowing):t._e()],1)])},fo=[],go=y(_o,vo,fo,!1,null,"14bf32a3",null,null);const mo=go.exports;const yo={__name:"literature-info",setup(s){return{__sfc:!0}}};var ho=function(){var t=this;return t._self._c,t._self._setupProxy,t._m(0)},xo=[function(){var s=this,t=s._self._c;return s._self._setupProxy,t("div",{staticClass:"text-sm text-[#3C4B5D]"},[t("div",{staticClass:"indent-2em"},[s._v("从机构学者使用馆藏文献的情况进行统计分析，评估本机构馆藏文献。")])])}],bo=y(yo,ho,xo,!1,null,"80dab287",null,null);const Do=bo.exports;const Co={__name:"collapse-menu",setup(s){const t=r(null),a=r(null),e=r(null);return{__sfc:!0,selectedMainIndex:t,selectedSubKey:a,openIndex:e,menuList:[{title:"05/ 馆藏数量评价",children:["美视电影学院美视电影学院美视电影学院","馆藏数据库重复文献分析","馆藏数据库重复文献分析","馆藏数据库重复文献分析"]},{title:"05/ 馆藏数量评价",children:["馆藏数据库重复文献分析"]},{title:"06/ 馆藏数量评价",children:[]}],toggle:i=>{e.value=e.value===i?null:i},handleMainClick:(i,c)=>{c.children&&c.children.length>0?e.value=e.value===i?null:i:(t.value=i,a.value=null)}}}};var wo=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",{staticClass:"menu"},t._l(e.menuList,function(n,o){return a("div",{key:o,staticClass:"menu-item"},[a("div",{staticClass:"menu-title",class:{clickable:n.children&&n.children.length>0,active:(!n.children||n.children.length===0)&&e.selectedMainIndex===o},on:{click:function(p){return e.handleMainClick(o,n)}}},[t._v(" "+t._s(n.title)+" "),n.children&&n.children.length>0?a("span",{staticClass:"arrow bg-white rounded-7px flex items-center justify-center",class:{rotated:e.openIndex===o}},[a("i",{staticClass:"el-icon-arrow-up text-[#6777EF]"})]):t._e()]),a("transition",{attrs:{name:"slide"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.openIndex===o,expression:"openIndex === index"}],staticClass:"submenu"},t._l(n.children,function(p,i){return a("div",{key:`${o}-${i}`,staticClass:"submenu-item",class:{active:e.selectedSubKey===`${o}-${i}`},on:{click:function(c){e.selectedSubKey=`${o}-${i}`}}},[t._v(" "+t._s(p)+" ")])}),0)])],1)}),0)},ko=[],To=y(Co,wo,ko,!1,null,"4d8e59f9",null,null);const Ao=To.exports,$o={},So=null,Oo=null;var Lo=y($o,So,Oo,!1,null,null,null,null);const Fo=Lo.exports,Yo={},Ro=null,Po=null;var jo=y(Yo,Ro,Po,!1,null,null,null,null);const Mo=jo.exports;const Bo={__name:"heading",props:{content:{type:String,required:!0},level:{type:Number,required:!0}},setup(s){return{__sfc:!0,props:s}}};var No=function(){var t=this,a=t._self._c;return t._self._setupProxy,t.level===1?a("h1",[t._v(t._s(t.content))]):t.level===2?a("h2",[t._v(t._s(t.content))]):t.level===3?a("h3",[t._v(t._s(t.content))]):t.level===4?a("h4",[t._v(t._s(t.content))]):t.level===5?a("h5",[t._v(t._s(t.content))]):t.level===6?a("h6",[t._v(t._s(t.content))]):t._e()},zo=[],Jo=y(Bo,No,zo,!1,null,"ad97372c",null,null);const Eo=Jo.exports;const Io={__name:"paragraph",props:{content:{type:String,required:!0}},setup(s){return{__sfc:!0,props:s}}};var Wo=function(){var t=this,a=t._self._c;return t._self._setupProxy,a("p",[t._v(t._s(t.content))])},Vo=[],Ho=y(Io,Wo,Vo,!1,null,"5248a8a1",null,null);const Ko=Ho.exports,Xo={},qo=null,Qo=null;var Go=y(Xo,qo,Qo,!1,null,null,null,null);const Zo=Go.exports;const Uo={__name:"content-block",props:{block:{type:Object,required:!0}},setup(s){const t=s,a=r(t.block.content),e=r(!0),n=v(()=>({toc:Fo,cover:Mo,heading:Eo,paragraph:Ko,table:Zo})[t.block.type]||"div"),o=(i,c)=>i.replace(/\{\{\s*(\w+)\s*\}\}/g,(l,u)=>c[u]!==void 0?String(c[u]):l),p=async()=>{try{const i={method:t.block.api.method,url:t.block.api.url};t.block.api.method==="GET"?i.params=t.block.api.params:i.data=t.block.api.body,e.value=!0,setTimeout(()=>{a.value=o(t.block.content,{data1:"张三",data2:18}),e.value=!1},2e3)}catch(i){console.log(i)}finally{e.value=!1}};return(async()=>t.block.api?await p():e.value=!1)(),{__sfc:!0,props:t,renderedContent:a,loading:e,componentType:n,replaceVariables:o,getComponentData:p}}};var ec=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",{staticClass:"content-block"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a(e.componentType,{tag:"component",attrs:{content:e.renderedContent,level:t.block.level,"table-headers":t.block.tableHeaders}})],1)])},tc=[],ac=y(Uo,ec,tc,!1,null,"6bc4360a",null,null);const nc=ac.exports;const sc={__name:"index",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","confirm","cancel"],setup(s,{emit:t}){const a=s,e=r([{type:"paragraph",content:"截止2024年12月31日，刊表内含{{data1}}万种期刊，{{data2}}万篇期刊文献",api:{url:"/api/chapter1",method:"GET"}},{type:"heading",level:2,content:"期刊分类",api:null}]);return{__sfc:!0,props:a,emit:t,blocks:e,CollapseMenu:Ao,ContentBlock:nc}}};var rc=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("d-dialog",{attrs:{title:"报告预览",type:"ok","show-footer":!1,width:"1200px",visible:e.props.visible},on:{"update:visible":function(n){return e.emit("update:visible",n)}}},[a("div",{staticClass:"flex justify-between gap-5"},[a("div",{staticClass:"toc w-220px"},[a(e.CollapseMenu)],1),a("div",{staticClass:"content flex-1 h-840px bg-[#F1F3F7] relative flex justify-center items-center"},[a("div",{staticClass:"w-550px h-780px overflow-y-auto"},t._l(e.blocks,function(n,o){return a(e.ContentBlock,{key:o,attrs:{block:n}})}),1),a("div",{staticClass:"arrow-previous bg-[#3C4B5D]"},[a("d-icon",{attrs:{name:"el-icon-vip-a-zuojiantou2"}})],1),a("div",{staticClass:"arrow-next bg-[#3C4B5D]"},[a("d-icon",{attrs:{name:"el-icon-vip-youjiantou"}})],1)])])])},lc=[],oc=y(sc,rc,lc,!1,null,"94354a52",null,null);const cc=oc.exports,ic="data:image/png;base64,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",uc=""+new URL("logo-19e1cc3d.png",import.meta.url).href;const pc={__name:"index",setup(s){const t=r(!1),a=r(""),e=r(""),n=r(""),o=r({shortcuts:[{text:"近3年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-3),l.$emit("pick",[_,u])}},{text:"近5年",onClick(l){const u=new Date,_=new Date;_.setFullYear(_.getFullYear()-5),l.$emit("pick",[_,u])}}]}),p=r([{operator:"张三",exportFormat:"pdf",exportTime:"2024-01-01 12:00:00"}]),i=r([{label:"操作者",prop:"operator",align:"center"},{label:"导出格式",prop:"exportFormat",align:"center"},{label:"导出时间",prop:"exportTime",align:"center"}]);return{__sfc:!0,previewVisible:t,operatorName:a,exportFormat:e,timeRange:n,pickerOptions:o,reportHistoryList:p,databaseColumns:i,handlePreviewReport:()=>{t.value=!0},DialogPreview:cc}}};var _c=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",{staticClass:"overflow-hidden flex justify-center"},[a("div",{staticClass:"w-462px h-804px mr-5 rounded-3px bg-[rgba(103,119,239,0.05)] flex flex-col items-center"},[t._m(0),a("div",{staticClass:"flex justify-center mt-30px"},[a("div",{staticClass:"flex flex-col items-center cursor-pointer",on:{click:e.handlePreviewReport}},[a("img",{staticClass:"w-30px h-25px",attrs:{src:ic,alt:""}}),a("div",{staticClass:"text-[#3C4B5D] text-sm mt-10px"},[t._v("报告预览")])])])]),a(e.DialogPreview,{attrs:{visible:e.previewVisible},on:{"update:visible":function(n){e.previewVisible=n}}})],1)},dc=[function(){var s=this,t=s._self._c;return s._self._setupProxy,t("div",{staticClass:"w-400px h-568px bg-white mt-45px"},[t("div",{staticClass:"img w-400px h-408px pt-25px"},[t("div",{staticClass:"ml-30px flex"},[t("img",{staticClass:"w-26px h-26px",attrs:{src:uc,alt:""}}),t("div",{staticClass:"text-white flex flex-col justify-center ml-8px"},[t("p",{staticClass:"text-xs"},[s._v("xxx大学图书馆")]),t("p",{staticClass:"text-[4px]"},[s._v("UNIVERSITY LIBRARY")])])])]),t("div",{staticClass:"describe text-[#10308D] font-bold text-base px-30px"},[t("div",{staticClass:"mt-5"},[s._v("xxx大学图书馆")]),t("div",[s._v("馆藏资源绩效分析报告（机构馆藏）")]),t("div",{staticClass:"mt-26px flex justify-between text-[#000000] text-[10px] font-normal"},[t("div",[s._v("2020-10")])])])])}],vc=y(pc,_c,dc,!1,null,"a9a16ccc",null,null);const fc=vc.exports;const gc={__name:"index",setup(s){const t=r("achievement-data");return{__sfc:!0,currentTabTop:t,handleChangeTopTab:e=>{t.value=e.value},TABS_TOP:ge,OrganizationOverview:je,OrganizationInfo:ke,CollectionQuantity:Fn,CollectionInfo:Mn,KeyCollection:gs,KeyCollectionInfo:bs,AchievementData:Nr,AchievementInfo:Wr,LiteratureBehavior:mo,LiteratureInfo:Do,Report:fc}}};var mc=function(){var t=this,a=t._self._c,e=t._self._setupProxy;return a("div",[a("d-card",{staticClass:"mb-5 text-[#34395E]",scopedSlots:t._u([{key:"title",fn:function(){return t._l(e.TABS_TOP,function(n){return a("div",{key:n.value,staticClass:"tab-top cursor-pointer leading-64px px-10px",class:{active:e.currentTabTop===n.value},on:{click:function(o){return e.handleChangeTopTab(n)}}},[t._v(" "+t._s(n.label)+" ")])})},proxy:!0}])},[e.currentTabTop==="organization-overview"?a(e.OrganizationInfo):t._e(),e.currentTabTop==="collection-quantity"?a(e.CollectionInfo):t._e(),e.currentTabTop==="key-collection"?a(e.KeyCollectionInfo):t._e(),e.currentTabTop==="achievement-data"?a(e.AchievementInfo):t._e(),e.currentTabTop==="literature-behavior"?a(e.LiteratureInfo):t._e()],1),a("d-card",{attrs:{"content-class":"text-blue-500","title-class":"justify-between"}},[e.currentTabTop==="organization-overview"?a(e.OrganizationOverview):t._e(),e.currentTabTop==="collection-quantity"?a(e.CollectionQuantity):t._e(),e.currentTabTop==="key-collection"?a(e.KeyCollection):t._e(),e.currentTabTop==="achievement-data"?a(e.AchievementData):t._e(),e.currentTabTop==="literature-behavior"?a(e.LiteratureBehavior):t._e(),e.currentTabTop==="report"?a(e.Report):t._e()],1)],1)},yc=[],hc=y(gc,mc,yc,!1,null,"2b9534bc",null,null);const wc=hc.exports;export{wc as default};
