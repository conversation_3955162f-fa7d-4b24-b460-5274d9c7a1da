var L=Object.defineProperty;var J=(i,e,n)=>e in i?L(i,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[e]=n;var _=(i,e,n)=>(J(i,typeof e!="symbol"?e+"":e,n),n);import{n as $,V as c,x as v,D as z,P as q,m as G,E as M,p as K,q as Z,v as Q,y as W}from"./element-variables-bb5f5d94.js";import"./main-e30dbd5a.js";const X={__name:"index",setup(i){return{__sfc:!0}}};var Y=function(){var e=this,n=e._self._c;return e._self._setupProxy,n("div",[e._v("app")])},ee=[],te=$(X,Y,ee,!1,null,"d03fe605",null,null);const ne=te.exports;function j(i){var e=i.type,n=i.url,t=i.query,s=window.dlibAppRouterPrefix?window.dlibAppRouterPrefix:"";e=e||"router";var o="";if(t&&(o="?"+Object.keys(t).map(function(l){return l+"="+t[l]}).join("&")),e=="router"){var r=window.location.href,a=r.split("/"),d="";if(!(r.indexOf("/index")!==-1||r.indexOf("/scenemanage")==-1&&r.indexOf("c67f511b-1a3a-4607-8d3e-7adee2a2359d")>-1||r.indexOf("/scenemanage")==-1&&r.indexOf("bd920f5c-f7bd-4051-9874-b1d251b04464")>-1||window.location.href===window.origin)){var u=a[a.length-2];u.indexOf("#")!==-1&&a.length>3&&(u=a[a.length-3]),d=u.indexOf(".")===-1?u:""}var y=s+(d?"/"+d+n+o:n+o);return y}if(e=="dlib"){var g=function(){var l="",p=n[0]||"",I=n[1]||"",b=localStorage.getItem("urlInfo")?JSON.parse(localStorage.getItem("urlInfo")):[],w=b.find(function(h){return h.code==p})?b.find(function(h){return h.code==p}).path:"";return p=="index"?l=w+"/index?page=1":l=w+"/"+p+I+o,{v:l}}();if(typeof g=="object")return g.v}return e=="full"?n:""}function oe(){if(axios)return axios({url:window.apiDomainAndPort+"/appcenter/api/baseinfo/getbaseinfo",method:"get",Authorization:"Bearer "+localStorage.getItem("BasicToken")}).then(function(i){if(i.data&&i.data.statusCode==200){var e=i.data.data||{};return localStorage.setItem("fileUrl",e.orgInfo.fileUrl),localStorage.setItem("headerFooterInfo",JSON.stringify(e.headerFooterInfo)),localStorage.setItem("orgInfo",JSON.stringify(e.orgInfo)),localStorage.setItem("urlInfo",JSON.stringify(e.urlInfo)),e&&e.userInfo&&localStorage.setItem("userInfo",JSON.stringify(e.userInfo)),{data:e,code:200}}else return{code:499,message:"基础信息获取失败"}}).catch(function(i){return{code:499,message:"基础信息获取失败"}})}window.addCommonStyle=function(i,e){var n=document.getElementsByClassName(e);if(!(n.length>0)){var t=document.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("type","text/css"),t.setAttribute("class",e),t.setAttribute("href",i),document.getElementsByTagName("body")[0].appendChild(t),t}};const f=i=>new Promise((e,n)=>{const t=document.createElement("script");t.type="text/javascript",t.src=i,t.onload=e,document.body.appendChild(t)}),m=i=>new Promise((e,n)=>{const t=document.createElement("link");t.rel="stylesheet",t.type="text/css",t.href=i,t.onload=e,document.head.appendChild(t)});class ie{constructor(){_(this,"ready",!1);_(this,"initReady",!1);_(this,"specialHeaderFooterConfig",[])}renderHeaderFooter(e,n){var t;try{this.router=e.router,this.headerFooterConfig=e.headerFooterConfig||[];const s=(o,r,a)=>{o={...o,meta:e.headerFooterConfig.find(u=>o.path.includes(u.route))||{}};const d=()=>{var h,C,S,A,k,E,O,P,F,U,x,R,B,T,D,N;const u=e.containerHeaderId,y=e.containerFooterId,g=((C=(h=o.meta)==null?void 0:h.header)==null?void 0:C.url)??this.headerRouter,l=((A=(S=o.meta)==null?void 0:S.footer)==null?void 0:A.url)??this.footerRouter,p=((E=(k=o.meta)==null?void 0:k.header)==null?void 0:E.options)??null,I=((P=(O=o.meta)==null?void 0:O.footer)==null?void 0:P.options)??null;this.headerVisible=((U=(F=o.meta)==null?void 0:F.header)==null?void 0:U.visible)!==!1,this.footerVisible=((R=(x=o.meta)==null?void 0:x.footer)==null?void 0:R.visible)!==!1;const b=((T=(B=o.meta)==null?void 0:B.header)==null?void 0:T.className)??this.newheadcla,w=((N=(D=o.meta)==null?void 0:D.footer)==null?void 0:N.className)??this.newfootcla;this.init_head(u,b,this.themecolor,p),this.init_footer(y,w,this.themecolor,I),Promise.all([f(g+"/component.js"),m(g+"/component.css"),f(l+"/component.js"),m(l+"/component.css")]).finally(()=>{dispatchEvent(new CustomEvent("headerFooter:ready"))})};this.initReady&&this.ready?d():addEventListener("headerFooterInfo:ready",d),a()};(t=this.router)==null||t.beforeEach(s),window.Vue.prototype.$setHref=j,window.Vue.prototype.localStorage=localStorage,this.oldheadid=e.containerHeaderId,this.oldfootid=e.containerFooterId,this.token=e.token,this.basicComponents=e.components,this.baseUrl=e.baseUrl||"https://openapi.vipslib.com",this.initFun=(...o)=>{n(...o)},this.baseInfo=e.baseInfo,e.callback&&(window.$vipsmart_jumpother_callback=e.callback),e.onLoginCallBack&&(window.$vipsmart_onLoginCallBack=e.onLoginCallBack)}catch(s){console.error(s)}this.initVipSmart()}getOptions(){return new Promise(e=>{if(this.baseInfo)window.apiDomainAndPort=this.baseInfo.apiDomainAndPort,window.casBaseUrl=this.baseInfo.casBaseUrl,window.OrgTokenLink=this.baseInfo.orgTokenLink,window.orgCode=this.baseInfo.orgCode,e();else{let n=this.baseUrl+"/open/OAuth/GetBaseInfo";axios.post(n,{token:this.token}).then(t=>{window.apiDomainAndPort=t.data.data.apiDomainAndPort,window.casBaseUrl=t.data.data.casBaseUrl,window.OrgTokenLink=t.data.data.orgTokenLink,window.orgCode=t.data.data.orgCode,t.data.data.userKey&&localStorage.setItem("token",this.token),e()}).catch(t=>{})}})}initVipSmart(){Promise.resolve().then(()=>this.getOptions()).then(()=>oe()).then(()=>{let e=JSON.parse(localStorage.getItem("headerFooterInfo"));this.headerRouter=e.headerRouter,this.footerRouter=e.footerRouter,this.headerNavList=e.headerNavList,this.newheadcla=e.headerTemplateCode,this.newfootcla=e.footerTemplateCode,this.themecolor=e.themeColor,this.fileUrl=JSON.parse(localStorage.getItem("orgInfo")).portalUrl,this.ready=!0,this.initReady&&this.ready&&dispatchEvent(new CustomEvent("headerFooterInfo:ready"))}).then(async()=>{this.initHead_VipSmart()})}repHead_VipSmart(e,n,t,s,o){return new Promise(r=>{Vue.nextTick(()=>{e&&this.init_head(e,t,o),n&&this.init_footer(n,s,o),r()})})}init_head(e,n,t,s){let o=document.getElementById(e);if(o.innerHTML="",!this.headerVisible)return;let r=document.createElement("div");r.setAttribute("class",t),o.appendChild(r);let a=document.createElement("div");a.setAttribute("class",n),a.setAttribute("id","jl_vip_zt_header_warp"),s&&a.setAttribute("data-set",JSON.stringify(s)),r.appendChild(a);let d=document.createElement("div");d.setAttribute("id","jl_vip_zt_header"),a.appendChild(d)}init_footer(e,n,t,s){let o=document.getElementById(e);if(o.innerHTML="",!this.footerVisible)return;let r=document.createElement("div");r.setAttribute("class",t),o.appendChild(r);let a=document.createElement("div");a.setAttribute("class",n),s&&a.setAttribute("data-set",JSON.stringify(s)),r.appendChild(a);let d=document.createElement("div");d.setAttribute("id","jl_vip_zt_footer"),a.appendChild(d)}initHead_VipSmart(){if(f(this.fileUrl+"/cdn/public/js/babel-polyfill/browser.min.js"),f(this.fileUrl+"/cdn/public/js/babel-polyfill/polyfill.min.js"),m(this.fileUrl+"/cdn/public/css/vipsmart-dalib3-skin.css"),setTimeout(()=>{m(this.fileUrl+"/cdn/public/template/app_template/dalib_bar/dalib_top_right_bar/component.css"),f(this.fileUrl+"/cdn/public/template/app_template/dalib_bar/dalib_top_right_bar/component.js")},2e3),this.baseInfo&&this.baseInfo.staticFileUrl){const r=this.baseInfo.staticFileUrl+"/cdn/custom/themes/"+window.orgCode+"/index.css",a=this.baseInfo.staticFileUrl+"/cdn/custom/plugins/"+window.orgCode+"/index.js";m(r),f(a);const d=document.createElement("style");d.innerHTML=`
        .dalib_top_bar img,
        .dalib_right_bar img {
          display: inline-block;
          vertical-align: baseline;
        }
      `,document.head.appendChild(d)}if(localStorage.getItem("headerFooterInfo")){const r=JSON.parse(localStorage.getItem("headerFooterInfo"));r&&r.themeColor&&document.body.classList.add(r.themeColor)}if(!this.basicComponents){this.initFun();return}if(!/^[a-zA-Z]+(;[a-zA-Z]+)*$/.test(this.basicComponents))return;const n={pagination:"/cdn/public/template/app_template/dalibPagination",breadcrumb:"/cdn/public/template/app_template/top_breadCrumbs_nv",banner:"/cdn/public/template/app_template/dalibtop_banner",menu:"/cdn/public/template/app_template/app_menu_sys",comment:"/cdn/public/template/readers_comments/temp2"},t=[],s=this.basicComponents.split(";");if(s.forEach(r=>{const a=n[r.trim()];a&&(m(this.fileUrl+a+"/component.css"),t.push(f(this.fileUrl+a+"/component.js")))}),s.includes("banner")){localStorage.removeItem("bannerData");const r=JSON.stringify({bannerHeight:200});axios({url:window.apiDomainAndPort+"/scenemanage/api/header-footer/banner-data",method:"GET",headers:{"Content-Type":"text/plain",Authorization:"Bearer "+localStorage.getItem("BasicToken")}}).then(a=>{a.data&&a.data.statusCode==200?localStorage.setItem("bannerData",JSON.stringify(a.data.data)):localStorage.setItem("bannerData",r)}).catch(()=>{localStorage.setItem("bannerData",r)})}const o=[];Promise.all(t).then(()=>{typeof renderBanner=="function"&&o.push(renderBanner),typeof renderPagin=="function"&&o.push(renderPagin),typeof renderBreadcrumb=="function"&&o.push(renderBreadcrumb),typeof renderReaderComments=="function"&&o.push(renderReaderComments),typeof renderMenu=="function"&&o.push(renderMenu),this.initReady=!0,this.initReady&&this.ready&&dispatchEvent(new CustomEvent("headerFooterInfo:ready"))}).then(()=>{this.initFun(o)})}}window.$setHref=j;const re=new ie;const ae=window["d-utils"].APP_CODE,H=window["d-utils"].storage,se=window["d-utils"].getApplicationConfig;window["d-utils"].attempt;const de=window["d-utils"].afterTokenReady,V=window["d-utils"].Token,le=window["d-utils"].request,ce=window["d-utils"].logout;H.local.set("env:app-cli",1);const ue=async()=>{c.use(z),c.use(q),c.use(G),c.use(M),c.use(K),c.prototype.APP_CODE=ae;const i=Z();i.use(Q),new c({pinia:i,router:v,render:e=>e(ne)}).$mount("#root"),v.addRoutes(W.app)};(async()=>{await de();const i=await V.getInstance().getValue();i&&H.local.set("token",i);const e=await se();window.axios=le,window.logoutCallback=ce,window.accessToken=await V.getInstance().getBasicValue(),window.orgcode=e.ORG_CODE,window.Vue=c,re.renderHeaderFooter({containerHeaderId:"vp-header",containerFooterId:"vp-footer",components:"pagination;breadcrumb;banner",baseInfo:{apiDomainAndPort:e.API_BASE_URL,casBaseUrl:e.CAS_BASE_URL,orgTokenLink:e.BASIC_TOKEN_URL,orgCode:e.ORG_CODE},router:v,headerFooterConfig:[]},async n=>{try{await Promise.all(n==null?void 0:n.map(t=>t(c)))}catch(t){console.error(t)}finally{ue()}})})();
