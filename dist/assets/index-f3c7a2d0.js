import{n as b,r as o,F as $,A as _e,B as ve,c as h,s as E,G as ge}from"./element-variables-32ad61ab.js";/* empty css                                                              */import{t as V,h as le,d as X}from"./helpers-640ed53b.js";import"./main-a563c162.js";const fe=[{label:"机构总览",value:"organization-overview"},{label:"馆藏数量评价",value:"collection-quantity"},{label:"重要收录保障评价",value:"key-collection"},{label:"成果数据保障评价",value:"achievement-data"},{label:"文献行为评价",value:"literature-behavior"},{label:"报告",value:"report"}];const me={__name:"card-chart",props:{title:{type:String,required:!0},describe:{type:String},showChart:{type:Boolean,default:!1},showTable:{type:Boolean,default:!1},showExport:{type:Boolean,default:!1},displayType:{type:String,default:"chart"}},emits:["toggle-type","export"],setup(s,{emit:a}){return{__sfc:!0,props:s,emits:a,handleShowChart:()=>{a("toggle-type","chart")},handleShowTable:()=>{a("toggle-type","table")},handleExport:()=>{a("export")}}}};var ye=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",{staticClass:"card-chart"},[t("div",{staticClass:"head flex items-center h-54px text-sm text-[#34395E] px-20px"},[t("div",{staticClass:"font-bold"},[a._v(a._s(a.title))]),t("div",{staticClass:"title-describe ml-10px"},[a._v(a._s(a.describe))])]),a.showChart||a.showTable||a.showExport||a.$slots["operation-left"]?t("div",{staticClass:"operation flex items-center justify-between px-20px h-36px mb-14px"},[t("div",{staticClass:"left"},[a._t("operation-left")],2),t("div",{staticClass:"right"},[t("div",{staticClass:"flex gap-10px"},[a.showChart||a.showTable?t("div",{staticClass:"flex items-center border border-[#D4D7DF] h-30px rounded-15px w-84px bg-[#F2F4F5]"},[a.showChart?t("div",{staticClass:"flex-1 flex items-center justify-center h-28px rounded-14px cursor-pointer hover:bg-white",class:{active:a.displayType==="chart"},on:{click:e.handleShowChart}},[t("d-icon",{staticClass:"text-[#2D3240]",attrs:{name:"el-icon-vip-tubiao"}})],1):a._e(),a.showTable?t("div",{staticClass:"flex-1 flex items-center justify-center h-28px rounded-14px cursor-pointer hover:bg-white",class:{active:a.displayType==="table"},on:{click:e.handleShowTable}},[t("d-icon",{staticClass:"text-[#2D3240]",attrs:{name:"el-icon-vip-biaoge1"}})],1):a._e()]):a._e(),a.showExport?t("div",{staticClass:"flex-1 flex items-center justify-center h-30px w-30px rounded-15px cursor-pointer hover:bg-white bg-[#F2F4F5]"},[t("d-icon",{staticClass:"text-[#6777EF]",attrs:{name:"el-icon-vip-fenxiang3"},on:{click:e.handleExport}})],1):a._e()])])]):a._e(),t("div",{staticClass:"w-full px-20px pb-20px"},[a._t("default")],2)])},he=[],be=b(me,ye,he,!1,null,"8472e613",null,null);const k=be.exports;const xe={__name:"organization-info",setup(s){const a=o([]),t=o(!1),e=async()=>{var c,u;t.value=!0;const n=await $.organizationApi.getOrganizationInfo(),r=((u=(c=n==null?void 0:n.list)==null?void 0:c[0])==null?void 0:u.detail)||"";a.value=r.split("<br/>"),t.value=!1};return(async()=>await e())(),{__sfc:!0,organizationInfo:a,isOrganizationInfoLoading:t,getOrganizationInfo:e}}};var De=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isOrganizationInfoLoading,expression:"isOrganizationInfoLoading"}],staticClass:"text-sm text-[#3C4B5D]"},a._l(e.organizationInfo,function(n,r){return t("div",{key:r,staticClass:"indent-2em"},[a._v(" "+a._s(n)+" ")])}),0)},Ce=[],we=b(xe,De,Ce,!1,null,"e23f28b2",null,null);const Te=we.exports;const ke={__name:"card-indicator",props:{data:{type:Object,required:!0}},setup(s){return{__sfc:!0,props:s,toThousandsSeparator:V}}};var Ae=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",{staticClass:"h-80px bg-[#F7F8FE] flex flex-col justify-center items-center rounded-3px"},[t("div",{staticClass:"indicator-value flex justify-center items-center"},[t("div",{staticClass:"text-[#34395E] font-bold text-2xl"},[a._v(a._s(e.toThousandsSeparator(a.data.indicator_value)))]),a.data.indicator_ratio?t("div",{staticClass:"text-[#404040] text-base ml-2px"},[a._v(" /"+a._s(a.data.indicator_ratio)+"% ")]):a._e()]),t("div",{staticClass:"w-full indicator-name text-[#404040] text-sm truncate flex items-center justify-center"},[t("span",[a._v(a._s(a.data.indicator_label)+"（"+a._s(a.data.unit)+"）")]),a.data.remark?t("el-tooltip",{scopedSlots:a._u([{key:"content",fn:function(){return[t("div",[a._v(a._s(a.data.remark))])]},proxy:!0}],null,!1,3667637481)},[t("d-icon",{staticClass:"text-primary",attrs:{name:"el-icon-vip-icon-tishi41"}})],1):a._e()],1)])},$e=[],Le=b(ke,Ae,$e,!1,null,"6753a59c",null,null);const Se=Le.exports,Oe=()=>{const s=o([]),a=o(!1),t=async()=>{try{a.value=!0;const i=await $.organizationApi.getCollectionCount();s.value=i.list,a.value=!1}catch(i){console.log(i)}},e=o([]),n=o(!1),r=async()=>{try{n.value=!0;const i=await $.organizationApi.getKeyCollection();e.value=i.list,n.value=!1}catch(i){console.log(i)}},c=o([]),u=o(!1),l=async()=>{try{u.value=!0;const i=await $.organizationApi.getAchievement();c.value=i.list,u.value=!1}catch(i){console.log(i)}},p=o([]),g=o(!1);return{collectionCountList:s,isCollectionCountListLoading:a,getCollectionCountList:t,keyCollectionList:e,isKeyCollectionListLoading:n,getKeyCollectionList:r,achievementList:c,isAchievementListLoading:u,getAchievementList:l,literatureBehaviorList:p,isLiteratureBehaviorListLoading:g,getLiteratureBehaviorList:async()=>{try{g.value=!0;const i=await $.organizationApi.getLiteratureBehavior();p.value=i.list,g.value=!1}catch(i){console.log(i)}}}};const Fe={__name:"index",setup(s){const{collectionCountList:a,isCollectionCountListLoading:t,getCollectionCountList:e,keyCollectionList:n,isKeyCollectionListLoading:r,getKeyCollectionList:c,achievementList:u,isAchievementListLoading:l,getAchievementList:p,literatureBehaviorList:g,isLiteratureBehaviorListLoading:_,getLiteratureBehaviorList:i}=Oe();return(async()=>(e(),c(),p(),i()))(),{__sfc:!0,collectionCountList:a,isCollectionCountListLoading:t,getCollectionCountList:e,keyCollectionList:n,isKeyCollectionListLoading:r,getKeyCollectionList:c,achievementList:u,isAchievementListLoading:l,getAchievementList:p,literatureBehaviorList:g,isLiteratureBehaviorListLoading:_,getLiteratureBehaviorList:i,CardChart:k,CardIndicator:Se}}};var Ye=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t("div",{staticClass:"organization-info"},[t(e.CardChart,{attrs:{title:"馆藏数量保障概况"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isCollectionCountListLoading,expression:"isCollectionCountListLoading"}],staticClass:"flex gap-5 flex-wrap"},a._l(e.collectionCountList,function(n,r){return t(e.CardIndicator,{key:r,staticClass:"responsive-width",attrs:{data:n}})}),1)]),t(e.CardChart,{attrs:{title:"重要收录保障概况"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isKeyCollectionListLoading,expression:"isKeyCollectionListLoading"}],staticClass:"flex gap-5 flex-wrap"},a._l(e.keyCollectionList,function(n,r){return t(e.CardIndicator,{key:r,staticClass:"responsive-width",attrs:{data:n}})}),1)]),t(e.CardChart,{attrs:{title:"成果保障概况"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isAchievementListLoading,expression:"isAchievementListLoading"}],staticClass:"flex gap-5 flex-wrap"},a._l(e.achievementList,function(n,r){return t(e.CardIndicator,{key:r,staticClass:"responsive-width",attrs:{data:n}})}),1)]),t(e.CardChart,{attrs:{title:"文献行为概况"}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLiteratureBehaviorListLoading,expression:"isLiteratureBehaviorListLoading"}],staticClass:"flex gap-5 flex-wrap"},a._l(e.literatureBehaviorList,function(n,r){return t(e.CardIndicator,{key:r,staticClass:"responsive-width",attrs:{data:n}})}),1)])],1)])},Re=[],je=b(Fe,Ye,Re,!1,null,"cff1b2f7",null,null);const Be=je.exports;function M(s,a={}){const{colors:t=["#6777EF","#B566FF","#36BDFF"],customConfig:e={}}=a,{xAxisData:n,lineData:r}=s,c=r.map((l,p)=>({smooth:!0,name:l.name,type:"line",data:l.data,color:t[p],showSymbol:!1,yAxisIndex:typeof l.yAxisIndex=="number"?l.yAxisIndex:0,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:le(t[p],.3)},{offset:1,color:le(t[p],0)}]}}})),u={tooltip:{trigger:"axis"},legend:{data:r.map(l=>l.name),icon:"circle",bottom:0,itemGap:40,itemWidth:8,itemHeight:8},grid:{top:20,bottom:20,left:0,right:0,containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:n,axisLine:{lineStyle:{color:"#D7DADB",width:2}},axisTick:{show:!0,alignWithLabel:!0,inside:!0},axisLabel:{textStyle:{color:"#909399",fontSize:12}}},yAxis:[{type:"value",axisLabel:{textStyle:{color:"#909399",fontSize:12}},splitLine:{lineStyle:{color:"#D7DADB",type:"dashed"}}},{type:"value",position:"right",axisLabel:{textStyle:{color:"#909399",fontSize:12}},splitLine:{show:!1}}],series:c};return X(u,e)}function W(s,a={}){const{colors:t=["#6777EF","#36BDFF","#F2A940","#62AC00"],horizontal:e=!1,customConfig:n={}}=a,{xAxisData:r,barData:c}=s,u=c.map((p,g)=>({name:p.name,type:"bar",data:p.data,color:t[g%t.length],barMaxWidth:60,itemStyle:{borderRadius:[5,5,5,5]},label:{show:!1,position:e?"right":"top",formatter:_=>V(_.value)},emphasis:{focus:"series"}})),l={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:c.map(p=>p.name),icon:"circle",itemWidth:8,itemHeight:8,bottom:0},grid:{top:10,bottom:30,left:0,right:0,containLabel:!0},xAxis:e?{type:"value",splitLine:{show:!0,lineStyle:{color:"#D7DADB",type:"dashed"}},axisTick:{show:!1},axisLabel:{formatter:p=>V(p),color:"#909399"}}:{type:"category",data:r,axisLine:{show:!0,lineStyle:{color:"#D7DADB",width:2}},axisTick:{show:!1},axisLabel:{rotate:r.length>10?45:0,textStyle:{color:"#909399",fontSize:12}}},yAxis:e?{type:"category",data:r,axisLine:{show:!0,lineStyle:{color:"#D7DADB",width:2}},axisTick:{show:!1},axisLabel:{color:"#909399"}}:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{formatter:p=>V(p),textStyle:{color:"#909399",fontSize:12}},splitLine:{show:!0,lineStyle:{color:"#D7DADB",type:"dashed"}}},series:u};return X(l,n)}function H(s,a={}){const{colors:t=["#6777EF","#36BDFF","#F2A940","#62AC00"],showTotal:e=!0,customConfig:n={}}=a,{pieData:r}=s,c=r.reduce((l,p)=>l+(p.value||0),0),u={tooltip:{trigger:"item",formatter:l=>`${l.data.name}: ${V(l.data.value)}`},legend:{data:r.map(l=>l.name),icon:"circle",itemWidth:8,itemHeight:8,bottom:0,itemGap:10,padding:[20,100],formatter:l=>{var p;return(p=r.find(g=>g.name===l))!=null&&p.value,`${l}`}},grid:{top:20,left:"center",containLabel:!0},series:[{type:"pie",radius:["40%","70%"],center:["50%","40%"],avoidLabelOverlap:!1,label:{show:!0,position:"outside",formatter:l=>`${l.name}: ${l.percent.toFixed(2)}%`},emphasis:{label:{show:!0,fontSize:14,fontWeight:"bold"}},labelLine:{show:!0,length:15,length2:10,smooth:!0},data:r,color:t}]};return e&&(u.title=[{text:"总计",x:"center",top:"32%",textStyle:{color:"#6C757D",fontSize:14}},{text:"............",x:"center",top:"36%",textStyle:{color:"#e5e5e5",fontSize:12}},{text:c,x:"center",top:"41%",textStyle:{fontSize:28,color:"#34395E",foontWeight:"800"}}]),X(u,n)}function Je(s,a={}){const{colors:t=["#6777EF","#36BDFF","#F2A940","#62AC00"],showSymbol:e=!0,symbolSize:n=10,minSymbolSize:r=6,maxSymbolSize:c=50,customConfig:u={}}=a,{scatterData:l}=s,p=[];l.forEach(w=>{w.data.forEach(P=>{Array.isArray(P)&&P.length>=2&&p.push(P[1])})});const g=Math.min(...p),i=Math.max(...p)-g,m=c-r,f=w=>i===0?(r+c)/2:r+m*(w-g)/i,d=l.map((w,P)=>({name:w.name,type:"scatter",data:w.data,symbolSize:i===0?n:function(I){return f(I[1])},symbol:e?"circle":"none",itemStyle:{borderColor:t[P%t.length],borderWidth:1,color:le(t[P%t.length],.5)},emphasis:{focus:"series",itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.3)",borderWidth:2}}})),y='<span style="display:inline-block;margin-right:4px;border-radius:10px;width:8px;height:8px;background-color:rgba(35,85,236,0.2);border-width:1px;border-style:solid;border-color:rgba(35,85,236,0.5);" ></span>',v={tooltip:{trigger:"item",formatter:w=>(console.log(w),`${y}<span style="color:#909399;vertical-align:middle;">数据库</span>
                <span style="font-size:16px;font-weight:bold;color:#333;vertical-align:middle;">${w.seriesName}</span>
                <br/>
                <div style="width:100%;height:1px;background:#EBEEF5;margin-top:12px;margin-bottom:12px;"></div>
                ${y}<span style="color:#909399;vertical-align:middle;">不重复文献</span>
                <span style="font-size:16px;font-weight:bold;color:#333333;vertical-align:middle;">${V(w.value[1])}</span>`)},legend:{data:l.map(w=>w.name),icon:"circle",itemWidth:10,itemHeight:10,bottom:0},grid:{top:20,bottom:20,left:0,right:0,containLabel:!0},xAxis:{type:"category",scale:!0,axisLine:{show:!0,lineStyle:{color:"#D7DADB",width:2}},axisLabel:{textStyle:{color:"#909399"}},axisTick:{show:!0,inside:!0},splitLine:{lineStyle:{type:"dashed"}}},yAxis:{type:"value",scale:!0,axisLine:{show:!1},axisTick:{show:!1},axisLabel:{formatter:w=>V(w),textStyle:{color:"#909399",fontSize:12}},splitLine:{lineStyle:{type:"dashed"}}},series:d};return X(v,u)}function Pe(s,a={}){const{colors:t=["#6777EF","#36BDFF","#F2A940","#62AC00"],sort:e="descending",gap:n=2,customConfig:r={}}=a,{funnelData:c}=s,u={tooltip:{trigger:"item",formatter:l=>`${l.data.name}: ${l.data.value}`},legend:{data:c.map(l=>l.name),icon:"circle",itemWidth:8,itemHeight:8,bottom:0},series:[{name:"漏斗图",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:Math.max(...c.map(l=>l.value))*1.1,minSize:"0%",maxSize:"100%",sort:e,gap:n,label:{show:!0,position:"inside",formatter:l=>`${l.data.name}: ${l.value}`,fontSize:14,color:"#fff"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{fontSize:16,fontWeight:"bold"}},data:c,color:t}]};return X(u,r)}function pe(s,a={}){const{colors:t=["#6777EF"],waveAnimation:e=!0,amplitude:n=20,waveSpeed:r=3e3,customConfig:c={}}=a,{value:u,text:l}=s,p=m=>typeof m!="number"?0:Math.max(0,Math.min(1,m)),g=Array.isArray(u)?u.map(p):[p(u)],_=Array.isArray(t)?t:[t],i={series:[{type:"liquidFill",radius:"80%",center:["50%","50%"],data:g.map((m,f)=>({value:m,itemStyle:{color:_[f%_.length],opacity:.8-f*.1},waveAnimation:e})),amplitude:n,waveAnimation:e,animationDuration:500,animationDurationUpdate:1e3,period:r,waveLength:"80%",waveHeight:30,wavesNumber:3,outline:{show:!0,borderDistance:5,itemStyle:{borderColor:_[0],borderWidth:2}},backgroundStyle:{color:"rgba(255, 255, 255, 0.1)"},label:{show:!0,position:["50%","50%"],formatter:l||function(m){return(m.value*100).toFixed(2)+"%"},fontSize:30,fontWeight:"bold",color:_[0]},animation:!0}]};return X(i,c)}function q(s,a={}){const{colors:t=["#6777EF","#00ABFF","#B566FF","#2067FF","#D8A500","#3877FF","#00BEBE","#636BFF"],showLabel:e=!0,levels:n=1,roam:r=!1,customConfig:c={}}=a,{treeData:u}=s,l={tooltip:{show:!0},series:[{type:"treemap",data:u.map((p,g)=>({...p,itemStyle:{color:t[g%t.length]}})),visibleMin:300,label:{show:e,formatter:"{b}: {c}",position:"inside",fontSize:14,color:"#fff"},emphasis:{label:{show:!0,fontSize:16,fontWeight:"bold"},itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}},roam:r,animationDuration:1e3,animationEasing:"quinticInOut"}]};return X(l,c)}function ie(s,a={}){const{colors:t=["#6777EF","#36BDFF","#F2A940","#62AC00"],sizeRange:e=[12,60],rotationRange:n=[-90,90],shape:r="pentagon",customConfig:c={}}=a,{wordcloudData:u}=s,l={series:[{type:"wordCloud",shape:r,left:"center",top:"center",width:"100%",height:"100%",textStyle:{fontWeight:"normal",color:function(){return t[Math.floor(Math.random()*t.length)]}},sizeRange:e,rotationRange:n,rotationStep:45,gridSize:8,drawOutOfBound:!1,layoutAnimation:!0,data:u.map(p=>({name:p.name,value:p.value,emphasis:{textStyle:{fontWeight:"bold",opacity:1}}}))}],tooltip:{show:!0,trigger:"item",formatter:function(p){return`${p.name}: ${p.value}`}},animation:!0,animationDuration:1e3,animationEasing:"cubicOut",animationDelay:function(p){return p*100}};return X(l,c)}const Ne=()=>{const s=o([]),a=o(!1);return{databaseData:s,isDatabaseDataLoading:a,getDatabaseData:async e=>{a.value=!0;try{const n={startTime:e[0],endTime:e[1]},r=await $.organizationApi.getDatabase(n);s.value=r.list}catch(n){console.log(n)}finally{a.value=!1}}}},Me=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"数据库数量（个）",prop:"dbcount",sortable:!0},{label:"数据库经费（万元）",prop:"dbamt",sortable:!0}],ze=[{label:"数据库",prop:"dbname",sortable:!0},{label:"文献类型",prop:"docname",sortable:!0},{label:"语言",prop:"language",sortable:!0,align:"center"},{label:"文献数量",prop:"indicator_total",sortable:!0},{label:"不重复文献",prop:"indicator_value",sortable:!0},{label:"不重复率",prop:"indicator_ratio",sortable:!0},{label:"重复文献",prop:"indicator_sec_value",sortable:!0},{label:"重复率",prop:"indicator_sec_ratio",sortable:!0}],Ie=[{label:"数据库",prop:"dbname",sortable:!0},{label:"文献类型",prop:"docname",sortable:!0},{label:"语言",prop:"language",sortable:!0,align:"center"},{label:"文献数量",prop:"indicator_total",sortable:!0},{label:"非OA文献",prop:"indicator_value",sortable:!0},{label:"非OA率",prop:"indicator_ratio",sortable:!0},{label:"OA文献",prop:"indicator_sec_value",sortable:!0},{label:"OA率",prop:"indicator_sec_ratio",sortable:!0}];var de={exports:{}};(function(s,a){(function(t,e){s.exports=e()})(_e,function(){var t=1e3,e=6e4,n=36e5,r="millisecond",c="second",u="minute",l="hour",p="day",g="week",_="month",i="quarter",m="year",f="date",d="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,v=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,w={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(A){var C=["th","st","nd","rd"],x=A%100;return"["+A+(C[(x-20)%10]||C[x]||C[0])+"]"}},P=function(A,C,x){var T=String(A);return!T||T.length>=C?A:""+Array(C+1-T.length).join(x)+A},I={s:P,z:function(A){var C=-A.utcOffset(),x=Math.abs(C),T=Math.floor(x/60),D=x%60;return(C<=0?"+":"-")+P(T,2,"0")+":"+P(D,2,"0")},m:function A(C,x){if(C.date()<x.date())return-A(x,C);var T=12*(x.year()-C.year())+(x.month()-C.month()),D=C.clone().add(T,_),L=x-D<0,S=C.clone().add(T+(L?-1:1),_);return+(-(T+(x-D)/(L?D-S:S-D))||0)},a:function(A){return A<0?Math.ceil(A)||0:Math.floor(A)},p:function(A){return{M:_,y:m,w:g,d:p,D:f,h:l,m:u,s:c,ms:r,Q:i}[A]||String(A||"").toLowerCase().replace(/s$/,"")},u:function(A){return A===void 0}},ee="en",Q={};Q[ee]=w;var ce="$isDayjsObject",re=function(A){return A instanceof se||!(!A||!A[ce])},ne=function A(C,x,T){var D;if(!C)return ee;if(typeof C=="string"){var L=C.toLowerCase();Q[L]&&(D=L),x&&(Q[L]=x,D=L);var S=C.split("-");if(!D&&S.length>1)return A(S[0])}else{var Y=C.name;Q[Y]=C,D=Y}return!T&&D&&(ee=D),D||!T&&ee},j=function(A,C){if(re(A))return A.clone();var x=typeof C=="object"?C:{};return x.date=A,x.args=arguments,new se(x)},F=I;F.l=ne,F.i=re,F.w=function(A,C){return j(A,{locale:C.$L,utc:C.$u,x:C.$x,$offset:C.$offset})};var se=function(){function A(x){this.$L=ne(x.locale,null,!0),this.parse(x),this.$x=this.$x||x.x||{},this[ce]=!0}var C=A.prototype;return C.parse=function(x){this.$d=function(T){var D=T.date,L=T.utc;if(D===null)return new Date(NaN);if(F.u(D))return new Date;if(D instanceof Date)return new Date(D);if(typeof D=="string"&&!/Z$/i.test(D)){var S=D.match(y);if(S){var Y=S[2]-1||0,R=(S[7]||"0").substring(0,3);return L?new Date(Date.UTC(S[1],Y,S[3]||1,S[4]||0,S[5]||0,S[6]||0,R)):new Date(S[1],Y,S[3]||1,S[4]||0,S[5]||0,S[6]||0,R)}}return new Date(D)}(x),this.init()},C.init=function(){var x=this.$d;this.$y=x.getFullYear(),this.$M=x.getMonth(),this.$D=x.getDate(),this.$W=x.getDay(),this.$H=x.getHours(),this.$m=x.getMinutes(),this.$s=x.getSeconds(),this.$ms=x.getMilliseconds()},C.$utils=function(){return F},C.isValid=function(){return this.$d.toString()!==d},C.isSame=function(x,T){var D=j(x);return this.startOf(T)<=D&&D<=this.endOf(T)},C.isAfter=function(x,T){return j(x)<this.startOf(T)},C.isBefore=function(x,T){return this.endOf(T)<j(x)},C.$g=function(x,T,D){return F.u(x)?this[T]:this.set(D,x)},C.unix=function(){return Math.floor(this.valueOf()/1e3)},C.valueOf=function(){return this.$d.getTime()},C.startOf=function(x,T){var D=this,L=!!F.u(T)||T,S=F.p(x),Y=function(Z,N){var K=F.w(D.$u?Date.UTC(D.$y,N,Z):new Date(D.$y,N,Z),D);return L?K:K.endOf(p)},R=function(Z,N){return F.w(D.toDate()[Z].apply(D.toDate("s"),(L?[0,0,0,0]:[23,59,59,999]).slice(N)),D)},B=this.$W,J=this.$M,z=this.$D,U="set"+(this.$u?"UTC":"");switch(S){case m:return L?Y(1,0):Y(31,11);case _:return L?Y(1,J):Y(0,J+1);case g:var G=this.$locale().weekStart||0,ae=(B<G?B+7:B)-G;return Y(L?z-ae:z+(6-ae),J);case p:case f:return R(U+"Hours",0);case l:return R(U+"Minutes",1);case u:return R(U+"Seconds",2);case c:return R(U+"Milliseconds",3);default:return this.clone()}},C.endOf=function(x){return this.startOf(x,!1)},C.$set=function(x,T){var D,L=F.p(x),S="set"+(this.$u?"UTC":""),Y=(D={},D[p]=S+"Date",D[f]=S+"Date",D[_]=S+"Month",D[m]=S+"FullYear",D[l]=S+"Hours",D[u]=S+"Minutes",D[c]=S+"Seconds",D[r]=S+"Milliseconds",D)[L],R=L===p?this.$D+(T-this.$W):T;if(L===_||L===m){var B=this.clone().set(f,1);B.$d[Y](R),B.init(),this.$d=B.set(f,Math.min(this.$D,B.daysInMonth())).$d}else Y&&this.$d[Y](R);return this.init(),this},C.set=function(x,T){return this.clone().$set(x,T)},C.get=function(x){return this[F.p(x)]()},C.add=function(x,T){var D,L=this;x=Number(x);var S=F.p(T),Y=function(J){var z=j(L);return F.w(z.date(z.date()+Math.round(J*x)),L)};if(S===_)return this.set(_,this.$M+x);if(S===m)return this.set(m,this.$y+x);if(S===p)return Y(1);if(S===g)return Y(7);var R=(D={},D[u]=e,D[l]=n,D[c]=t,D)[S]||1,B=this.$d.getTime()+x*R;return F.w(B,this)},C.subtract=function(x,T){return this.add(-1*x,T)},C.format=function(x){var T=this,D=this.$locale();if(!this.isValid())return D.invalidDate||d;var L=x||"YYYY-MM-DDTHH:mm:ssZ",S=F.z(this),Y=this.$H,R=this.$m,B=this.$M,J=D.weekdays,z=D.months,U=D.meridiem,G=function(N,K,te,oe){return N&&(N[K]||N(T,L))||te[K].slice(0,oe)},ae=function(N){return F.s(Y%12||12,N,"0")},Z=U||function(N,K,te){var oe=N<12?"AM":"PM";return te?oe.toLowerCase():oe};return L.replace(v,function(N,K){return K||function(te){switch(te){case"YY":return String(T.$y).slice(-2);case"YYYY":return F.s(T.$y,4,"0");case"M":return B+1;case"MM":return F.s(B+1,2,"0");case"MMM":return G(D.monthsShort,B,z,3);case"MMMM":return G(z,B);case"D":return T.$D;case"DD":return F.s(T.$D,2,"0");case"d":return String(T.$W);case"dd":return G(D.weekdaysMin,T.$W,J,2);case"ddd":return G(D.weekdaysShort,T.$W,J,3);case"dddd":return J[T.$W];case"H":return String(Y);case"HH":return F.s(Y,2,"0");case"h":return ae(1);case"hh":return ae(2);case"a":return Z(Y,R,!0);case"A":return Z(Y,R,!1);case"m":return String(R);case"mm":return F.s(R,2,"0");case"s":return String(T.$s);case"ss":return F.s(T.$s,2,"0");case"SSS":return F.s(T.$ms,3,"0");case"Z":return S}return null}(N)||S.replace(":","")})},C.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},C.diff=function(x,T,D){var L,S=this,Y=F.p(T),R=j(x),B=(R.utcOffset()-this.utcOffset())*e,J=this-R,z=function(){return F.m(S,R)};switch(Y){case m:L=z()/12;break;case _:L=z();break;case i:L=z()/3;break;case g:L=(J-B)/6048e5;break;case p:L=(J-B)/864e5;break;case l:L=J/n;break;case u:L=J/e;break;case c:L=J/t;break;default:L=J}return D?L:F.a(L)},C.daysInMonth=function(){return this.endOf(_).$D},C.$locale=function(){return Q[this.$L]},C.locale=function(x,T){if(!x)return this.$L;var D=this.clone(),L=ne(x,T,!0);return L&&(D.$L=L),D},C.clone=function(){return F.w(this.$d,this)},C.toDate=function(){return new Date(this.valueOf())},C.toJSON=function(){return this.isValid()?this.toISOString():null},C.toISOString=function(){return this.$d.toISOString()},C.toString=function(){return this.$d.toUTCString()},A}(),ue=se.prototype;return j.prototype=ue,[["$ms",r],["$s",c],["$m",u],["$H",l],["$W",p],["$M",_],["$y",m],["$D",f]].forEach(function(A){ue[A[1]]=function(C){return this.$g(C,A[0],A[1])}}),j.extend=function(A,C){return A.$i||(A(C,se,j),A.$i=!0),j},j.locale=ne,j.isDayjs=re,j.unix=function(A){return j(1e3*A)},j.en=Q[ee],j.Ls=Q,j.p={},j})})(de);var Ee=de.exports;const O=ve(Ee);const We={__name:"database",setup(s){const a=O(),t=O().subtract(5,"year"),e=o([t.format("YYYY-MM-DD"),a.format("YYYY-MM-DD")]),{databaseData:n,isDatabaseDataLoading:r,getDatabaseData:c}=Ne(),u=o("chart"),l=d=>{u.value=d},p=o({shortcuts:[{text:"近3年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-3),d.$emit("pick",[v,y])}},{text:"近5年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-5),d.$emit("pick",[v,y])}}]}),g=d=>{c(e.value)},_=h(()=>[{name:"数据库数量（个）",data:n.value.map(d=>{const y=Number(d.dbcount);return isNaN(y)?0:y})},{name:"数据库经费（万元）",data:n.value.map(d=>{const y=Number(d.dbamt);return isNaN(y)?0:y})}]),i=h(()=>n.value.map(d=>d.year)),m=h(()=>{const d={xAxisData:i.value,lineData:_.value};return M(d,{customConfig:{}})}),f=h(()=>n.value);return(async()=>c(e.value))(),{__sfc:!0,now:a,fiveYearsAgo:t,timeRange:e,databaseData:n,isDatabaseDataLoading:r,getDatabaseData:c,displayType:u,handleToggleType:l,pickerOptions:p,handleDateChange:g,chartData:_,xAxisData:i,lineChartData:m,databaseTableData:f,CardChart:k,databaseColumns:Me}}};var Ve=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"数据库",describe:"馆藏数据库数量以及经费","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.handleDateChange},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseDataLoading,expression:"isDatabaseDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseDataLoading,expression:"isDatabaseDataLoading"}],attrs:{data:e.databaseTableData,columns:e.databaseColumns,"null-text":"-"},scopedSlots:a._u([{key:"dbamt-th",fn:function(n){return[t("span",[a._v(a._s(n.column.label))]),t("el-tooltip",{scopedSlots:a._u([{key:"content",fn:function(){return[t("div",[a._v("数据库的合同金额")])]},proxy:!0}],null,!0)},[t("d-icon",{staticClass:"text-primary align-middle",attrs:{name:"el-icon-vip-icon-tishi41"}})],1)]}}],null,!1,2235703315)}):a._e()],1)},He=[],Ke=b(We,Ve,He,!1,null,"8f9fcc85",null,null);const Xe=Ke.exports,qe=()=>{const s=o([]),a=o(!1);return{procurementData:s,isProcurementDataLoading:a,getProcurementData:async({year:e,language:n})=>{a.value=!0;try{const r=await $.organizationApi.getProcurement({year:e,language:n});s.value=r.list}catch(r){console.log(r)}finally{a.value=!1}}}};const Qe={__name:"procurement",setup(s){const{procurementData:a,isProcurementDataLoading:t,getProcurementData:e}=qe(),n=E.useSelect(),r=new Date().getFullYear().toString(),c=o(r),u=o(""),l=f=>{m()},p=f=>{m()},g=h(()=>a.value),_=h(()=>g.value.map(f=>({name:f.name,value:f.value}))),i=h(()=>H({pieData:_.value},{showTotal:!1,customConfig:{series:[{radius:["0%","70%"],emphasis:{label:{show:!1}}}]}})),m=()=>{e(Object.fromEntries(Object.entries({year:c.value,language:u.value}).filter(([,f])=>f)))};return(async()=>m())(),{__sfc:!0,procurementData:a,isProcurementDataLoading:t,getProcurementData:e,selectStore:n,currentYear:r,year:c,language:u,handleYearChange:l,handleLanguageChange:p,sourceData:g,pieData:_,procurementStauts:i,getData:m,CardChart:k}}};var Ge=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"数据库采购状态分布",describe:"馆藏数据库采购状态的分布情况","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.handleYearChange},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.handleLanguageChange},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isProcurementDataLoading,expression:"isProcurementDataLoading"}],staticClass:"h-300px",attrs:{option:e.procurementStauts}})],1)},Ze=[],Ue=b(Qe,Ge,Ze,!1,null,"605708fc",null,null);const ea=Ue.exports,aa=()=>{const s=o([]),a=o(!1);return{languageData:s,isLanguageDataLoading:a,getLanguageData:async e=>{a.value=!0;try{const n=await $.organizationApi.getLanguage({year:e});s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const ta={__name:"language",setup(s){const{languageData:a,isLanguageDataLoading:t,getLanguageData:e}=aa(),n=new Date().getFullYear().toString(),r=o(n),c=()=>{e(r.value)},u=h(()=>a.value),l=h(()=>u.value.map(g=>({name:g.name,value:g.value}))),p=h(()=>H({pieData:l.value},{customConfig:{}}));return(async()=>e(r.value))(),{__sfc:!0,languageData:a,isLanguageDataLoading:t,getLanguageData:e,currentYear:n,year:r,handleYearChange:c,sourceData:u,pieData:l,procurementStauts:p,CardChart:k}}};var na=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"数据库语言类型分布",describe:"馆藏数据库语言的分布情况","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("el-date-picker",{attrs:{type:"year","value-format":"yyyy",placeholder:"请选择年份",clearable:!1},on:{change:e.handleYearChange},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})]},proxy:!0}])},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.procurementStauts}})],1)},sa=[],oa=b(ta,na,sa,!1,null,"7898f968",null,null);const ra=oa.exports,la=()=>{const s=o([]),a=o(!1);return{literatureData:s,isLiteratureDataLoading:a,getLiteratureData:async e=>{a.value=!0;try{const n=await $.organizationApi.getLiterature(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const ia={__name:"literature",setup(s){const a=E.useSelect(),{literatureData:t,isLiteratureDataLoading:e,getLiteratureData:n}=la(),r=new Date().getFullYear().toString(),c=o(r),u=o(""),l=o(""),p=()=>{n(Object.fromEntries(Object.entries({year:c.value,language:l.value,orderType:u.value}).filter(([,m])=>m)))};(async()=>p())();const g=h(()=>t.value.map(m=>m.name)),_=h(()=>[{name:"数据库数量",data:t.value.map(m=>m.value)}]),i=h(()=>W({xAxisData:g.value,barData:_.value},{customConfig:{}}));return{__sfc:!0,selectStore:a,literatureData:t,isLiteratureDataLoading:e,getLiteratureData:n,currentYear:r,year:c,orderType:u,language:l,getData:p,categories:g,seriesData:_,verticalBarOption:i,CardChart:k}}};var ca=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"数据库文献类型分布",describe:"馆藏数据库文献类型的分布情况","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择采购状态",clearable:""},on:{change:e.getData},model:{value:e.orderType,callback:function(n){e.orderType=n},expression:"orderType"}},a._l(e.selectStore.selects.order_type,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLiteratureDataLoading,expression:"isLiteratureDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)},ua=[],pa=b(ia,ca,ua,!1,null,"2bf8587d",null,null);const da=pa.exports,_a=()=>{const s=o([]),a=o(!1);return{subjectData:s,isSubjectDataLoading:a,getSubjectData:async e=>{a.value=!0;try{const n=await $.organizationApi.getSubject(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const va={__name:"subject",setup(s){const a=E.useSelect(),{subjectData:t,isSubjectDataLoading:e,getSubjectData:n}=_a(),r=new Date().getFullYear().toString(),c=o(r),u=o(""),l=o(""),p=()=>{n(Object.fromEntries(Object.entries({year:c.value,language:l.value,orderType:u.value}).filter(([,m])=>m)))};(async()=>p())();const g=h(()=>t.value.map(m=>m.name)),_=h(()=>[{name:"数据库数量",data:t.value.map(m=>m.value)}]),i=h(()=>W({xAxisData:g.value,barData:_.value},{customConfig:{}}));return{__sfc:!0,selectStore:a,subjectData:t,isSubjectDataLoading:e,getSubjectData:n,currentYear:r,year:c,orderType:u,language:l,getData:p,categories:g,seriesData:_,verticalBarOption:i,CardChart:k}}};var ga=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"数据库学科类型分布",describe:"馆藏数据库教育部学科分类（门类级）的分布情况","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择采购状态",clearable:""},on:{change:e.getData},model:{value:e.orderType,callback:function(n){e.orderType=n},expression:"orderType"}},a._l(e.selectStore.selects.order_type,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)},fa=[],ma=b(va,ga,fa,!1,null,"b9d4cc16",null,null);const ya=ma.exports,ha=()=>{const s=o([]),a=o(!1);return{nonRepetitiveData:s,isNonRepetitiveDataLoading:a,getNonRepetitiveData:async e=>{a.value=!0;try{const n=await $.organizationApi.getNonRepetitive(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const ba={__name:"non-repetitive",setup(s){const a=E.useSelect(),{nonRepetitiveData:t,isNonRepetitiveDataLoading:e,getNonRepetitiveData:n}=ha(),r=new Date().getFullYear().toString(),c=o(r),u=o("2"),l=o(""),p=o("chart"),g=f=>{p.value=f},_=()=>{n(Object.fromEntries(Object.entries({year:c.value,language:l.value,docType:u.value}).filter(([,f])=>f)))};(async()=>_())();const i=h(()=>[{name:"数据库",data:t.value.map(f=>[f.dbname,Number(f.indicator_value)])}]),m=h(()=>Je({scatterData:i.value},{colors:["#6777EF","#F2A940"],symbolSize:12,customConfig:{legend:{show:!1}}}));return{__sfc:!0,selectStore:a,nonRepetitiveData:t,isNonRepetitiveDataLoading:e,getNonRepetitiveData:n,currentYear:r,year:c,docType:u,language:l,displayType:p,handleToggleType:g,getData:_,scatterData:i,scatterOption:m,CardChart:k,nonRepetitiveColumns:ze}}};var xa=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"数据库不重复文献分析",describe:"分析馆藏不重复文献的情况，即在馆藏文献中，仅出现过1次。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择文献类型"},on:{change:e.getData},model:{value:e.docType,callback:function(n){e.docType=n},expression:"docType"}},a._l([{label:"期刊",value:"2"},{label:"期刊文献",value:"3"}],function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isNonRepetitiveDataLoading,expression:"isNonRepetitiveDataLoading"}],staticClass:"h-300px",attrs:{option:e.scatterOption}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isNonRepetitiveDataLoading,expression:"isNonRepetitiveDataLoading"}],attrs:{data:e.nonRepetitiveData,columns:e.nonRepetitiveColumns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}},{key:"indicator_sec_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_sec_ratio)+"%")])]}}],null,!1,1524383769)}):a._e()],1)},Da=[],Ca=b(ba,xa,Da,!1,null,"091e5668",null,null);const wa=Ca.exports,Ta=()=>{const s=o([]),a=o(!1);return{oaData:s,isOaDataLoading:a,getOaData:async e=>{a.value=!0;try{const n=await $.organizationApi.getOa(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const ka={__name:"oa",setup(s){const a=E.useSelect(),{oaData:t,isOaDataLoading:e,getOaData:n}=Ta(),r=new Date().getFullYear().toString(),c=o(r),u=o("2"),l=o(""),p=o("chart"),g=y=>{p.value=y},_=()=>{n(Object.fromEntries(Object.entries({year:c.value,language:l.value,docType:u.value}).filter(([,y])=>y)))};(async()=>_())();const i=h(()=>t.value.map(y=>({name:y.dbname,value:Number(y.indicator_value)}))),m=h(()=>q({treeData:i.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),f=o(null);return{__sfc:!0,selectStore:a,oaData:t,isOaDataLoading:e,getOaData:n,currentYear:r,year:c,docType:u,language:l,displayType:p,handleToggleType:g,getData:_,basicTreeData:i,basicTreemapOption:m,clickInfo:f,handleTreemapClick:y=>{y.data&&(f.value={name:y.data.name,value:y.data.value,path:y.treePathInfo.map(v=>v.name).join(" > "),level:y.treePathInfo.length-1,rawData:y.data},console.log("点击了矩形树图节点:",y))},CardChart:k,oaColumns:Ie}}};var Aa=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"数据库OA文献分析",describe:"统计数据库内的OA文献数量，以数据库实际标记为准。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择文献类型"},on:{change:e.getData},model:{value:e.docType,callback:function(n){e.docType=n},expression:"docType"}},a._l([{label:"期刊",value:"2"},{label:"期刊文献",value:"3"}],function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isOaDataLoading,expression:"isOaDataLoading"}],staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isOaDataLoading,expression:"isOaDataLoading"}],attrs:{data:e.oaData,columns:e.oaColumns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}},{key:"indicator_sec_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_sec_ratio)+"%")])]}}],null,!1,1524383769)}):a._e()],1)},$a=[],La=b(ka,Aa,$a,!1,null,"98ccb007",null,null);const Sa=La.exports;const Oa={__name:"index",setup(s){return{__sfc:!0,Database:Xe,Procurement:ea,Language:ra,Literature:da,Subject:ya,NonRepetitive:wa,Oa:Sa}}};var Fa=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.Database,{staticClass:"mb-20px"}),t("div",{staticClass:"flex gap-20px mb-20px"},[t("div",{staticClass:"flex-1 min-w-300px"},[t(e.Procurement)],1),t("div",{staticClass:"flex-1 min-w-300px"},[t(e.Language)],1)]),t("div",{staticClass:"flex gap-20px mb-20px"},[t("div",{staticClass:"flex-1 min-w-300px"},[t(e.Literature)],1),t("div",{staticClass:"flex-1 min-w-300px"},[t(e.Subject)],1)]),t(e.NonRepetitive,{staticClass:"mb-20px"}),t(e.Oa)],1)},Ya=[],Ra=b(Oa,Fa,Ya,!1,null,"ba4fcab1",null,null);const ja=Ra.exports,Ba=()=>{const s=o([]),a=o(!1);return{journalData:s,isJournalDataLoading:a,getJournalData:async e=>{a.value=!0;try{const n=await ge.getJournal(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}},Ja=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"期刊种数",prop:"totalcate",sortable:!0},{label:"期刊册数",prop:"totalcount",sortable:!0},{label:"中文期刊种数",prop:"zhcate",sortable:!0},{label:"中文期刊册数",prop:"zhcount",sortable:!0},{label:"外文期刊种数",prop:"uncate",sortable:!0},{label:"外文期刊册数",prop:"uncount",sortable:!0}],Pa=[{label:"学科",prop:"domain_name",sortable:!0},{label:"期刊数量（种）",prop:"catecount",sortable:!0},{label:"期刊数量（册）",prop:"bookcount",sortable:!0}],Na=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"期刊种数",prop:"totalcate",sortable:!0},{label:"现刊种数",prop:"xkcate",sortable:!0},{label:"过刊种数",prop:"gkcate",sortable:!0},{label:"停刊种数",prop:"tkcate",sortable:!0}];const Ma={__name:"journal",setup(s){const{journalData:a,isJournalDataLoading:t,getJournalData:e}=Ba(),n=O(),r=O().subtract(5,"year"),c=o([r.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),u=o("1"),l=o("1"),p=o("chart"),g=y=>{p.value=y},_=o({shortcuts:[{text:"近3年",onClick(y){const v=new Date,w=new Date;w.setFullYear(w.getFullYear()-3),y.$emit("pick",[w,v])}},{text:"近5年",onClick(y){const v=new Date,w=new Date;w.setFullYear(w.getFullYear()-5),y.$emit("pick",[w,v])}}]}),i=h(()=>[{name:"期刊数量（种）",data:a.value.map(y=>{const v=Number(y.totalcate);return isNaN(v)?0:v})},{name:"期刊数量（册）",data:a.value.map(y=>{const v=Number(y.totalcount);return isNaN(v)?0:v})}]),m=h(()=>a.value.map(y=>y.year)),f=h(()=>{const y={xAxisData:m.value,lineData:i.value};return M(y,{customConfig:{}})}),d=async()=>{await e({startTime:c.value[0],endTime:c.value[1],oaFlag:u.value,distinctFlag:l.value})};return(async()=>d())(),{__sfc:!0,journalData:a,isJournalDataLoading:t,getJournalData:e,now:n,fiveYearsAgo:r,timeRange:c,containOA:u,removeDuplicates:l,displayType:p,handleToggleType:g,pickerOptions:_,chartData:i,xAxisData:m,lineChartData:f,getData:d,CardChart:k,journalColumns:Ja}}};var za=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"期刊",describe:"馆藏期刊数量","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium",clearable:!1,"value-format":"yyyy-MM-dd"},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],attrs:{data:e.journalData,columns:e.journalColumns,"null-text":"-"}}):a._e()],1)},Ia=[],Ea=b(Ma,za,Ia,!1,null,"c6fed1a8",null,null);const Wa=Ea.exports,Va=()=>{const s=o([]),a=o(!1);return{languageData:s,isLanguageDataLoading:a,getLanguageData:async e=>{a.value=!0;try{const n=await $.organizationApi.getJournalLanguage(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const Ha={__name:"language",setup(s){const{languageData:a,isLanguageDataLoading:t,getLanguageData:e}=Va(),n=new Date().getFullYear().toString(),r=o(n),c=o("1"),u=h(()=>a.value.filter(d=>d.showtype==="0")),l=h(()=>u.value.map(d=>({name:d.name,value:d.value||0}))),p=h(()=>H({pieData:l.value},{showTotal:!1,customConfig:{series:[{radius:["0%","70%"],emphasis:{label:{show:!1}}}]}})),g=h(()=>a.value.filter(d=>d.showtype==="1")),_=h(()=>g.value.map(d=>d.name)),i=h(()=>[{name:"期刊数量（种）",data:g.value.map(d=>d.value||0)}]),m=h(()=>W({xAxisData:_.value,barData:i.value},{customConfig:{}})),f=async()=>{await e({year:r.value,oaFlag:c.value})};return(async()=>f())(),{__sfc:!0,languageData:a,isLanguageDataLoading:t,getLanguageData:e,currentYear:n,year:r,containOA:c,sourceData:u,pieData:l,languagePieData:p,type1Data:g,categories:_,seriesData:i,verticalBarOption:m,getData:f,CardChart:k}}};var Ka=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"期刊语言分布",describe:"馆藏中文、外文期刊数量","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"选择年","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[t("div",{staticClass:"flex gap-20px"},[t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.languagePieData}})],1),t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)])])},Xa=[],qa=b(Ha,Ka,Xa,!1,null,"06d180e3",null,null);const Qa=qa.exports,Ga=()=>{const s=o([]),a=o(!1);return{carrierData:s,isCarrierDataLoading:a,getCarrierData:async e=>{a.value=!0;try{const n=await $.organizationApi.getJournalCarrier(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const Za={__name:"carrier",setup(s){const{carrierData:a,isCarrierDataLoading:t,getCarrierData:e}=Ga(),n=new Date().getFullYear().toString(),r=o(n),c=o("1"),u=o("1"),l=h(()=>a.value),p=h(()=>l.value.filter(f=>f.showtype==="0")),g=h(()=>l.value.filter(f=>f.showtype==="1")),_=h(()=>H({pieData:p.value},{showTotal:!1,customConfig:{series:[{radius:["0%","70%"],emphasis:{label:{show:!1}}}]}})),i=h(()=>H({pieData:g.value},{customConfig:{series:[{radius:["40%","70%"],emphasis:{label:{show:!0}}}]}})),m=async()=>{await e({year:r.value,oaFlag:c.value,distinctFlag:u.value})};return(async()=>m())(),{__sfc:!0,carrierData:a,isCarrierDataLoading:t,getCarrierData:e,currentYear:n,year:r,containOA:c,removeDuplicates:u,sourceData:l,pieDataCate:p,pieDataCount:g,oaPieData:_,oaPieDataWithTotal:i,getData:m,CardChart:k}}};var Ua=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"期刊载体分布",describe:"纸质、电子馆藏期刊数量","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[t("div",{staticClass:"flex gap-20px"},[t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isCarrierDataLoading,expression:"isCarrierDataLoading"}],staticClass:"h-300px",attrs:{option:e.oaPieData}})],1),t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isCarrierDataLoading,expression:"isCarrierDataLoading"}],staticClass:"h-300px",attrs:{option:e.oaPieDataWithTotal}})],1)])])},et=[],at=b(Za,Ua,et,!1,null,"15e7bdd2",null,null);const tt=at.exports,nt=()=>{const s=o([]),a=o(!1);return{subjectData:s,isSubjectDataLoading:a,getSubjectData:async e=>{a.value=!0;try{const n=await $.organizationApi.getJournalSubject(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const st={__name:"subject",setup(s){const a=E.useSelect(),{subjectData:t,isSubjectDataLoading:e,getSubjectData:n}=nt(),r=new Date().getFullYear().toString(),c=o(r),u=o("1"),l=o(""),p=o("1"),g=o("1"),_=o("chart"),i=v=>{_.value=v},m=h(()=>t.value.map(v=>v.domain_name)),f=h(()=>[{name:"期刊数量",data:t.value.map(v=>v.bookcount)}]),d=h(()=>W({xAxisData:m.value,barData:f.value},{customConfig:{}})),y=()=>{n(Object.fromEntries(Object.entries({year:c.value,eduCategory:u.value,language:l.value,oaFlag:p.value,distinctFlag:g.value}).filter(([,v])=>v)))};return(async()=>y())(),{__sfc:!0,selectStore:a,subjectData:t,isSubjectDataLoading:e,getSubjectData:n,currentYear:r,year:c,eduCategory:u,language:l,containOA:p,removeDuplicates:g,displayType:_,handleToggleType:i,categories:m,seriesData:f,verticalBarOption:d,getData:y,CardChart:k,subjectColumns:Pa}}};var ot=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"期刊学科分布",describe:"馆藏不同学科分类下的期刊数量","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择学科分类",clearable:!1},on:{change:e.getData},model:{value:e.eduCategory,callback:function(n){e.eduCategory=n},expression:"eduCategory"}},a._l(e.selectStore.selects.edu_category,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],attrs:{data:e.subjectData,columns:e.subjectColumns,"null-text":"-"}}):a._e()],1)},rt=[],lt=b(st,ot,rt,!1,null,"7cab680a",null,null);const it=lt.exports,ct=()=>{const s=o([]),a=o(!1);return{oaData:s,isOaDataLoading:a,getOaData:async e=>{a.value=!0;try{const n=await $.organizationApi.getJournalOa(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const ut={__name:"oa",setup(s){const a=E.useSelect(),{oaData:t,isOaDataLoading:e,getOaData:n}=ct(),r=new Date().getFullYear().toString(),c=o(r),u=o(""),l=o("1"),p=h(()=>t.value.length===0?0:t.value[0].value),g=h(()=>{const i=(p.value/100).toFixed(4);return pe({value:Number(i),text:"OA期刊占比"},{customConfig:{series:[{label:{formatter:function(m){return`OA期刊占比

`+(m.value*100).toFixed(2)+"%"}}}]}})}),_=()=>{n(Object.fromEntries(Object.entries({year:c.value,language:u.value,distinctFlag:l.value}).filter(([,i])=>i)))};return(async()=>_())(),{__sfc:!0,selectStore:a,oaData:t,isOaDataLoading:e,getOaData:n,currentYear:r,year:c,language:u,removeDuplicates:l,oaPercentage:p,customTextLiquidOption:g,getData:_,CardChart:k}}};var pt=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"OA期刊",describe:"Open Access Journal，公开获取（免费）的期刊。","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isOaDataLoading,expression:"isOaDataLoading"}],staticClass:"h-300px",attrs:{option:e.customTextLiquidOption}})],1)},dt=[],_t=b(ut,pt,dt,!1,null,"54a50b6c",null,null);const vt=_t.exports,gt=()=>{const s=o([]),a=o(!1);return{statusData:s,isStatusDataLoading:a,getStatusData:async e=>{a.value=!0;try{const n=await $.organizationApi.getJournalStatus(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const ft={__name:"status",setup(s){const a=E.useSelect(),{statusData:t,isStatusDataLoading:e,getStatusData:n}=gt(),r=O(),c=O().subtract(5,"year"),u=o([c.format("YYYY-MM-DD"),r.format("YYYY-MM-DD")]),l=o(""),p=o("1"),g=o("1"),_=o("chart"),i=w=>{_.value=w},m=o({shortcuts:[{text:"近3年",onClick(w){const P=new Date,I=new Date;I.setFullYear(I.getFullYear()-3),w.$emit("pick",[I,P])}},{text:"近5年",onClick(w){const P=new Date,I=new Date;I.setFullYear(I.getFullYear()-5),w.$emit("pick",[I,P])}}]}),f=h(()=>[{name:"现刊（种）",data:t.value.map(w=>w.xkcate??0)},{name:"过刊（种）",data:t.value.map(w=>w.gkcate??0)},{name:"停刊（种）",data:t.value.map(w=>w.tkcate??0)}]),d=h(()=>t.value.map(w=>w.year)),y=h(()=>{const w={xAxisData:d.value,lineData:f.value};return M(w,{customConfig:{}})}),v=()=>{n(Object.fromEntries(Object.entries({startTime:u.value[0],endTime:u.value[1],language:l.value,oaFlag:p.value,distinctFlag:g.value}).filter(([,w])=>w)))};return(async()=>v())(),{__sfc:!0,selectStore:a,statusData:t,isStatusDataLoading:e,getStatusData:n,now:r,fiveYearsAgo:c,timeRange:u,language:l,containOA:p,removeDuplicates:g,displayType:_,handleToggleType:i,pickerOptions:m,chartData:f,xAxisData:d,lineChartData:y,getData:v,CardChart:k,statusColumns:Na}}};var mt=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"期刊状态分布",describe:"统计现刊、过刊、停刊数量。现刊：非停刊期刊，可访问年份是最近2年的，反之过刊。停刊：注销刊号的期刊。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium",clearable:!1,"value-format":"yyyy-MM-dd"},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isStatusDataLoading,expression:"isStatusDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isStatusDataLoading,expression:"isStatusDataLoading"}],attrs:{data:e.statusData,columns:e.statusColumns,"null-text":"-"},scopedSlots:a._u([{key:"xkcate-th",fn:function(n){return[t("span",[a._v(a._s(n.column.label))]),t("el-tooltip",{scopedSlots:a._u([{key:"content",fn:function(){return[t("div",[a._v(" 指期刊的可访问年份是最近2年的（ 假设当前日期为2025年3月21日，期刊在2024年1月1日起出版过，并且非停刊） ")])]},proxy:!0}],null,!0)},[t("d-icon",{staticClass:"text-primary align-middle",attrs:{name:"el-icon-vip-icon-tishi41"}})],1)]}},{key:"gkcate-th",fn:function(n){return[t("span",[a._v(a._s(n.column.label))]),t("el-tooltip",{scopedSlots:a._u([{key:"content",fn:function(){return[t("div",[a._v(" 指期刊的可访问年份是最近2年的（ 假设当前日期为2025年3月21日，期刊在2024年1月1日起未出版过，并且非停刊） ")])]},proxy:!0}],null,!0)},[t("d-icon",{staticClass:"text-primary align-middle",attrs:{name:"el-icon-vip-icon-tishi41"}})],1)]}},{key:"tkcate-th",fn:function(n){return[t("span",[a._v(a._s(n.column.label))]),t("el-tooltip",{scopedSlots:a._u([{key:"content",fn:function(){return[t("div",[a._v("已经注销刊号的期刊")])]},proxy:!0}],null,!0)},[t("d-icon",{staticClass:"text-primary align-middle",attrs:{name:"el-icon-vip-icon-tishi41"}})],1)]}}],null,!1,832403308)}):a._e()],1)},yt=[],ht=b(ft,mt,yt,!1,null,"da8a774f",null,null);const bt=ht.exports;const xt={__name:"index",setup(s){return{__sfc:!0,Journal:Wa,Language:Qa,Carrier:tt,Subject:it,Oa:vt,Status:bt}}};var Dt=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.Journal,{staticClass:"mb-5"}),t(e.Language,{staticClass:"mb-5"}),t(e.Carrier,{staticClass:"mb-5"}),t(e.Subject,{staticClass:"mb-5"}),t("div",{staticClass:"flex gap-5 mb-5"},[t("div",{staticClass:"w-550px max-w-700px"},[t(e.Oa)],1),t("div",{staticClass:"flex-1 min-w-860px"},[t(e.Status)],1)])],1)},Ct=[],wt=b(xt,Dt,Ct,!1,null,"54cd0286",null,null);const Tt=wt.exports,kt=()=>{const s=o([]),a=o(!1);return{bookData:s,isBookDataLoading:a,getBookData:async e=>{a.value=!0;try{const n=await $.organizationApi.getBookCount(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}},At=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"图书种数",prop:"totalcate",sortable:!0},{label:"图书册数",prop:"totalcount",sortable:!0},{label:"中文图书种数",prop:"zhcate",sortable:!0},{label:"中文图书册数",prop:"zhcount",sortable:!0},{label:"外文图书种数",prop:"uncate",sortable:!0},{label:"外文图书册数",prop:"uncount",sortable:!0}];const $t={__name:"book",setup(s){const{bookData:a,isBookDataLoading:t,getBookData:e}=kt(),n=O(),r=O().subtract(5,"year"),c=o([r.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),u=o("1"),l=o("chart"),p=d=>{l.value=d},g=o({shortcuts:[{text:"近3年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-3),d.$emit("pick",[v,y])}},{text:"近5年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-5),d.$emit("pick",[v,y])}}]}),_=h(()=>[{name:"图书种数",data:a.value.map(d=>d.totalcate||0)},{name:"图书册数",data:a.value.map(d=>d.totalcount||0)}]),i=h(()=>a.value.map(d=>d.year)),m=h(()=>{const d={xAxisData:i.value,lineData:_.value};return M(d,{customConfig:{}})}),f=async()=>{await e({startTime:c.value[0],endTime:c.value[1],distinctFlag:u.value})};return(async()=>f())(),{__sfc:!0,bookData:a,isBookDataLoading:t,getBookData:e,now:n,fiveYearsAgo:r,timeRange:c,removeDuplicates:u,displayType:l,handleToggleType:p,pickerOptions:g,chartData:_,xAxisData:i,lineChartData:m,getData:f,CardChart:k,bookColumns:At}}};var Lt=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"图书",describe:"馆藏图书数量","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isBookDataLoading,expression:"isBookDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isBookDataLoading,expression:"isBookDataLoading"}],attrs:{data:e.bookData,columns:e.bookColumns,"null-text":"-"}}):a._e()],1)},St=[],Ot=b($t,Lt,St,!1,null,"33e148e3",null,null);const Ft=Ot.exports,Yt=()=>{const s=o([]),a=o(!1);return{languageData:s,isLanguageDataLoading:a,getLanguageData:async e=>{a.value=!0;try{const n=await $.organizationApi.getBookLanguage(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const Rt={__name:"language",setup(s){const{languageData:a,isLanguageDataLoading:t,getLanguageData:e}=Yt(),n=new Date().getFullYear().toString(),r=o(n),c=h(()=>a.value.filter(f=>f.showtype==="0")),u=h(()=>c.value.map(f=>({name:f.name,value:f.value||0}))),l=h(()=>H({pieData:u.value},{showTotal:!1,customConfig:{series:[{radius:["0%","60%"],emphasis:{label:{show:!1}}}]}})),p=h(()=>a.value.filter(f=>f.showtype==="1")),g=h(()=>p.value.map(f=>f.name)),_=h(()=>[{name:"图书数量（种）",data:p.value.map(f=>f.value||0)}]),i=h(()=>W({xAxisData:g.value,barData:_.value},{customConfig:{}})),m=async()=>{await e({year:r.value})};return(async()=>m())(),{__sfc:!0,languageData:a,isLanguageDataLoading:t,getLanguageData:e,currentYear:n,year:r,sourceData:c,pieData:u,languagePieData:l,type1Data:p,categories:g,seriesData:_,verticalBarOption:i,getData:m,CardChart:k}}};var jt=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"图书语种分布",describe:"中文、外文馆藏图书数量","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)]},proxy:!0}])},[t("div",{staticClass:"flex gap-20px"},[t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.languagePieData}})],1),t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)])])},Bt=[],Jt=b(Rt,jt,Bt,!1,null,"fdc4b101",null,null);const Pt=Jt.exports,Nt=()=>{const s=o([]),a=o(!1);return{carrierData:s,isCarrierDataLoading:a,getCarrierData:async e=>{a.value=!0;try{const n=await $.organizationApi.getBookCarrier(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const Mt={__name:"carrier",setup(s){const{carrierData:a,isCarrierDataLoading:t,getCarrierData:e}=Nt(),n=new Date().getFullYear().toString(),r=o(n),c=o("1"),u=h(()=>a.value.filter(i=>i.showtype==="0").map(i=>({name:i.name,value:i.value}))),l=h(()=>a.value.filter(i=>i.showtype==="1").map(i=>({name:i.name,value:i.value}))),p=h(()=>H({pieData:u.value},{showTotal:!1,customConfig:{series:[{radius:["0%","60%"],emphasis:{label:{show:!1}}}]}})),g=h(()=>H({pieData:l.value},{customConfig:{series:[{radius:["30%","60%"],emphasis:{label:{show:!0}}}]}})),_=async()=>{await e({year:r.value,distinctFlag:c.value})};return(async()=>_())(),{__sfc:!0,carrierData:a,isCarrierDataLoading:t,getCarrierData:e,currentYear:n,year:r,removeDuplicates:c,pieData0:u,pieData1:l,oaPieData:p,oaPieDataWithTotal:g,getData:_,CardChart:k}}};var zt=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"图书载体分布",describe:"纸质、电子馆藏图书数量","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[t("div",{staticClass:"flex gap-20px"},[t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isCarrierDataLoading,expression:"isCarrierDataLoading"}],staticClass:"h-300px",attrs:{option:e.oaPieData}})],1),t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isCarrierDataLoading,expression:"isCarrierDataLoading"}],staticClass:"h-300px",attrs:{option:e.oaPieDataWithTotal}})],1)])])},It=[],Et=b(Mt,zt,It,!1,null,"c6f55041",null,null);const Wt=Et.exports;const Vt={__name:"index",setup(s){return{__sfc:!0,Book:Ft,Language:Pt,Carrier:Wt}}};var Ht=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.Book,{staticClass:"mb-5"}),t(e.Language,{staticClass:"mb-5"}),t(e.Carrier)],1)},Kt=[],Xt=b(Vt,Ht,Kt,!1,null,"654c6c52",null,null);const qt=Xt.exports,Qt=()=>{const s=o([]),a=o(!1);return{journalArticleData:s,isJournalArticleDataLoading:a,getJournalArticleData:async e=>{a.value=!0;try{const n=await $.organizationApi.getArticleCount(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}},Gt=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"期刊文献（篇）",prop:"totalcount",sortable:!0},{label:"中文期刊文献",prop:"zhcount",sortable:!0},{label:"外文期刊文献",prop:"uncount",sortable:!0}],Zt=[{label:"学科",prop:"domain_name",sortable:!0},{label:"期刊文献（篇）",prop:"bookcount",sortable:!0}];const Ut={__name:"journal-article",setup(s){const{journalArticleData:a,isJournalArticleDataLoading:t,getJournalArticleData:e}=Qt(),n=O(),r=O().subtract(5,"year"),c=o([r.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),u=o("1"),l=o("1"),p=o("chart"),g=y=>{p.value=y},_=o({shortcuts:[{text:"近3年",onClick(y){const v=new Date,w=new Date;w.setFullYear(w.getFullYear()-3),y.$emit("pick",[w,v])}},{text:"近5年",onClick(y){const v=new Date,w=new Date;w.setFullYear(w.getFullYear()-5),y.$emit("pick",[w,v])}}]}),i=h(()=>[{name:"期刊文献（篇）",data:a.value.map(y=>y.totalcount||0)}]),m=h(()=>a.value.map(y=>y.year)),f=h(()=>{const y={xAxisData:m.value,lineData:i.value};return M(y,{customConfig:{}})}),d=async()=>{await e({startTime:c.value[0],endTime:c.value[1],oaFlag:u.value,distinctFlag:l.value})};return(async()=>d())(),{__sfc:!0,journalArticleData:a,isJournalArticleDataLoading:t,getJournalArticleData:e,now:n,fiveYearsAgo:r,timeRange:c,containOA:u,removeDuplicates:l,displayType:p,handleToggleType:g,pickerOptions:_,chartData:i,xAxisData:m,lineChartData:f,getData:d,CardChart:k,journalArticleColumns:Gt}}};var en=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"期刊文献",describe:"馆藏电子期刊文献数量。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalArticleDataLoading,expression:"isJournalArticleDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalArticleDataLoading,expression:"isJournalArticleDataLoading"}],attrs:{data:e.journalArticleData,columns:e.journalArticleColumns,"null-text":"-"}}):a._e()],1)},an=[],tn=b(Ut,en,an,!1,null,"83e6bed6",null,null);const nn=tn.exports,sn=()=>{const s=o([]),a=o(!1);return{languageData:s,isLanguageDataLoading:a,getLanguageData:async e=>{a.value=!0;try{const n=await $.organizationApi.getArticleLanguage(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const on={__name:"language",setup(s){const{languageData:a,isLanguageDataLoading:t,getLanguageData:e}=sn(),n=new Date().getFullYear().toString(),r=o(n),c=o("1"),u=o("1"),l=h(()=>a.value.filter(d=>d.showtype==="0").map(d=>({name:d.name,value:d.value}))),p=h(()=>H({pieData:l.value},{showTotal:!1,customConfig:{series:[{radius:["0%","60%"],emphasis:{label:{show:!1}}}]}})),g=h(()=>a.value.filter(d=>d.showtype==="1")),_=h(()=>g.value.map(d=>d.name)),i=h(()=>[{name:"期刊文献（篇）",data:g.value.map(d=>d.value)}]),m=h(()=>W({xAxisData:_.value,barData:i.value},{customConfig:{}})),f=async()=>{await e(Object.fromEntries(Object.entries({year:r.value,oaFlag:c.value,distinctFlag:u.value}).filter(([,d])=>d)))};return f(),{__sfc:!0,languageData:a,isLanguageDataLoading:t,getLanguageData:e,currentYear:n,year:r,containOA:c,removeDuplicates:u,pieData0:l,languagePieData:p,type1Data:g,categories:_,seriesData:i,verticalBarOption:m,getData:f,CardChart:k}}};var rn=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"期刊文献语言分布",describe:"馆藏中文、外文电子期刊文献数量。","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1,"value-format":"yyyy"},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)],1)]},proxy:!0}])},[t("div",{staticClass:"flex gap-20px"},[t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.languagePieData}})],1),t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isLanguageDataLoading,expression:"isLanguageDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}})],1)])])},ln=[],cn=b(on,rn,ln,!1,null,"91800f47",null,null);const un=cn.exports,pn=()=>{const s=o([]),a=o(!1);return{subjectData:s,isSubjectDataLoading:a,getSubjectData:async e=>{a.value=!0;try{const n=await $.organizationApi.getArticleSubject(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const dn={__name:"subject",setup(s){const a=E.useSelect(),{subjectData:t,isSubjectDataLoading:e,getSubjectData:n}=pn(),r=new Date().getFullYear().toString(),c=o(r),u=o("1"),l=o(""),p=o("1"),g=o("1"),_=o("chart"),i=v=>{_.value=v},m=h(()=>t.value.map(v=>v.domain_name)),f=h(()=>[{name:"期刊文献（篇）",data:t.value.map(v=>v.bookcount)}]),d=h(()=>W({xAxisData:m.value,barData:f.value},{customConfig:{}})),y=async()=>{await n({year:c.value,eduCategory:u.value,language:l.value,oaFlag:p.value,distinctFlag:g.value})};return y(),{__sfc:!0,selectStore:a,subjectData:t,isSubjectDataLoading:e,getSubjectData:n,currentYear:r,year:c,subject:u,language:l,containOA:p,removeDuplicates:g,displayType:_,handleToggleType:i,categories:m,seriesData:f,verticalBarOption:d,getData:y,CardChart:k,subjectColumns:Zt}}};var _n=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"期刊文献学科分布",describe:"馆藏不同学科分类下的电子期刊文献数量。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择学科分类",clearable:!1},on:{change:e.getData},model:{value:e.subject,callback:function(n){e.subject=n},expression:"subject"}},a._l(e.selectStore.selects.edu_category,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isSubjectDataLoading,expression:"isSubjectDataLoading"}],attrs:{data:e.subjectData,columns:e.subjectColumns,"null-text":"-"}}):a._e()],1)},vn=[],gn=b(dn,_n,vn,!1,null,"df9f7f7b",null,null);const fn=gn.exports,mn=()=>{const s=o([]),a=o(!1);return{oaData:s,isOaDataLoading:a,getOaData:async e=>{a.value=!0;try{const n=await $.organizationApi.getArticleOa(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const yn={__name:"oa",setup(s){const{oaData:a,isOaDataLoading:t,getOaData:e}=mn(),n=E.useSelect(),r=new Date().getFullYear().toString(),c=o(r),u=o(""),l=o("1"),p=h(()=>a.value.length===0?0:a.value[0].value),g=h(()=>{const i=(p.value/100).toFixed(4);return console.log(i,"v"),pe({value:Number(i),text:"OA期刊文献收录率"},{customConfig:{series:[{label:{fontSize:20,formatter:function(m){return`OA期刊文献收录率
`+(m.value*100).toFixed(2)+"%"}}}]}})}),_=async()=>{await e(Object.fromEntries(Object.entries({year:c.value,language:u.value,distinctFlag:l.value}).filter(([,i])=>i)))};return _(),{__sfc:!0,oaData:a,isOaDataLoading:t,getOaData:e,selectStore:n,currentYear:r,year:c,language:u,removeDuplicates:l,oaPercentage:p,customTextLiquidOption:g,getData:_,CardChart:k}}};var hn=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"OA期刊文献",describe:"Open Access Journal，公开获取（免费）的电子期刊文献。","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择语言",clearable:""},on:{change:e.getData},model:{value:e.language,callback:function(n){e.language=n},expression:"language"}},a._l(e.selectStore.selects.lan,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("去除重复")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.removeDuplicates,callback:function(n){e.removeDuplicates=n},expression:"removeDuplicates"}})],1)])]},proxy:!0}])},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isOaDataLoading,expression:"isOaDataLoading"}],staticClass:"h-300px",attrs:{option:e.customTextLiquidOption}})],1)},bn=[],xn=b(yn,hn,bn,!1,null,"3ab5b896",null,null);const Dn=xn.exports;const Cn={__name:"index",setup(s){return{__sfc:!0,JournalArticle:nn,Language:un,Subject:fn,Oa:Dn}}};var wn=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.JournalArticle,{staticClass:"mb-5"}),t(e.Language,{staticClass:"mb-5"}),t(e.Subject,{staticClass:"mb-5"}),t("div",{staticClass:"flex gap-20px mb-20px"},[t("div",{staticClass:"flex-1 min-w-300px"},[t(e.Oa)],1),t("div",{staticClass:"flex-1 min-w-300px"})])],1)},Tn=[],kn=b(Cn,wn,Tn,!1,null,"cdd23f78",null,null);const An=kn.exports;const $n={__name:"index",setup(s){const a=o([{name:"1",label:"数据库"},{name:"2",label:"期刊"},{name:"3",label:"图书"},{name:"4",label:"期刊文献"}]),t=o("1");return{__sfc:!0,tabs:a,currentTabName:t,handleTabClick:n=>{t.value=n.name},TabDatabase:ja,TabJournal:Tt,TabBook:qt,TabJournalArticle:An}}};var Ln=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t("div",{staticClass:"h-11 flex gap-10px"},a._l(e.tabs,function(n){return t("div",{key:n.name,staticClass:"tab-item cursor-pointer h-9 leading-9 bg-[#F0F4FF] text-[#697FFA] px-4 rounded-3px text-sm",class:{active:n.name===e.currentTabName},on:{click:function(r){return e.handleTabClick(n)}}},[a._v(" "+a._s(n.label)+" ")])}),0),e.currentTabName==="1"?t(e.TabDatabase,{staticClass:"mt-5"}):a._e(),e.currentTabName==="2"?t(e.TabJournal,{staticClass:"mt-5"}):a._e(),e.currentTabName==="3"?t(e.TabBook,{staticClass:"mt-5"}):a._e(),e.currentTabName==="4"?t(e.TabJournalArticle,{staticClass:"mt-5"}):a._e()],1)},Sn=[],On=b($n,Ln,Sn,!1,null,"220d247e",null,null);const Fn=On.exports;const Yn={__name:"collection-info",setup(s){return{__sfc:!0}}};var Rn=function(){var a=this;return a._self._c,a._self._setupProxy,a._m(0)},jn=[function(){var s=this,a=s._self._c;return s._self._setupProxy,a("div",{staticClass:"text-sm text-[#3C4B5D]"},[a("div",{staticClass:"indent-2em"},[s._v(" 从馆藏数量维度进行统计分析，展示本机构馆藏文献的客观数据量，您可以在这里查看到数据库、期刊、期刊文献的馆藏数量变化趋势以及分布情况。 ")])])}],Bn=b(Yn,Rn,jn,!1,null,"1eef17d8",null,null);const Jn=Bn.exports,Pn=()=>{const s=o([]),a=o(!1);return{journalData:s,isJournalDataLoading:a,getJournalData:async e=>{a.value=!0;try{const n=await $.organizationApi.getImportantJournal(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}},Nn=[{label:"重要收录",prop:"indicator_label",sortable:!0},{label:"版本",prop:"data_version",sortable:!0},{label:"期刊总量",prop:"indicator_total",sortable:!0},{label:"期刊保障量",prop:"indicator_value",sortable:!0},{label:"期刊保障率",prop:"indicator_ratio",sortable:!0},{label:"未保障量",prop:"indicator_sec_value",sortable:!0}],Mn=[{label:"重要收录",prop:"data_version",sortable:!0},{label:"期刊总量",prop:"indicator_total",sortable:!0},{label:"馆藏保障量",prop:"indicator_value",sortable:!0},{label:"保障率",prop:"indicator_ratio",sortable:!0},{label:"未保障量",prop:"indicator_sec_ratio",sortable:!0}],zn=[{label:"数据库",prop:"dbname",sortable:!0},{label:"未保障重要收录期刊收录量（种）",prop:"indicator_value",sortable:!0}],In=[{label:"重要收录",prop:"indicator_name",sortable:!0},{label:"版本",prop:"data_version",sortable:!0},{label:"期刊文献总量",prop:"indicator_total",sortable:!0},{label:"馆藏保障量",prop:"indicator_value",sortable:!0},{label:"保障率",prop:"indicator_ratio",sortable:!0}];const En={__name:"journal",setup(s){const{journalData:a,isJournalDataLoading:t,getJournalData:e}=Pn(),n=new Date().getFullYear().toString(),r=o(n),c=o("chart"),u=i=>{c.value=i},l=h(()=>a.value.map(i=>i.indicator_label)),p=h(()=>[{name:"保障率",data:a.value.map(i=>i.indicator_ratio||0)}]),g=h(()=>W({xAxisData:l.value,barData:p.value},{horizontal:!0,customConfig:{tooltip:{formatter:i=>{let m=`${i[0].axisValue}<br/>`;return i.forEach(f=>{m+=`${f.marker} ${f.seriesName}: ${f.value}%<br/>`}),m}},xAxis:{axisLabel:{formatter:i=>V(i)+"%",color:"#909399"}}}})),_=async()=>{await e({year:r.value})};return _(),{__sfc:!0,journalData:a,isJournalDataLoading:t,getJournalData:e,currentYear:n,year:r,displayType:c,handleToggleType:u,categories:l,seriesData:p,verticalBarOption:g,getData:_,CardChart:k,journalColumns:Nn}}};var Wn=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"重要收录期刊保障情况",describe:"常见中外文重要收录期刊的保障量、保障率。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1,"value-format":"yyyy"},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],staticClass:"h-540px",attrs:{option:e.verticalBarOption}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],attrs:{data:e.journalData,columns:e.journalColumns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}}],null,!1,3405306803)}):a._e()],1)},Vn=[],Hn=b(En,Wn,Vn,!1,null,"bf0a44ee",null,null);const Kn=Hn.exports,Xn=()=>{const s=o([]),a=o(!1);return{partitionData:s,isPartitionDataLoading:a,getPartitionData:async e=>{a.value=!0;try{const n=await $.organizationApi.getImportantPartition(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const qn={__name:"partition",setup(s){const a=E.useSelect(),{partitionData:t,isPartitionDataLoading:e,getPartitionData:n}=Xn(),r=new Date().getFullYear().toString(),c=o(r),u=o("1"),l=h(()=>t.value.filter(m=>m.data_type==="0")),p=h(()=>t.value.filter(m=>m.data_type==="1")),g=h(()=>[...l.value.map(m=>({name:m.data_version,value:m.indicator_ratio})),...p.value.map(m=>({name:m.data_version,value:m.indicator_ratio}))]),_=h(()=>Pe({funnelData:g.value},{colors:["#6777EF","#36BDFF","#F2A940","#62AC00","#53B666"],sort:"descending",gap:4})),i=async()=>{await n({year:c.value,quartileCategory:u.value})};return i(),{__sfc:!0,selectStore:a,partitionData:t,isPartitionDataLoading:e,getPartitionData:n,currentYear:r,year:c,partition:u,topData:l,otherData:p,currentPeriodData:g,compareFunnelOption:_,getData:i,CardChart:k,partitionColumns:Mn}}};var Qn=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"重要收录期刊分区保障分析",describe:"常见中外文重要收录期刊分区保障情况。"},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择分区"},on:{change:e.getData},model:{value:e.partition,callback:function(n){e.partition=n},expression:"partition"}},a._l(e.selectStore.selects.core_mark_quartile_category,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)])]},proxy:!0}])},[t("div",{staticClass:"flex gap-20px w-full"},[t("div",{staticClass:"flex-1 min-w-300px"},[t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isPartitionDataLoading,expression:"isPartitionDataLoading"}],staticClass:"h-300px",attrs:{option:e.compareFunnelOption}})],1),t("div",{staticClass:"flex-1 min-w-300px"},[t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isPartitionDataLoading,expression:"isPartitionDataLoading"}],attrs:{data:e.partitionData,columns:e.partitionColumns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}}])})],1)])])},Gn=[],Zn=b(qn,Qn,Gn,!1,null,"04ea31c4",null,null);const Un=Zn.exports,es=()=>{const s=o([]),a=o(!1);return{databaseTop10Data:s,isDatabaseTop10DataLoading:a,getDatabaseTop10Data:async e=>{a.value=!0;try{const n=await $.organizationApi.getImportantDatabaseTop10(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const as={__name:"database-top10",setup(s){const{databaseTop10Data:a,isDatabaseTop10DataLoading:t,getDatabaseTop10Data:e}=es(),n=new Date().getFullYear().toString(),r=o(n),c=o("1"),u=o("chart"),l=f=>{u.value=f},p=h(()=>a.value.map(f=>({name:f.dbname,value:Number(f.indicator_value)}))),g=h(()=>q({treeData:p.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),_=o(null),i=f=>{f.data&&(_.value={name:f.data.name,value:f.data.value,path:f.treePathInfo.map(d=>d.name).join(" > "),level:f.treePathInfo.length-1,rawData:f.data},console.log("点击了矩形树图节点:",f))},m=async()=>{await e({year:r.value,oaFlag:c.value})};return m(),{__sfc:!0,databaseTop10Data:a,isDatabaseTop10DataLoading:t,getDatabaseTop10Data:e,currentYear:n,year:r,containOA:c,displayType:u,handleToggleType:l,basicTreeData:p,basicTreemapOption:g,clickInfo:_,handleTreemapClick:i,getData:m,CardChart:k,databaseTop10Columns:zn}}};var ts=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"未保障重要收录期刊数据库收录TOP10",describe:"收录未保障重要收录期刊数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseTop10DataLoading,expression:"isDatabaseTop10DataLoading"}],staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseTop10DataLoading,expression:"isDatabaseTop10DataLoading"}],attrs:{data:e.databaseTop10Data,columns:e.databaseTop10Columns,"null-text":"-"}}):a._e()],1)},ns=[],ss=b(as,ts,ns,!1,null,"46487dd9",null,null);const os=ss.exports,rs=()=>{const s=o([]),a=o(!1);return{journalArticleData:s,isJournalArticleDataLoading:a,getJournalArticleData:async e=>{a.value=!0;try{const n=await $.organizationApi.getImportantArticle(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const ls={__name:"journal-article",setup(s){const{journalArticleData:a,isJournalArticleDataLoading:t,getJournalArticleData:e}=rs(),n=new Date().getFullYear().toString(),r=o(n),c=o("chart"),u=i=>{c.value=i},l=h(()=>a.value.map(i=>i.indicator_name)),p=h(()=>[{name:"保障率",data:a.value.map(i=>i.indicator_ratio)}]),g=h(()=>W({xAxisData:l.value,barData:p.value},{horizontal:!0,customConfig:{tooltip:{formatter:i=>{let m=`${i[0].axisValue}<br/>`;return i.forEach(f=>{m+=`${f.marker} ${f.seriesName}: ${f.value}%<br/>`}),m}},xAxis:{axisLabel:{formatter:i=>V(i)+"%",color:"#909399"}}}})),_=async()=>{await e({year:r.value})};return _(),{__sfc:!0,journalArticleData:a,isJournalArticleDataLoading:t,getJournalArticleData:e,currentYear:n,year:r,displayType:c,handleToggleType:u,categories:l,seriesData:p,verticalBarOption:g,getData:_,CardChart:k,journalArticleColumns:In}}};var is=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"重要收录期刊文献保障情况",describe:"常见中外文重要数据库收录期刊文献的保障量、保障率。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalArticleDataLoading,expression:"isJournalArticleDataLoading"}],staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalArticleDataLoading,expression:"isJournalArticleDataLoading"}],attrs:{data:e.journalArticleData,columns:e.journalArticleColumns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}}],null,!1,3405306803)}):a._e()],1)},cs=[],us=b(ls,is,cs,!1,null,"d25229bd",null,null);const ps=us.exports;const ds={__name:"index",setup(s){return{__sfc:!0,Journal:Kn,Partition:Un,DatabaseTop10:os,JournalArticle:ps}}};var _s=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.Journal,{staticClass:"mb-5"}),t(e.Partition,{staticClass:"mb-5"}),t(e.DatabaseTop10,{staticClass:"mb-5"}),t(e.JournalArticle,{staticClass:"mb-5"})],1)},vs=[],gs=b(ds,_s,vs,!1,null,"51502a33",null,null);const fs=gs.exports;const ms={__name:"key-collection-info",setup(s){return{__sfc:!0}}};var ys=function(){var a=this;return a._self._c,a._self._setupProxy,a._m(0)},hs=[function(){var s=this,a=s._self._c;return s._self._setupProxy,a("div",{staticClass:"text-sm text-[#3C4B5D]"},[a("div",{staticClass:"indent-2em"},[s._v(" 从馆藏文献的重要收录保障情况进行统计分析，评估本机构馆藏文献。重要收录保障评价仅支持期刊和期刊文献，并且数据范围仅限于：JCR期刊、Scopus来源刊、EI来源刊、ESI来源刊、北大核心期刊、南大核心期刊、中科院分区表来源刊、CSCD来源刊、WOS-A&HCI来源刊、WOS-SSCI来源刊、WOS-SCIE来源刊、WOS-ESCI来源刊\\WOS-A&HCI期刊文献、WOS-SSCI期刊文献、WOS-SCIE期刊文献、WOS-ESCI期刊文献。 ")])])}],bs=b(ms,ys,hs,!1,null,"7cc041d5",null,null);const xs=bs.exports,Ds=()=>{const s=o([]),a=o(!1);return{academicAchievementsData:s,isAcademicAchievementsDataLoading:a,getAcademicAchievementsData:async e=>{a.value=!0;try{const n=await $.organizationApi.getAchievementData(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}},Cs=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"学术成果（篇）",prop:"indicator_total",sortable:!0},{label:"馆藏保障量（篇）",prop:"indicator_value",sortable:!0},{label:"保障率",prop:"indicator_ratio",sortable:!0},{label:"未保障量（篇）",prop:"indicator_sec_value",sortable:!0}],ws=[{label:"数据库",prop:"dbname",sortable:!0},{label:"学术成果收录量（篇）",prop:"indicator_value",sortable:!0},{label:"收录率",prop:"indicator_ratio",sortable:!0},{label:"是否保障",prop:"is_cover",sortable:!0}],Ts=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"发文期刊（种）",prop:"indicator_total",sortable:!0},{label:"馆藏保障量（种）",prop:"indicator_value",sortable:!0},{label:"保障率",prop:"indicator_ratio",sortable:!0},{label:"未保障量（种）",prop:"indicator_sec_value",sortable:!0}],ks=[{label:"数据库",prop:"dbname",sortable:!0},{label:"发文期刊收录量（种）",prop:"indicator_value",sortable:!0},{label:"收录率",prop:"indicator_ratio",sortable:!0},{label:"是否保障",prop:"is_cover",sortable:!0}],As=[{label:"刊名",prop:"title",sortable:!0},{label:"ISSN",prop:"issn",sortable:!0},{label:"发文量",prop:"indicator_value",sortable:!0},{label:"是否保障",prop:"is_cover",sortable:!0},{label:"收录数据库",prop:"dbname",sortable:!0}];const $s={__name:"academic-achievements",setup(s){const{academicAchievementsData:a,isAcademicAchievementsDataLoading:t,getAcademicAchievementsData:e}=Ds(),n=O(),r=O().subtract(5,"year"),c=o([r.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),u=o("1"),l=o("chart"),p=d=>{l.value=d},g=o({shortcuts:[{text:"近3年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-3),d.$emit("pick",[v,y])}},{text:"近5年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-5),d.$emit("pick",[v,y])}}]}),_=h(()=>[{name:"学术成果（篇）",data:a.value.map(d=>d.indicator_total??0)},{name:"学术成果保障量（篇）",data:a.value.map(d=>d.indicator_value??0)},{name:"学术成果保障率 （%）",data:a.value.map(d=>d.indicator_ratio??0),yAxisIndex:1}]),i=h(()=>a.value.map(d=>d.year)),m=h(()=>{const d={xAxisData:i.value,lineData:_.value};return M(d,{customConfig:{yAxis:[{},{axisLabel:{formatter:y=>y+"%"}}],tooltip:{formatter:y=>y.length>2?(y[2],y.map((v,w)=>w===2?`${v.marker} ${v.seriesName}: ${Number(v.value).toFixed(1)}%`:`${v.marker} ${v.seriesName}: ${v.value}`).join("<br/>")):y.map(v=>`${v.marker} ${v.seriesName}: ${v.value}`).join("<br/>")}}})}),f=async()=>{await e({startTime:c.value[0],endTime:c.value[1],oaFlag:u.value})};return f(),{__sfc:!0,academicAchievementsData:a,isAcademicAchievementsDataLoading:t,getAcademicAchievementsData:e,now:n,fiveYearsAgo:r,timeRange:c,containOA:u,displayType:l,handleToggleType:p,pickerOptions:g,chartData:_,xAxisData:i,lineChartData:m,getData:f,CardChart:k,academicAchievementsColumns:Cs}}};var Ls=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"学术成果",describe:"机构学术成果数量及保障情况。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isAcademicAchievementsDataLoading,expression:"isAcademicAchievementsDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isAcademicAchievementsDataLoading,expression:"isAcademicAchievementsDataLoading"}],attrs:{data:e.academicAchievementsData,columns:e.academicAchievementsColumns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}}],null,!1,3405306803)}):a._e()],1)},Ss=[],Os=b($s,Ls,Ss,!1,null,"70945204",null,null);const Fs=Os.exports,Ys=()=>{const s=o([]),a=o(!1);return{achievementsTop10Data:s,isAchievementsTop10DataLoading:a,getAchievementsTop10Data:async e=>{a.value=!0;try{const n=await $.organizationApi.getAchievementCollectionDatabaseTop10(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const Rs={__name:"achievements-top10",setup(s){const{achievementsTop10Data:a,isAchievementsTop10DataLoading:t,getAchievementsTop10Data:e}=Ys(),n=new Date().getFullYear().toString(),r=o(n),c=o("1"),u=o("chart"),l=f=>{u.value=f},p=h(()=>a.value.map(f=>({name:f.dbname,value:Number(f.indicator_value)}))),g=h(()=>q({treeData:p.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),_=o(null),i=f=>{f.data&&(_.value={name:f.data.name,value:f.data.value,path:f.treePathInfo.map(d=>d.name).join(" > "),level:f.treePathInfo.length-1,rawData:f.data},console.log("点击了矩形树图节点:",f))},m=async()=>{await e({year:r.value,oaFlag:c.value})};return m(),{__sfc:!0,achievementsTop10Data:a,isAchievementsTop10DataLoading:t,getAchievementsTop10Data:e,currentYear:n,year:r,containOA:c,displayType:u,handleToggleType:l,basicTreeData:p,basicTreemapOption:g,clickInfo:_,handleTreemapClick:i,getData:m,CardChart:k,achievementsTop10Columns:ws}}};var js=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"学术成果收录TOP10（数据库）",describe:"收录机构学术成果数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isAchievementsTop10DataLoading,expression:"isAchievementsTop10DataLoading"}],staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isAchievementsTop10DataLoading,expression:"isAchievementsTop10DataLoading"}],attrs:{data:e.achievementsTop10Data,columns:e.achievementsTop10Columns,"null-text":"-"},scopedSlots:a._u([{key:"is_cover",fn:function(n){return[n.row.is_cover==="1"?t("el-tag",{attrs:{type:"success"}},[a._v(" 是 ")]):t("el-tag",{attrs:{type:"danger"}},[a._v(" 否 ")])]}},{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}}],null,!1,171036848)}):a._e()],1)},Bs=[],Js=b(Rs,js,Bs,!1,null,"9f139725",null,null);const Ps=Js.exports,Ns=()=>{const s=o([]),a=o(!1);return{journalData:s,isJournalDataLoading:a,getJournalData:async e=>{a.value=!0;try{const n=await $.organizationApi.getAchievementJournal(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const Ms={__name:"journal",setup(s){const{journalData:a,isJournalDataLoading:t,getJournalData:e}=Ns(),n=O(),r=O().subtract(5,"year"),c=o([r.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),u=o("1"),l=o("chart"),p=d=>{l.value=d},g=o({shortcuts:[{text:"近3年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-3),d.$emit("pick",[v,y])}},{text:"近5年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-5),d.$emit("pick",[v,y])}}]}),_=h(()=>[{name:"发文期刊（种）",data:a.value.map(d=>d.indicator_total??0)},{name:"发文期刊保障量（种）",data:a.value.map(d=>d.indicator_value??0)},{name:"发文期刊保障率（%）",data:a.value.map(d=>d.indicator_ratio??0),yAxisIndex:1}]),i=h(()=>a.value.map(d=>d.year)),m=h(()=>{const d={xAxisData:i.value,lineData:_.value};return M(d,{customConfig:{yAxis:[{},{axisLabel:{formatter:y=>y+"%"}}],tooltip:{formatter:y=>y.length>2?(y[2],y.map((v,w)=>w===2?`${v.marker} ${v.seriesName}: ${Number(v.value).toFixed(1)}%`:`${v.marker} ${v.seriesName}: ${v.value}`).join("<br/>")):y.map(v=>`${v.marker} ${v.seriesName}: ${v.value}`).join("<br/>")}}})}),f=async()=>{await e({startTime:c.value[0],endTime:c.value[1],oaFlag:u.value})};return f(),{__sfc:!0,journalData:a,isJournalDataLoading:t,getJournalData:e,now:n,fiveYearsAgo:r,timeRange:c,containOA:u,displayType:l,handleToggleType:p,pickerOptions:g,chartData:_,xAxisData:i,lineChartData:m,getData:f,CardChart:k,journalColumns:Ts}}};var zs=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"发文期刊",describe:"机构学术成果发文期刊的数量以及保障情况。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],attrs:{data:e.journalData,columns:e.journalColumns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}}],null,!1,3405306803)}):a._e()],1)},Is=[],Es=b(Ms,zs,Is,!1,null,"e7ba31ea",null,null);const Ws=Es.exports,Vs=()=>{const s=o([]),a=o(!1);return{journalTop10Data:s,isJournalTop10DataLoading:a,getJournalTop10Data:async e=>{a.value=!0;try{const n=await $.organizationApi.getAchievementJournalDatabaseTop10(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const Hs={__name:"journal-top10",setup(s){const{journalTop10Data:a,isJournalTop10DataLoading:t,getJournalTop10Data:e}=Vs(),n=new Date().getFullYear().toString(),r=o(n),c=o("1"),u=o("chart"),l=f=>{u.value=f},p=h(()=>a.value.map(f=>({name:f.dbname,value:Number(f.indicator_value)}))),g=h(()=>q({treeData:p.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),_=o(null),i=f=>{f.data&&(_.value={name:f.data.name,value:f.data.value,path:f.treePathInfo.map(d=>d.name).join(" > "),level:f.treePathInfo.length-1,rawData:f.data},console.log("点击了矩形树图节点:",f))},m=async()=>{await e({year:r.value,oaFlag:c.value})};return m(),{__sfc:!0,journalTop10Data:a,isJournalTop10DataLoading:t,getJournalTop10Data:e,currentYear:n,year:r,containOA:c,displayType:u,handleToggleType:l,basicTreeData:p,basicTreemapOption:g,clickInfo:_,handleTreemapClick:i,getData:m,CardChart:k,journalTop10Columns:ks}}};var Ks=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"发文期刊收录TOP10（数据库）",describe:"收录机构发文期刊数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalTop10DataLoading,expression:"isJournalTop10DataLoading"}],staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalTop10DataLoading,expression:"isJournalTop10DataLoading"}],attrs:{data:e.journalTop10Data,columns:e.journalTop10Columns,"null-text":"-"},scopedSlots:a._u([{key:"is_cover",fn:function(n){return[n.row.is_cover==="1"?t("el-tag",{attrs:{type:"success"}},[a._v(" 是 ")]):t("el-tag",{attrs:{type:"danger"}},[a._v(" 否 ")])]}},{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}}],null,!1,171036848)}):a._e()],1)},Xs=[],qs=b(Hs,Ks,Xs,!1,null,"cb44a020",null,null);const Qs=qs.exports,Gs=()=>{const s=o([]),a=o(!1);return{journalTop20Data:s,isJournalTop20DataLoading:a,getJournalTop20Data:async e=>{a.value=!0;try{const n=await $.organizationApi.getAchievementJournalTop20(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const Zs={__name:"journal-top20",setup(s){const{journalTop20Data:a,isJournalTop20DataLoading:t,getJournalTop20Data:e}=Gs(),n=new Date().getFullYear().toString(),r=o(n),c=o("1"),u=o("chart"),l=m=>{u.value=m},p=h(()=>a.value.map(m=>({name:`《${m.title}》`,value:Number(m.indicator_value)}))),g=h(()=>ie({wordcloudData:p.value},{colors:["#6777EF","#36BDFF","#F2A940","#62AC00","#FC544B"],sizeRange:[14,50],rotationRange:[0,0],shape:"circle",customConfig:{}})),_=m=>{m.data&&console.log("点击了词云节点:",m.data.name,m.data.value)},i=async()=>{await e({year:r.value,oaFlag:c.value})};return i(),{__sfc:!0,journalTop20Data:a,isJournalTop20DataLoading:t,getJournalTop20Data:e,currentYear:n,year:r,containOA:c,displayType:u,handleToggleType:l,journalData:p,wordCloudOption:g,handleWordCloudClick:_,getData:i,CardChart:k,journalTop20Columns:As}}};var Us=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"发文期刊TOP20",describe:"发文量前20的期刊。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalTop20DataLoading,expression:"isJournalTop20DataLoading"}],staticClass:"h-300px",attrs:{option:e.wordCloudOption,events:{click:e.handleWordCloudClick}}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalTop20DataLoading,expression:"isJournalTop20DataLoading"}],attrs:{data:e.journalTop20Data,columns:e.journalTop20Columns,"null-text":"-"},scopedSlots:a._u([{key:"is_cover",fn:function(n){return[n.row.is_cover==="1"?t("el-tag",{attrs:{type:"success"}},[a._v(" 是 ")]):t("el-tag",{attrs:{type:"danger"}},[a._v(" 否 ")])]}}],null,!1,1575907594)}):a._e()],1)},eo=[],ao=b(Zs,Us,eo,!1,null,"308b5684",null,null);const to=ao.exports;const no={__name:"index",setup(s){return{__sfc:!0,AcademicAchievements:Fs,AchievementsTop10:Ps,Journal:Ws,JournalTop10:Qs,JournalTop20:to}}};var so=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.AcademicAchievements,{staticClass:"mb-20px"}),t(e.AchievementsTop10,{staticClass:"mb-20px"}),t(e.Journal,{staticClass:"mb-20px"}),t(e.JournalTop10,{staticClass:"mb-20px"}),t(e.JournalTop20,{staticClass:"mb-20px"})],1)},oo=[],ro=b(no,so,oo,!1,null,"ad0d07e0",null,null);const lo=ro.exports,io=()=>{const s=o([]),a=o(!1);return{referencesData:s,isReferencesDataLoading:a,getReferencesData:async e=>{a.value=!0;try{const n=await $.organizationApi.getReferenceData(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}},co=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"参考文献（篇）",prop:"indicator_total",sortable:!0},{label:"参考次数",prop:"indicator_count",sortable:!0},{label:"参考成本（元）",prop:"cost",sortable:!0},{label:"馆藏保障量（篇）",prop:"indicator_value",sortable:!0},{label:"保障率",prop:"indicator_ratio",sortable:!0},{label:"未保障量（篇）",prop:"indicator_sec_value",sortable:!0}],uo=[{prop:"doctitle",label:"标题",sortable:!0},{prop:"indicator_value",label:"参考次数",sortable:!0},{prop:"is_cover",label:"是否保障",sortable:!0},{prop:"jutitle",label:"来源期刊",sortable:!0},{prop:"dbname",label:"所在数据库",sortable:!0}],po=[{prop:"dbname",label:"数据库",sortable:!0},{prop:"indicator_value",label:"参考文献收录数量（篇）",sortable:!0},{prop:"indicator_ratio",label:"收录率",sortable:!0},{prop:"is_cover",label:"是否保障",sortable:!0}],_o=[{label:"年份",prop:"year",align:"center",sortable:!0},{label:"参考期刊（种）",prop:"indicator_total",sortable:!0},{label:"馆藏保障量（种）",prop:"indicator_value",sortable:!0},{label:"保障率",prop:"indicator_ratio",sortable:!0},{label:"未保障量（种）",prop:"indicator_sec_value",sortable:!0}],vo=[{prop:"dbname",label:"数据库",sortable:!0},{prop:"indicator_value",label:"参考期刊收录数量（种）",sortable:!0},{prop:"indicator_ratio",label:"收录率",sortable:!0},{prop:"is_cover",label:"是否保障",sortable:!0}],go=[{prop:"title",label:"刊名",sortable:!0},{prop:"issn",label:"ISSN",sortable:!0},{prop:"indicator_value",label:"参考次数",sortable:!0},{prop:"is_cover",label:"是否保障",sortable:!0},{prop:"dbname",label:"所在数据库",sortable:!0}];const fo={__name:"references",setup(s){const{referencesData:a,isReferencesDataLoading:t,getReferencesData:e}=io(),n=O(),r=O().subtract(5,"year"),c=o([r.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),u=o("1"),l=o("chart"),p=d=>{l.value=d},g=o({shortcuts:[{text:"近3年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-3),d.$emit("pick",[v,y])}},{text:"近5年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-5),d.$emit("pick",[v,y])}}]}),_=h(()=>[{name:"学术成果（篇）",data:a.value.map(d=>d.indicator_total??0)},{name:"学术成果保障量（篇）",data:a.value.map(d=>d.indicator_value??0)},{name:"学术成果保障率 （%）",data:a.value.map(d=>d.indicator_ratio??0),yAxisIndex:1}]),i=h(()=>a.value.map(d=>d.year)),m=h(()=>{const d={xAxisData:i.value,lineData:_.value};return M(d,{customConfig:{yAxis:[{},{axisLabel:{formatter:y=>y+"%"}}],tooltip:{formatter:y=>y.length>2?(y[2],y.map((v,w)=>w===2?`${v.marker} ${v.seriesName}: ${Number(v.value).toFixed(1)}%`:`${v.marker} ${v.seriesName}: ${v.value}`).join("<br/>")):y.map(v=>`${v.marker} ${v.seriesName}: ${v.value}`).join("<br/>")}}})}),f=async()=>{await e({startTime:c.value[0],endTime:c.value[1],oaFlag:u.value})};return(async()=>f())(),{__sfc:!0,referencesData:a,isReferencesDataLoading:t,getReferencesData:e,now:n,fiveYearsAgo:r,timeRange:c,containOA:u,displayType:l,handleToggleType:p,pickerOptions:g,chartData:_,xAxisData:i,lineChartData:m,getData:f,CardChart:k,referencesColumns:co}}};var mo=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"参考文献",describe:"机构学术成果参考文献数量及保障情况。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isReferencesDataLoading,expression:"isReferencesDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isReferencesDataLoading,expression:"isReferencesDataLoading"}],attrs:{data:e.referencesData,columns:e.referencesColumns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}}],null,!1,3405306803)}):a._e()],1)},yo=[],ho=b(fo,mo,yo,!1,null,"868dec2f",null,null);const bo=ho.exports,xo=()=>{const s=o([]),a=o(!1);return{referencesTop20Data:s,isReferencesTop20DataLoading:a,getReferencesTop20Data:async e=>{a.value=!0;try{const n=await $.organizationApi.getReferenceTop20(e);s.value=n.list||[]}catch(n){console.log(n)}finally{a.value=!1}}}};const Do={__name:"references-top20",setup(s){const{referencesTop20Data:a,isReferencesTop20DataLoading:t,getReferencesTop20Data:e}=xo(),n=o(O().format("YYYY")),r=o("1"),c=o("chart"),u=_=>{c.value=_},l=h(()=>ie({wordcloudData:a.value.map(_=>({name:`《${_.doctitle}》`,value:Number(_.indicator_value)}))},{colors:["#6777EF","#36BDFF","#F2A940","#62AC00","#FC544B"],sizeRange:[14,50],rotationRange:[0,0],shape:"circle",customConfig:{}})),p=_=>{_.data&&console.log("点击了词云节点:",_.data.name,_.data.value)},g=async()=>{await e({year:n.value,oaFlag:r.value})};return(async()=>g())(),{__sfc:!0,referencesTop20Data:a,isReferencesTop20DataLoading:t,getReferencesTop20Data:e,year:n,containOA:r,displayType:c,handleToggleType:u,wordCloudOption:l,handleWordCloudClick:p,getData:g,CardChart:k,referencesTop20Columns:uo}}};var Co=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"参考文献TOP20",describe:"参考次数前20的期刊文献。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isReferencesTop20DataLoading,expression:"isReferencesTop20DataLoading"}],staticClass:"h-300px",attrs:{option:e.wordCloudOption,events:{click:e.handleWordCloudClick}}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isReferencesTop20DataLoading,expression:"isReferencesTop20DataLoading"}],attrs:{data:e.referencesTop20Data,columns:e.referencesTop20Columns,"null-text":"-"},scopedSlots:a._u([{key:"is_cover",fn:function(n){return[n.row.is_cover==="1"?t("el-tag",{attrs:{type:"success"}},[a._v(" 是 ")]):t("el-tag",{attrs:{type:"danger"}},[a._v(" 否 ")])]}}],null,!1,1575907594)}):a._e()],1)},wo=[],To=b(Do,Co,wo,!1,null,"c1c6f0b9",null,null);const ko=To.exports,Ao=()=>{const s=o([]),a=o(!1);return{referencesTop10Data:s,isReferencesTop10DataLoading:a,getReferencesTop10Data:async e=>{a.value=!0;try{const n=await $.organizationApi.getReferenceTop10(e);s.value=n.list||[]}catch(n){console.log(n)}finally{a.value=!1}}}};const $o={__name:"references-top10",setup(s){const{referencesTop10Data:a,isReferencesTop10DataLoading:t,getReferencesTop10Data:e}=Ao(),n=o(O().format("YYYY")),r=o("1"),c=o("chart"),u=i=>{c.value=i},l=h(()=>q({treeData:a.value.map(i=>({name:i.dbname,value:Number(i.indicator_value)}))},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),p=o(null),g=i=>{i.data&&(p.value={name:i.data.name,value:i.data.value,path:i.treePathInfo.map(m=>m.name).join(" > "),level:i.treePathInfo.length-1,rawData:i.data},console.log("点击了矩形树图节点:",i))},_=async()=>{await e({year:n.value,oaFlag:r.value})};return(async()=>_())(),{__sfc:!0,referencesTop10Data:a,isReferencesTop10DataLoading:t,getReferencesTop10Data:e,year:n,containOA:r,displayType:c,handleToggleType:u,basicTreemapOption:l,clickInfo:p,handleTreemapClick:g,getData:_,CardChart:k,referencesTop10Columns:po}}};var Lo=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"参考文献收录TOP10（数据库）",describe:"收录机构参考文献数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份",clearable:!1,"value-format":"yyyy"},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isReferencesTop10DataLoading,expression:"isReferencesTop10DataLoading"}],staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isReferencesTop10DataLoading,expression:"isReferencesTop10DataLoading"}],attrs:{data:e.referencesTop10Data,columns:e.referencesTop10Columns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}},{key:"is_cover",fn:function(n){return[n.row.is_cover==="1"?t("el-tag",{attrs:{type:"success"}},[a._v(" 是 ")]):t("el-tag",{attrs:{type:"danger"}},[a._v(" 否 ")])]}}],null,!1,3681649040)}):a._e()],1)},So=[],Oo=b($o,Lo,So,!1,null,"a38d2086",null,null);const Fo=Oo.exports,Yo=()=>{const s=o([]),a=o(!1);return{journalData:s,isJournalDataLoading:a,getJournalData:async e=>{a.value=!0;try{const n=await $.organizationApi.getReferenceJournal(e);s.value=n.list}catch(n){console.log(n)}finally{a.value=!1}}}};const Ro={__name:"journal",setup(s){const{journalData:a,isJournalDataLoading:t,getJournalData:e}=Yo(),n=O(),r=O().subtract(5,"year"),c=o([r.format("YYYY-MM-DD"),n.format("YYYY-MM-DD")]),u=o("1"),l=o("chart"),p=d=>{l.value=d},g=o({shortcuts:[{text:"近3年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-3),d.$emit("pick",[v,y])}},{text:"近5年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-5),d.$emit("pick",[v,y])}}]}),_=h(()=>[{name:"参考期刊（种）",data:a.value.map(d=>d.indicator_total??0)},{name:"参考期刊保障量（种）",data:a.value.map(d=>d.indicator_value??0)},{name:"参考期刊保障率 （%）",data:a.value.map(d=>d.indicator_ratio??0),yAxisIndex:1}]),i=h(()=>a.value.map(d=>d.year)),m=h(()=>{const d={xAxisData:i.value,lineData:_.value};return M(d,{customConfig:{yAxis:[{},{axisLabel:{formatter:y=>y+"%"}}],tooltip:{formatter:y=>y.length>2?(y[2],y.map((v,w)=>w===2?`${v.marker} ${v.seriesName}: ${Number(v.value).toFixed(1)}%`:`${v.marker} ${v.seriesName}: ${v.value}`).join("<br/>")):y.map(v=>`${v.marker} ${v.seriesName}: ${v.value}`).join("<br/>")}}})}),f=async()=>{await e({startTime:c.value[0],endTime:c.value[1],oaFlag:u.value})};return(async()=>f())(),{__sfc:!0,journalData:a,isJournalDataLoading:t,getJournalData:e,now:n,fiveYearsAgo:r,timeRange:c,containOA:u,displayType:l,handleToggleType:p,pickerOptions:g,chartData:_,xAxisData:i,lineChartData:m,getData:f,CardChart:k,journalColumns:_o}}};var jo=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"参考期刊",describe:"机构学术成果参考期刊的数量以及保障情况。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalDataLoading,expression:"isJournalDataLoading"}],attrs:{data:e.journalData,columns:e.journalColumns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}}],null,!1,3405306803)}):a._e()],1)},Bo=[],Jo=b(Ro,jo,Bo,!1,null,"05f5fe16",null,null);const Po=Jo.exports,No=()=>{const s=o([]),a=o(!1);return{journalTop10Data:s,isJournalTop10DataLoading:a,getJournalTop10Data:async e=>{a.value=!0;try{const n=await $.organizationApi.getReferenceJournalTop10(e);s.value=n.list||[]}catch(n){console.log(n)}finally{a.value=!1}}}};const Mo={__name:"journal-top10",setup(s){const{journalTop10Data:a,isJournalTop10DataLoading:t,getJournalTop10Data:e}=No(),n=o(O().format("YYYY")),r=o("1"),c=o("chart"),u=i=>{c.value=i},l=h(()=>q({treeData:a.value.map(i=>({name:i.dbname,value:Number(i.indicator_value)}))},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),p=o(null),g=i=>{i.data&&(p.value={name:i.data.name,value:i.data.value,path:i.treePathInfo.map(m=>m.name).join(" > "),level:i.treePathInfo.length-1,rawData:i.data},console.log("点击了矩形树图节点:",i))},_=async()=>{await e({year:n.value,oaFlag:r.value})};return(async()=>_())(),{__sfc:!0,journalTop10Data:a,isJournalTop10DataLoading:t,getJournalTop10Data:e,year:n,containOA:r,displayType:c,handleToggleType:u,basicTreemapOption:l,clickInfo:p,handleTreemapClick:g,getData:_,CardChart:k,journalTop10Columns:vo}}};var zo=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"参考期刊收录TOP10（数据库）",describe:"收录机构参考期刊数量最多的前10个数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalTop10DataLoading,expression:"isJournalTop10DataLoading"}],staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalTop10DataLoading,expression:"isJournalTop10DataLoading"}],attrs:{data:e.journalTop10Data,columns:e.journalTop10Columns,"null-text":"-"},scopedSlots:a._u([{key:"indicator_ratio",fn:function(n){return[t("span",[a._v(a._s(n.row.indicator_ratio??0)+"%")])]}},{key:"is_cover",fn:function(n){return[n.row.is_cover==="1"?t("el-tag",{attrs:{type:"success"}},[a._v(" 是 ")]):t("el-tag",{attrs:{type:"danger"}},[a._v(" 否 ")])]}}],null,!1,3681649040)}):a._e()],1)},Io=[],Eo=b(Mo,zo,Io,!1,null,"f285061d",null,null);const Wo=Eo.exports,Vo=()=>{const s=o([]),a=o(!1);return{journalTop20Data:s,isJournalTop20DataLoading:a,getJournalTop20Data:async e=>{a.value=!0;try{const n=await $.organizationApi.getReferenceJournalTop20(e);s.value=n.list||[]}catch(n){console.log(n)}finally{a.value=!1}}}};const Ho={__name:"journal-top20",setup(s){const{journalTop20Data:a,isJournalTop20DataLoading:t,getJournalTop20Data:e}=Vo(),n=o(O().format("YYYY")),r=o("1"),c=o("chart"),u=_=>{c.value=_},l=h(()=>ie({wordcloudData:a.value.map(_=>({name:`《${_.title}》`,value:_.indicator_value}))},{colors:["#6777EF","#36BDFF","#F2A940","#62AC00","#FC544B"],sizeRange:[14,50],rotationRange:[0,0],shape:"circle",customConfig:{}})),p=_=>{_.data&&console.log("点击了词云节点:",_.data.name,_.data.value)},g=async()=>{await e({year:n.value,oaFlag:r.value})};return(async()=>g())(),{__sfc:!0,journalTop20Data:a,isJournalTop20DataLoading:t,getJournalTop20Data:e,year:n,containOA:r,displayType:c,handleToggleType:u,wordCloudOption:l,handleWordCloudClick:p,getData:g,CardChart:k,journalTop20Columns:go}}};var Ko=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"参考期刊TOP20",describe:"参考次数前20的期刊。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("div",[t("span",{staticClass:"text-[#34395E] text-sm mr-10px"},[a._v("含OA资源")]),t("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:e.getData},model:{value:e.containOA,callback:function(n){e.containOA=n},expression:"containOA"}})],1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalTop20DataLoading,expression:"isJournalTop20DataLoading"}],staticClass:"h-300px",attrs:{option:e.wordCloudOption,events:{click:e.handleWordCloudClick}}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isJournalTop20DataLoading,expression:"isJournalTop20DataLoading"}],attrs:{data:e.journalTop20Data,columns:e.journalTop20Columns,"null-text":"-"},scopedSlots:a._u([{key:"is_cover",fn:function(n){return[n.row.is_cover==="1"?t("el-tag",{attrs:{type:"success"}},[a._v(" 是 ")]):t("el-tag",{attrs:{type:"danger"}},[a._v(" 否 ")])]}}],null,!1,1575907594)}):a._e()],1)},Xo=[],qo=b(Ho,Ko,Xo,!1,null,"0bd9488a",null,null);const Qo=qo.exports;const Go={__name:"index",setup(s){return{__sfc:!0,References:bo,ReferencesTop20:ko,ReferencesTop10:Fo,Journal:Po,JournalTop10:Wo,JournalTop20:Qo}}};var Zo=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.References,{staticClass:"mb-20px"}),t(e.ReferencesTop20,{staticClass:"mb-20px"}),t(e.ReferencesTop10,{staticClass:"mb-20px"}),t(e.Journal,{staticClass:"mb-20px"}),t(e.JournalTop10,{staticClass:"mb-20px"}),t(e.JournalTop20,{staticClass:"mb-20px"})],1)},Uo=[],er=b(Go,Zo,Uo,!1,null,"81eb8d23",null,null);const ar=er.exports;const tr={__name:"index",setup(s){const a=o([{name:"1",label:"学术成果"},{name:"2",label:"参考文献"}]),t=o("2");return{__sfc:!0,tabs:a,currentTabName:t,handleTabClick:n=>{t.value=n.name},TabAcademicAchievements:lo,TabReferences:ar}}};var nr=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t("div",{staticClass:"h-11 flex gap-10px"},a._l(e.tabs,function(n){return t("div",{key:n.name,staticClass:"tab-item cursor-pointer h-9 leading-9 bg-[#F0F4FF] text-[#697FFA] px-4 rounded-3px text-sm",class:{active:n.name===e.currentTabName},on:{click:function(r){return e.handleTabClick(n)}}},[a._v(" "+a._s(n.label)+" ")])}),0),e.currentTabName==="1"?t(e.TabAcademicAchievements):a._e(),e.currentTabName==="2"?t(e.TabReferences):a._e()],1)},sr=[],or=b(tr,nr,sr,!1,null,"5fd79f5f",null,null);const rr=or.exports;const lr={__name:"achievement-info",setup(s){return{__sfc:!0}}};var ir=function(){var a=this;return a._self._c,a._self._setupProxy,a._m(0)},cr=[function(){var s=this,a=s._self._c;return s._self._setupProxy,a("div",{staticClass:"text-sm text-[#3C4B5D]"},[a("div",{staticClass:"indent-2em"},[s._v(" 从机构学术成果保障情况以及参考文献保障情况进行统计分析，评估本机构馆藏文献。学出成果仅支持期刊文献，并且数据范围仅限于：WOS（SCI-EXPANDED、SSCI、A&HCI、ESCI、IC、CCR-EXPANDED）、EI期刊、ScienceDirect期刊、Scopus 期刊、IEEE 期刊、Springer期刊、PubMed期刊、PMC期刊、Wiley期刊、美国化学学会期刊、美国计算机协会期刊、cell 、nature、science 、CNKI、维普中文科技期刊、万方中文期刊、CSCD 、CSSCI。参考文献数据范围仅WOS和维普中文期刊。 ")])])}],ur=b(lr,ir,cr,!1,null,"3d0fbb7e",null,null);const pr=ur.exports,dr=()=>{const s=o([]),a=o(!1);return{clickCountData:s,isClickCountDataLoading:a,getClickCountData:async n=>{a.value=!0;try{const r=await $.organizationApi.getDatabaseClickCount(n);s.value=r.list||[]}catch(r){console.log(r)}finally{a.value=!1}},clickCountColumns:[{prop:"year",label:"年份",sortable:!0},{prop:"indicator_value",label:"数据库点击量（次）",sortable:!0}]}};const _r={__name:"click-count",setup(s){const{clickCountData:a,isClickCountDataLoading:t,getClickCountData:e,clickCountColumns:n}=dr(),r=O(),c=O().subtract(5,"year"),u=o([c.format("YYYY-MM-DD"),r.format("YYYY-MM-DD")]),l=o("chart"),p=d=>{l.value=d},g=o({shortcuts:[{text:"近3年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-3),d.$emit("pick",[v,y])}},{text:"近5年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-5),d.$emit("pick",[v,y])}}]}),_=h(()=>[{name:"点击量",data:a.value.map(d=>d.indicator_value||0)}]),i=h(()=>a.value.map(d=>d.year||"")),m=h(()=>{const d={xAxisData:i.value,lineData:_.value};return M(d,{customConfig:{}})}),f=async()=>{await e({startTime:u.value[0],endTime:u.value[1]})};return(async()=>f())(),{__sfc:!0,clickCountData:a,isClickCountDataLoading:t,getClickCountData:e,clickCountColumns:n,now:r,fiveYearsAgo:c,timeRange:u,displayType:l,handleToggleType:p,pickerOptions:g,chartData:_,xAxisData:i,lineChartData:m,getData:f,CardChart:k}}};var vr=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"数据库点击量",describe:"机构学者点击数据库的次数。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isClickCountDataLoading,expression:"isClickCountDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isClickCountDataLoading,expression:"isClickCountDataLoading"}],attrs:{data:e.clickCountData,columns:e.clickCountColumns,"null-text":"-"}}):a._e()],1)},gr=[],fr=b(_r,vr,gr,!1,null,"58f858de",null,null);const mr=fr.exports,yr=()=>{const s=o([]),a=o(!1);return{clickCountTop10Data:s,isClickCountTop10DataLoading:a,getClickCountTop10Data:async n=>{a.value=!0;try{const r=await $.organizationApi.getDatabaseClickTop10(n);s.value=r.list||[]}catch(r){console.log(r)}finally{a.value=!1}},clickCountTop10Columns:[{prop:"dbname",label:"数据库",sortable:!0},{prop:"indicator_value",label:"数据库点击量（次）",sortable:!0}]}};const hr={__name:"click-count-top10",setup(s){const{clickCountTop10Data:a,isClickCountTop10DataLoading:t,getClickCountTop10Data:e,clickCountTop10Columns:n}=yr(),r=o(O().format("YYYY")),c=async()=>{await e({year:r.value})};return(async()=>c())(),{__sfc:!0,clickCountTop10Data:a,isClickCountTop10DataLoading:t,getClickCountTop10Data:e,clickCountTop10Columns:n,year:r,getData:c,CardChart:k}}};var br=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"数据库点击量(TOP10)",describe:"点击次数前10的数据库。","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)]},proxy:!0}])},[t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isClickCountTop10DataLoading,expression:"isClickCountTop10DataLoading"}],attrs:{data:e.clickCountTop10Data,columns:e.clickCountTop10Columns,"null-text":"-"}})],1)},xr=[],Dr=b(hr,br,xr,!1,null,"d736ac17",null,null);const Cr=Dr.exports;const wr={__name:"index",setup(s){return{__sfc:!0,ClickCount:mr,ClickCountTop10:Cr}}};var Tr=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.ClickCount,{staticClass:"mb-20px"}),t(e.ClickCountTop10,{staticClass:"mb-20px"})],1)},kr=[],Ar=b(wr,Tr,kr,!1,null,"1c284366",null,null);const $r=Ar.exports;const Lr={__name:"document-delivery",setup(s){const a=O(),t=O().subtract(5,"year"),e=o([t.format("YYYY-MM-DD"),a.format("YYYY-MM-DD")]),n=o("chart"),r=g=>{n.value=g},c=o({shortcuts:[{text:"近3年",onClick(g){const _=new Date,i=new Date;i.setFullYear(i.getFullYear()-3),g.$emit("pick",[i,_])}},{text:"近5年",onClick(g){const _=new Date,i=new Date;i.setFullYear(i.getFullYear()-5),g.$emit("pick",[i,_])}}]}),u=o([{name:"访问量",data:[1e3,2e3]},{name:"成交量",data:[500,1500]}]),l=o(["周一","周二"]),p=h(()=>{const g={xAxisData:l.value,lineData:u.value};return M(g,{customConfig:{}})});return{__sfc:!0,now:a,fiveYearsAgo:t,timeRange:e,displayType:n,handleToggleType:r,pickerOptions:c,chartData:u,xAxisData:l,lineChartData:p,CardChart:k}}};var Sr=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"文献传递量",describe:"机构学者传递文献的次数。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{attrs:{data:a.tableData,columns:a.columns}}):a._e()],1)},Or=[],Fr=b(Lr,Sr,Or,!1,null,"82a40544",null,null);const Yr=Fr.exports;const Rr={__name:"delivery-article-top10",setup(s){const a=o(""),t=o({shortcuts:[{text:"近3年",onClick(e){const n=new Date,r=new Date;r.setFullYear(r.getFullYear()-3),e.$emit("pick",[r,n])}},{text:"近5年",onClick(e){const n=new Date,r=new Date;r.setFullYear(r.getFullYear()-5),e.$emit("pick",[r,n])}}]});return{__sfc:!0,year:a,pickerOptions:t,CardChart:k}}};var jr=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"文献传递TOP10（文献）",describe:"传递次数前10的文献。","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)]},proxy:!0}])},[a._v(" TODO:表格 ")])},Br=[],Jr=b(Rr,jr,Br,!1,null,"75900e0b",null,null);const Pr=Jr.exports;const Nr={__name:"delivery-database-top10",setup(s){const a=o(""),t=o("chart"),e=l=>{t.value=l},n=o([{name:"图书",value:6e3},{name:"期刊",value:4e3},{name:"数据库",value:3e3},{name:"多媒体",value:2e3},{name:"报纸",value:1e3}]),r=h(()=>q({treeData:n.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),c=o(null);return{__sfc:!0,year:a,displayType:t,handleToggleType:e,basicTreeData:n,basicTreemapOption:r,clickInfo:c,handleTreemapClick:l=>{l.data&&(c.value={name:l.data.name,value:l.data.value,path:l.treePathInfo.map(p=>p.name).join(" > "),level:l.treePathInfo.length-1,rawData:l.data},console.log("点击了矩形树图节点:",l))},CardChart:k}}};var Mr=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"文献传递TOP10（数据库）",describe:"传递次数前10的数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):a._e(),e.displayType==="table"?t("d-table",{attrs:{data:a.tableData,columns:a.columns}}):a._e()],1)},zr=[],Ir=b(Nr,Mr,zr,!1,null,"dbe23f9d",null,null);const Er=Ir.exports;const Wr={__name:"delivery-subject",setup(s){const a=o(""),t=o(""),e=o(!0),n=o(!0),r=o("chart"),c=g=>{r.value=g},u=o(["一月","二月","三月","四月","五月","六月"]),l=o([{name:"收入",data:[320,332,301,334,390,330]},{name:"支出",data:[120,132,101,134,90,230]}]),p=h(()=>W({xAxisData:u.value,barData:l.value},{customConfig:{}}));return{__sfc:!0,year:a,subject:t,containOA:e,removeDuplicates:n,displayType:r,handleToggleType:c,categories:u,seriesData:l,verticalBarOption:p,CardChart:k}}};var Vr=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"传递文献学科分布",describe:"不同学科分类下的文献传递数量。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择学科"},model:{value:e.subject,callback:function(n){e.subject=n},expression:"subject"}},a._l([{label:"中文",value:"1"},{label:"外文",value:"2"}],function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):a._e(),e.displayType==="table"?t("d-table",{attrs:{data:a.tableData,columns:a.columns}}):a._e()],1)},Hr=[],Kr=b(Wr,Vr,Hr,!1,null,"35ee5ea4",null,null);const Xr=Kr.exports;const qr={__name:"accepted-volume",setup(s){const a=O(),t=O().subtract(5,"year"),e=o([t.format("YYYY-MM-DD"),a.format("YYYY-MM-DD")]),n=o("chart"),r=g=>{n.value=g},c=o({shortcuts:[{text:"近3年",onClick(g){const _=new Date,i=new Date;i.setFullYear(i.getFullYear()-3),g.$emit("pick",[i,_])}},{text:"近5年",onClick(g){const _=new Date,i=new Date;i.setFullYear(i.getFullYear()-5),g.$emit("pick",[i,_])}}]}),u=o([{name:"访问量",data:[1e3,2e3]},{name:"成交量",data:[500,1500]}]),l=o(["周一","周二"]),p=h(()=>{const g={xAxisData:l.value,lineData:u.value};return M(g,{customConfig:{}})});return{__sfc:!0,now:a,fiveYearsAgo:t,timeRange:e,displayType:n,handleToggleType:r,pickerOptions:c,chartData:u,xAxisData:l,lineChartData:p,CardChart:k}}};var Qr=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"文献接收量",describe:"机构学者接收文献的次数。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{attrs:{data:a.tableData,columns:a.columns}}):a._e()],1)},Gr=[],Zr=b(qr,Qr,Gr,!1,null,"115b9628",null,null);const Ur=Zr.exports;const el={__name:"accepted-article-top10",setup(s){const a=o(""),t=o({shortcuts:[{text:"近3年",onClick(e){const n=new Date,r=new Date;r.setFullYear(r.getFullYear()-3),e.$emit("pick",[r,n])}},{text:"近5年",onClick(e){const n=new Date,r=new Date;r.setFullYear(r.getFullYear()-5),e.$emit("pick",[r,n])}}]});return{__sfc:!0,timeRange:a,pickerOptions:t,CardChart:k}}};var al=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"文献接收TOP10（文献）",describe:"接收次数前10的文献。","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})],1)]},proxy:!0}])},[a._v(" TODO:表格 ")])},tl=[],nl=b(el,al,tl,!1,null,"47eaca35",null,null);const sl=nl.exports;const ol={__name:"accepted-database-top10",setup(s){const a=o(""),t=o("chart"),e=l=>{t.value=l},n=o([{name:"图书",value:6e3},{name:"期刊",value:4e3},{name:"数据库",value:3e3},{name:"多媒体",value:2e3},{name:"报纸",value:1e3}]),r=h(()=>q({treeData:n.value},{showLabel:!0,roam:!1,customConfig:{series:[{width:"100%",height:"100%",nodeClick:!1,breadcrumb:{show:!1}}]}})),c=o(null);return{__sfc:!0,year:a,displayType:t,handleToggleType:e,basicTreeData:n,basicTreemapOption:r,clickInfo:c,handleTreemapClick:l=>{l.data&&(c.value={name:l.data.name,value:l.data.value,path:l.treePathInfo.map(p=>p.name).join(" > "),level:l.treePathInfo.length-1,rawData:l.data},console.log("点击了矩形树图节点:",l))},CardChart:k}}};var rl=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"文献接收TOP10（数据库）",describe:"接收次数前10的数据库。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{staticClass:"h-300px",attrs:{option:e.basicTreemapOption,events:{click:e.handleTreemapClick}}}):a._e(),e.displayType==="table"?t("d-table",{attrs:{data:a.tableData,columns:a.columns}}):a._e()],1)},ll=[],il=b(ol,rl,ll,!1,null,"35f8ffac",null,null);const cl=il.exports;const ul={__name:"accepted-subject",setup(s){const a=o(""),t=o(""),e=o(!0),n=o(!0),r=o("chart"),c=_=>{r.value=_},u=o({shortcuts:[{text:"近3年",onClick(_){const i=new Date,m=new Date;m.setFullYear(m.getFullYear()-3),_.$emit("pick",[m,i])}},{text:"近5年",onClick(_){const i=new Date,m=new Date;m.setFullYear(m.getFullYear()-5),_.$emit("pick",[m,i])}}]}),l=o(["一月","二月","三月","四月","五月","六月"]),p=o([{name:"收入",data:[320,332,301,334,390,330]},{name:"支出",data:[120,132,101,134,90,230]}]),g=h(()=>W({xAxisData:l.value,barData:p.value},{customConfig:{}}));return{__sfc:!0,year:a,subject:t,containOA:e,removeDuplicates:n,displayType:r,handleToggleType:c,pickerOptions:u,categories:l,seriesData:p,verticalBarOption:g,CardChart:k}}};var pl=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"接收文献学科分布",describe:"不同学科分类下的文献接收数量。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份"},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}}),t("el-select",{attrs:{placeholder:"请选择学科"},model:{value:e.subject,callback:function(n){e.subject=n},expression:"subject"}},a._l([{label:"中文",value:"1"},{label:"外文",value:"2"}],function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)],1)])]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{staticClass:"h-300px",attrs:{option:e.verticalBarOption}}):a._e(),e.displayType==="table"?t("d-table",{attrs:{data:a.tableData,columns:a.columns}}):a._e()],1)},dl=[],_l=b(ul,pl,dl,!1,null,"4e3cbaf4",null,null);const vl=_l.exports;const gl={__name:"index",setup(s){return{__sfc:!0,DocumentDelivery:Yr,DeliveryArticleTop10:Pr,DeliveryDatabaseTop10:Er,DeliverySubject:Xr,AcceptedVolume:Ur,AcceptedArticleTop10:sl,AcceptedDatabaseTop10:cl,AcceptedSubject:vl}}};var fl=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.DocumentDelivery,{staticClass:"mb-20px"}),t(e.DeliveryArticleTop10,{staticClass:"mb-20px"}),t(e.DeliveryDatabaseTop10,{staticClass:"mb-20px"}),t(e.DeliverySubject,{staticClass:"mb-20px"}),t(e.AcceptedVolume,{staticClass:"mb-20px"}),t(e.AcceptedArticleTop10,{staticClass:"mb-20px"}),t(e.AcceptedDatabaseTop10,{staticClass:"mb-20px"}),t(e.AcceptedSubject,{staticClass:"mb-20px"})],1)},ml=[],yl=b(gl,fl,ml,!1,null,"2b8a61c6",null,null);const hl=yl.exports,bl=()=>{const s=o([]),a=o(!1);return{bookBorrowingData:s,isBookBorrowingDataLoading:a,getBookBorrowingData:async n=>{a.value=!0;try{const r=await $.organizationApi.getBookBorrowingCount(n);s.value=r.list||[]}catch(r){console.log(r)}finally{a.value=!1}},bookBorrowingColumns:[{prop:"year",label:"年份",sortable:!0},{prop:"indicator_total",label:"图书量（种）",sortable:!0},{prop:"indicator_value",label:"借阅量（次）",sortable:!0}]}};const xl={__name:"book-borrowing",setup(s){const{bookBorrowingData:a,isBookBorrowingDataLoading:t,getBookBorrowingData:e,bookBorrowingColumns:n}=bl(),r=O(),c=O().subtract(5,"year"),u=o([c.format("YYYY-MM-DD"),r.format("YYYY-MM-DD")]),l=o("chart"),p=d=>{l.value=d},g=o({shortcuts:[{text:"近3年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-3),d.$emit("pick",[v,y])}},{text:"近5年",onClick(d){const y=new Date,v=new Date;v.setFullYear(v.getFullYear()-5),d.$emit("pick",[v,y])}}]}),_=h(()=>[{name:"借阅量",data:a.value.map(d=>d.indicator_value||0)}]),i=h(()=>a.value.map(d=>d.year||"")),m=h(()=>{const d={xAxisData:i.value,lineData:_.value};return M(d,{customConfig:{}})}),f=async()=>{await e({startTime:u.value[0],endTime:u.value[1]})};return(async()=>f())(),{__sfc:!0,bookBorrowingData:a,isBookBorrowingDataLoading:t,getBookBorrowingData:e,bookBorrowingColumns:n,now:r,fiveYearsAgo:c,timeRange:u,displayType:l,handleToggleType:p,pickerOptions:g,chartData:_,xAxisData:i,lineChartData:m,getData:f,CardChart:k}}};var Dl=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"纸质图书借阅量",describe:"机构学者借阅纸质图书的次数。","show-chart":"","show-table":"","show-export":"","display-type":e.displayType},on:{"toggle-type":e.handleToggleType},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,size:"medium","value-format":"yyyy-MM-dd",clearable:!1},on:{change:e.getData},model:{value:e.timeRange,callback:function(n){e.timeRange=n},expression:"timeRange"}})]},proxy:!0}])},[e.displayType==="chart"?t("dv-chart",{directives:[{name:"loading",rawName:"v-loading",value:e.isBookBorrowingDataLoading,expression:"isBookBorrowingDataLoading"}],staticClass:"h-300px",attrs:{option:e.lineChartData}}):a._e(),e.displayType==="table"?t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isBookBorrowingDataLoading,expression:"isBookBorrowingDataLoading"}],attrs:{data:e.bookBorrowingData,columns:e.bookBorrowingColumns,"null-text":"-"}}):a._e()],1)},Cl=[],wl=b(xl,Dl,Cl,!1,null,"4ea9805b",null,null);const Tl=wl.exports,kl=()=>{const s=o([]),a=o(!1);return{bookBorrowingTop10Data:s,isBookBorrowingTop10DataLoading:a,getBookBorrowingTop10Data:async n=>{a.value=!0;try{const r=await $.organizationApi.getBookBorrowingTop10(n);s.value=r.list||[]}catch(r){console.log(r)}finally{a.value=!1}},bookBorrowingTop10Columns:[{prop:"title",label:"题名",sortable:!0},{prop:"isbn",label:"ISBN",sortable:!0},{prop:"indicator_value",label:"借阅量（次）",sortable:!0}]}};const Al={__name:"book-borrowing-top10",setup(s){const{bookBorrowingTop10Data:a,isBookBorrowingTop10DataLoading:t,getBookBorrowingTop10Data:e,bookBorrowingTop10Columns:n}=kl(),r=o(O().format("YYYY")),c=async()=>{await e({year:r.value})};return(async()=>c())(),{__sfc:!0,bookBorrowingTop10Data:a,isBookBorrowingTop10DataLoading:t,getBookBorrowingTop10Data:e,bookBorrowingTop10Columns:n,year:r,getData:c,CardChart:k}}};var $l=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t(e.CardChart,{attrs:{title:"纸质图书借阅TOP10",describe:"借阅次数前10的纸质图书。","show-export":""},scopedSlots:a._u([{key:"operation-left",fn:function(){return[t("div",{staticClass:"flex gap-20px items-center"},[t("div",{staticClass:"flex gap-10px"},[t("el-date-picker",{attrs:{type:"year",placeholder:"请选择年份","value-format":"yyyy",clearable:!1},on:{change:e.getData},model:{value:e.year,callback:function(n){e.year=n},expression:"year"}})],1)])]},proxy:!0}])},[t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isBookBorrowingTop10DataLoading,expression:"isBookBorrowingTop10DataLoading"}],attrs:{data:e.bookBorrowingTop10Data,columns:e.bookBorrowingTop10Columns,"null-text":"-"}})],1)},Ll=[],Sl=b(Al,$l,Ll,!1,null,"9b4b88cd",null,null);const Ol=Sl.exports;const Fl={__name:"index",setup(s){return{__sfc:!0,BookBorrowing:Tl,BookBorrowingTop10:Ol}}};var Yl=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t(e.BookBorrowing,{staticClass:"mb-20px"}),t(e.BookBorrowingTop10,{staticClass:"mb-20px"})],1)},Rl=[],jl=b(Fl,Yl,Rl,!1,null,"7b9f610c",null,null);const Bl=jl.exports;const Jl={__name:"index",setup(s){const a=o([{name:"1",label:"数据库"},{name:"3",label:"纸书借阅"}]),t=o("1");return{__sfc:!0,tabs:a,currentTabName:t,handleTabClick:n=>{t.value=n.name},TabDatabase:$r,TabDocumentDelivery:hl,TabBookBorrowing:Bl}}};var Pl=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t("div",[t("div",{staticClass:"h-11 flex gap-10px"},a._l(e.tabs,function(n){return t("div",{key:n.name,staticClass:"tab-item cursor-pointer h-9 leading-9 bg-[#F0F4FF] text-[#697FFA] px-4 rounded-3px text-sm",class:{active:n.name===e.currentTabName},on:{click:function(r){return e.handleTabClick(n)}}},[a._v(" "+a._s(n.label)+" ")])}),0),e.currentTabName==="1"?t(e.TabDatabase):a._e(),e.currentTabName==="2"?t(e.TabDocumentDelivery):a._e(),e.currentTabName==="3"?t(e.TabBookBorrowing):a._e()],1)])},Nl=[],Ml=b(Jl,Pl,Nl,!1,null,"14bf32a3",null,null);const zl=Ml.exports;const Il={__name:"literature-info",setup(s){return{__sfc:!0}}};var El=function(){var a=this;return a._self._c,a._self._setupProxy,a._m(0)},Wl=[function(){var s=this,a=s._self._c;return s._self._setupProxy,a("div",{staticClass:"text-sm text-[#3C4B5D]"},[a("div",{staticClass:"indent-2em"},[s._v("从机构学者使用馆藏文献的情况进行统计分析，评估本机构馆藏文献。")])])}],Vl=b(Il,El,Wl,!1,null,"80dab287",null,null);const Hl=Vl.exports;const Kl={__name:"collapse-menu",setup(s){const a=o(null),t=o(null),e=o(null);return{__sfc:!0,selectedMainIndex:a,selectedSubKey:t,openIndex:e,menuList:[{title:"05/ 馆藏数量评价",children:["美视电影学院美视电影学院美视电影学院","馆藏数据库重复文献分析","馆藏数据库重复文献分析","馆藏数据库重复文献分析"]},{title:"05/ 馆藏数量评价",children:["馆藏数据库重复文献分析"]},{title:"06/ 馆藏数量评价",children:[]}],toggle:u=>{e.value=e.value===u?null:u},handleMainClick:(u,l)=>{l.children&&l.children.length>0?e.value=e.value===u?null:u:(a.value=u,t.value=null)}}}};var Xl=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",{staticClass:"menu"},a._l(e.menuList,function(n,r){return t("div",{key:r,staticClass:"menu-item"},[t("div",{staticClass:"menu-title",class:{clickable:n.children&&n.children.length>0,active:(!n.children||n.children.length===0)&&e.selectedMainIndex===r},on:{click:function(c){return e.handleMainClick(r,n)}}},[a._v(" "+a._s(n.title)+" "),n.children&&n.children.length>0?t("span",{staticClass:"arrow bg-white rounded-7px flex items-center justify-center",class:{rotated:e.openIndex===r}},[t("i",{staticClass:"el-icon-arrow-up text-[#6777EF]"})]):a._e()]),t("transition",{attrs:{name:"slide"}},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.openIndex===r,expression:"openIndex === index"}],staticClass:"submenu"},a._l(n.children,function(c,u){return t("div",{key:`${r}-${u}`,staticClass:"submenu-item",class:{active:e.selectedSubKey===`${r}-${u}`},on:{click:function(l){e.selectedSubKey=`${r}-${u}`}}},[a._v(" "+a._s(c)+" ")])}),0)])],1)}),0)},ql=[],Ql=b(Kl,Xl,ql,!1,null,"4d8e59f9",null,null);const Gl=Ql.exports,Zl={},Ul=null,ei=null;var ai=b(Zl,Ul,ei,!1,null,null,null,null);const ti=ai.exports,ni={},si=null,oi=null;var ri=b(ni,si,oi,!1,null,null,null,null);const li=ri.exports;const ii={__name:"heading",props:{content:{type:String,required:!0},level:{type:Number,required:!0}},setup(s){return{__sfc:!0,props:s}}};var ci=function(){var a=this,t=a._self._c;return a._self._setupProxy,a.level===1?t("h1",[a._v(a._s(a.content))]):a.level===2?t("h2",[a._v(a._s(a.content))]):a.level===3?t("h3",[a._v(a._s(a.content))]):a.level===4?t("h4",[a._v(a._s(a.content))]):a.level===5?t("h5",[a._v(a._s(a.content))]):a.level===6?t("h6",[a._v(a._s(a.content))]):a._e()},ui=[],pi=b(ii,ci,ui,!1,null,"ad97372c",null,null);const di=pi.exports;const _i={__name:"paragraph",props:{content:{type:String,required:!0}},setup(s){return{__sfc:!0,props:s}}};var vi=function(){var a=this,t=a._self._c;return a._self._setupProxy,t("p",[a._v(a._s(a.content))])},gi=[],fi=b(_i,vi,gi,!1,null,"5248a8a1",null,null);const mi=fi.exports,yi={},hi=null,bi=null;var xi=b(yi,hi,bi,!1,null,null,null,null);const Di=xi.exports;const Ci={__name:"content-block",props:{block:{type:Object,required:!0}},setup(s){const a=s,t=o(a.block.content),e=o(!0),n=h(()=>({toc:ti,cover:li,heading:di,paragraph:mi,table:Di})[a.block.type]||"div"),r=(u,l)=>u.replace(/\{\{\s*(\w+)\s*\}\}/g,(p,g)=>l[g]!==void 0?String(l[g]):p),c=async()=>{try{const u={method:a.block.api.method,url:a.block.api.url};a.block.api.method==="GET"?u.params=a.block.api.params:u.data=a.block.api.body,e.value=!0,setTimeout(()=>{t.value=r(a.block.content,{data1:"张三",data2:18}),e.value=!1},2e3)}catch(u){console.log(u)}finally{e.value=!1}};return(async()=>a.block.api?await c():e.value=!1)(),{__sfc:!0,props:a,renderedContent:t,loading:e,componentType:n,replaceVariables:r,getComponentData:c}}};var wi=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",{staticClass:"content-block"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t(e.componentType,{tag:"component",attrs:{content:e.renderedContent,level:a.block.level,"table-headers":a.block.tableHeaders}})],1)])},Ti=[],ki=b(Ci,wi,Ti,!1,null,"6bc4360a",null,null);const Ai=ki.exports;const $i={__name:"index",props:{visible:{type:Boolean,default:!1}},emits:["update:visible","confirm","cancel"],setup(s,{emit:a}){const t=s,e=o([{type:"paragraph",content:"截止2024年12月31日，刊表内含{{data1}}万种期刊，{{data2}}万篇期刊文献",api:{url:"/api/chapter1",method:"GET"}},{type:"heading",level:2,content:"期刊分类",api:null}]);return{__sfc:!0,props:t,emit:a,blocks:e,CollapseMenu:Gl,ContentBlock:Ai}}};var Li=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("d-dialog",{attrs:{title:"报告预览",type:"ok","show-footer":!1,width:"1200px",visible:e.props.visible},on:{"update:visible":function(n){return e.emit("update:visible",n)}}},[t("div",{staticClass:"flex justify-between gap-5"},[t("div",{staticClass:"toc w-220px"},[t(e.CollapseMenu)],1),t("div",{staticClass:"content flex-1 h-840px bg-[#F1F3F7] relative flex justify-center items-center"},[t("div",{staticClass:"w-550px h-780px overflow-y-auto"},a._l(e.blocks,function(n,r){return t(e.ContentBlock,{key:r,attrs:{block:n}})}),1),t("div",{staticClass:"arrow-previous bg-[#3C4B5D]"},[t("d-icon",{attrs:{name:"el-icon-vip-a-zuojiantou2"}})],1),t("div",{staticClass:"arrow-next bg-[#3C4B5D]"},[t("d-icon",{attrs:{name:"el-icon-vip-youjiantou"}})],1)])])])},Si=[],Oi=b($i,Li,Si,!1,null,"94354a52",null,null);const Fi=Oi.exports,Yi="data:image/png;base64,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",Ri=""+new URL("logo-19e1cc3d.png",import.meta.url).href;const ji={__name:"index",setup(s){const a=o(!1),t=o(""),e=o(""),n=o(""),r=o({shortcuts:[{text:"近3年",onClick(p){const g=new Date,_=new Date;_.setFullYear(_.getFullYear()-3),p.$emit("pick",[_,g])}},{text:"近5年",onClick(p){const g=new Date,_=new Date;_.setFullYear(_.getFullYear()-5),p.$emit("pick",[_,g])}}]}),c=o([{operator:"张三",exportFormat:"pdf",exportTime:"2024-01-01 12:00:00"}]),u=o([{label:"操作者",prop:"operator",align:"center"},{label:"导出格式",prop:"exportFormat",align:"center"},{label:"导出时间",prop:"exportTime",align:"center"}]);return{__sfc:!0,previewVisible:a,operatorName:t,exportFormat:e,timeRange:n,pickerOptions:r,reportHistoryList:c,databaseColumns:u,handlePreviewReport:()=>{a.value=!0},DialogPreview:Fi}}};var Bi=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",{staticClass:"overflow-hidden flex justify-center"},[t("div",{staticClass:"w-462px h-804px mr-5 rounded-3px bg-[rgba(103,119,239,0.05)] flex flex-col items-center"},[a._m(0),t("div",{staticClass:"flex justify-center mt-30px"},[t("div",{staticClass:"flex flex-col items-center cursor-pointer",on:{click:e.handlePreviewReport}},[t("img",{staticClass:"w-30px h-25px",attrs:{src:Yi,alt:""}}),t("div",{staticClass:"text-[#3C4B5D] text-sm mt-10px"},[a._v("报告预览")])])])]),t(e.DialogPreview,{attrs:{visible:e.previewVisible},on:{"update:visible":function(n){e.previewVisible=n}}})],1)},Ji=[function(){var s=this,a=s._self._c;return s._self._setupProxy,a("div",{staticClass:"w-400px h-568px bg-white mt-45px"},[a("div",{staticClass:"img w-400px h-408px pt-25px"},[a("div",{staticClass:"ml-30px flex"},[a("img",{staticClass:"w-26px h-26px",attrs:{src:Ri,alt:""}}),a("div",{staticClass:"text-white flex flex-col justify-center ml-8px"},[a("p",{staticClass:"text-xs"},[s._v("xxx大学图书馆")]),a("p",{staticClass:"text-[4px]"},[s._v("UNIVERSITY LIBRARY")])])])]),a("div",{staticClass:"describe text-[#10308D] font-bold text-base px-30px"},[a("div",{staticClass:"mt-5"},[s._v("xxx大学图书馆")]),a("div",[s._v("馆藏资源绩效分析报告（机构馆藏）")]),a("div",{staticClass:"mt-26px flex justify-between text-[#000000] text-[10px] font-normal"},[a("div",[s._v("2020-10")])])])])}],Pi=b(ji,Bi,Ji,!1,null,"a9a16ccc",null,null);const Ni=Pi.exports;const Mi={__name:"index",setup(s){const a=o("organization-overview");return{__sfc:!0,currentTabTop:a,handleChangeTopTab:e=>{a.value=e.value},TABS_TOP:fe,OrganizationOverview:Be,OrganizationInfo:Te,CollectionQuantity:Fn,CollectionInfo:Jn,KeyCollection:fs,KeyCollectionInfo:xs,AchievementData:rr,AchievementInfo:pr,LiteratureBehavior:zl,LiteratureInfo:Hl,Report:Ni}}};var zi=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t("d-card",{staticClass:"mb-5 text-[#34395E]",scopedSlots:a._u([{key:"title",fn:function(){return a._l(e.TABS_TOP,function(n){return t("div",{key:n.value,staticClass:"tab-top cursor-pointer leading-64px px-10px",class:{active:e.currentTabTop===n.value},on:{click:function(r){return e.handleChangeTopTab(n)}}},[a._v(" "+a._s(n.label)+" ")])})},proxy:!0}])},[e.currentTabTop==="organization-overview"?t(e.OrganizationInfo):a._e(),e.currentTabTop==="collection-quantity"?t(e.CollectionInfo):a._e(),e.currentTabTop==="key-collection"?t(e.KeyCollectionInfo):a._e(),e.currentTabTop==="achievement-data"?t(e.AchievementInfo):a._e(),e.currentTabTop==="literature-behavior"?t(e.LiteratureInfo):a._e()],1),t("d-card",{attrs:{"content-class":"text-blue-500","title-class":"justify-between"}},[e.currentTabTop==="organization-overview"?t(e.OrganizationOverview):a._e(),e.currentTabTop==="collection-quantity"?t(e.CollectionQuantity):a._e(),e.currentTabTop==="key-collection"?t(e.KeyCollection):a._e(),e.currentTabTop==="achievement-data"?t(e.AchievementData):a._e(),e.currentTabTop==="literature-behavior"?t(e.LiteratureBehavior):a._e(),e.currentTabTop==="report"?t(e.Report):a._e()],1)],1)},Ii=[],Ei=b(Mi,zi,Ii,!1,null,"d151dbc4",null,null);const Xi=Ei.exports;export{Xi as default};
