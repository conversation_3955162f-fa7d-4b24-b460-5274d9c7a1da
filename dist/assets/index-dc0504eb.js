import{n as t,r as l}from"./element-variables-32ad61ab.js";import{D as a}from"./index-19b14be8.js";import"./main-a563c162.js";/* empty css                                                              */const _={__name:"index",setup(r){const e=l(!1);return{__sfc:!0,isVisible:e,showDrawer:()=>{console.log("showDrawer"),e.value=!0},handleBeforeClose:()=>{console.log("handleBeforeClose")},handleCancel:()=>{console.log("cancel")},DrawerBase:a}}};var c=function(){var e=this,n=e._self._c,s=e._self._setupProxy;return n("div",[n("d-card",{attrs:{"content-class":"text-blue-500 pt-0","title-class":"justify-between"},scopedSlots:e._u([{key:"title",fn:function(){return[n("div",[e._v("标题")]),n("d-button",{on:{click:s.showDrawer}},[e._v("按钮")])]},proxy:!0}])},[n("div",[e._v("内容aaaa")]),n("div",[e._v("内容bbbb")])]),n(s.DrawerBase,{attrs:{title:"测试标题",visible:s.isVisible},on:{"before-close":s.handleBeforeClose,cancel:s.handleCancel}},e._l(200,function(o){return n("div",{key:o},[e._v(" "+e._s(o)+" ")])}),0)],1)},i=[],f=t(_,c,i,!1,null,"39face9f",null,null);const h=f.exports;export{h as default};
