import{r as c,F as b,n as x,s as A,C as p,j as L}from"./element-variables-32ad61ab.js";import{f as S}from"./helpers-640ed53b.js";import"./main-a563c162.js";const E=()=>{const u=c(""),a=c({pageIndex:1,pageSize:10}),t=c([]),e=c(!1),s=c(0),n=async()=>{e.value=!0;try{const i=await b.databaseApi.getDatabaseList({...a.value,name:u.value});t.value=i.list,s.value=i.total}catch(i){console.log(i)}finally{e.value=!1}};return{databaseName:u,databaseList:t,isDatabaseListLoading:e,getDatabaseList:n,total:s,pagination:a,onCurrentPageChange:i=>{a.value.pageIndex=i,n()},onPageSizeChange:i=>{a.value.pageSize=i,n()},handleSearch:()=>{a.value.pageIndex=1,n()}}},P=[{label:"数据库名称",prop:"name",width:500},{label:"语言",prop:"lan",align:"center",width:180},{label:"采购状态",prop:"orderType",align:"center",width:180},{label:"起始年",prop:"startYear",align:"center",width:180},{label:"最新合同金额（元）",prop:"amount",align:"center",width:180},{label:"操作",prop:"btns",align:"center"}],I=[{label:"年份",prop:"year",align:"center",width:180},{label:"采购状态",prop:"orderType",align:"center",width:140},{label:"合同金额（元）",prop:"amount",width:180},{label:"合同",prop:"filePath"},{label:"操作",prop:"btns",align:"center"}],V=()=>{const u=c({}),a=c(!1),t=async r=>{a.value=!0;try{const l=await b.databaseApi.getDatabaseDetail(r);u.value=l}catch(l){console.log(l)}finally{a.value=!1}},e=c([]),s=async()=>{try{const r=await b.databaseApi.getDocType();e.value=r==null?void 0:r.map(l=>({label:l.label,value:l.type}))}catch(r){console.log(r)}},n=c([]);return{docTypes:e,getDocType:s,subjectEscs:n,getSubjectEsc:async()=>{try{const r=await b.databaseApi.getSubjectEsc();n.value=r==null?void 0:r.map(l=>({label:l.domainName,value:l.domainIdCode}))}catch(r){console.log(r)}},databaseDetail:u,isDatabaseDetailLoading:a,getDatabaseDetail:t}},T="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAApCAYAAACoYAD2AAAAAXNSR0IArs4c6QAAArRJREFUWEftmc9LFVEUxz9nfvg0zcyNLbI2BYlEVMv+gop+bIrIXrly1yJooa7eNghaGFEuVNKUkLaJy4rETN3UP5ARtUgQ8fmeM/Pm1H2NKIHg+F4wj+bALAbuPfc75/u9Z865V6iC5Xp0X7HAdYQsylksXtoho26RudykeJUuIZU6MPPvdmlzo00/ShdCKzCN8GJjndePJqVQ6RpVAdl7Uw/iMOI6XPYDfIQ5Cya8kJGHo5JPBMh717Q1U8+I63KpDBI+AhOBMpwYkNsj6QX4ksRIpiCBqm+clO5K0kSqyVSTSfvjpJr8rzR5P6uNNozVuVwtJ3P4IDC2XM/Q4KCYgqMiE1OwLudxGhV7w0cyLhrHY2DjOSXacHjsOpyPqqBFLF6FPhMFWDW+4/jcxFHvlysqT3qz2o1wAuhAESQeSFVCETLAaUs4FJp3WFHlG8ISEMSW1R8UJlgzosxK3x2dIqTDcTka52v/HlsqQRhxIAKWeay9eTTzVSEIeKPCtPTe1i8CTQr7jctYXEcYoirFUFqGpaACIVu4Y6GVLUbnRViUvqw+UKEdOLIXuiNaXBGOm/4mVNSCvMJP4AdKUBZRHItAivIO4b3039IzCodFaA8VseJqUggUmlGyts3JUkgJ+IqyIMJMGFKw5U+Ed2ubOEolPnk+n+VZj7rfPey1UrwduLngagNeS542y+ap63AxasQWRJnEZnStidXMCs5uAW4f11TEp5MgHg07rBS1tM9dhyubeVKF8fVmhgYGZGMvALfPqQrImmhp0wIj9p9gB3HVHN2JPWapOU2mkawk4aYb51+koJo4C0r6xhk2BUaiT3rF5QnKDQVz2zBvCeOJOjM3LW0ddIdwAWgR4W0oTDWsM5uYKxJTOC8VOBUo537328dEmS56zBzoZCWXE9PrVGS/AIZAKepMJiT/AAAAAElFTkSuQmCC";const z={__name:"dialog-database-detail",props:{databaseId:{type:String,required:!0},visible:{type:Boolean,default:!1}},emits:["update:visible","confirm","cancel"],setup(u,{emit:a}){const t=u,e=A.useSelect(),{docTypes:s,getDocType:n,subjectEscs:f,getSubjectEsc:r,databaseDetail:l,isDatabaseDetailLoading:i,getDatabaseDetail:y}=V(),d=()=>{if(!l.value.list.every(o=>o.year&&o.orderType&&o.amount)){p.message({type:"error",message:"请填写完整信息！",showClose:!0,center:!0});return}if(new Set(l.value.list.map(o=>o.year)).size!==l.value.list.length){p.message({type:"error",message:"年份不能重复！",showClose:!0,center:!0});return}l.value.list.forEach(o=>{o.srcdb||(o.srcdb=l.value.srcdb)}),a("confirm",l.value.list)},g=()=>{a("cancel")},h=o=>{l.value.list.splice(o.$index,1)},C=()=>{console.log("下载合同")},w=()=>{l.value.list.push({year:"",orderType:"",amount:"",filePath:""})},m=o=>new Promise((D,_)=>{if(!(o.size/1024/1024<10))return p.message({type:"error",message:"上传文件大小不能超过 10MB!",showClose:!0,center:!0}),_();D()}),v=async(o,D)=>{const _=new FormData;_.append("file ",o.file),(await b.databaseApi.uploadFile(_)).statusCode===200?(p.message({type:"success",message:"上传成功！",showClose:!0,center:!0}),D.filePath=o.file.name):p.message({type:"error",message:"上传失败！",showClose:!0,center:!0})};return(async()=>(await n(),await r(),await y(t.databaseId)))(),{__sfc:!0,props:t,emit:a,selectStore:e,docTypes:s,getDocType:n,subjectEscs:f,getSubjectEsc:r,databaseDetail:l,isDatabaseDetailLoading:i,getDatabaseDetail:y,handleConfirm:d,handleCancel:g,delContract:h,downLoadContract:C,handleAdd:w,beforeUpload:m,uploadFile:v,detailColumns:I,findLabelByValue:S}}};var F=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("d-dialog",{attrs:{title:"数据库详情",type:"ok",width:"1200px",visible:e.props.visible},on:{"update:visible":function(s){return e.emit("update:visible",s)}},scopedSlots:a._u([{key:"footer",fn:function(){return[t("d-button",{attrs:{type:"gray-light",icon:"el-icon-vip-quxiao"},on:{click:e.handleCancel}},[a._v(" 取消 ")]),t("d-button",{attrs:{type:"primary",icon:"el-icon-vip-baocun1"},on:{click:e.handleConfirm}},[a._v(" 保存 ")])]},proxy:!0}])},[t("el-form",{attrs:{inline:"",model:e.databaseDetail}},[t("el-form-item",{attrs:{label:"数据库名称"}},[t("span",[a._v(a._s(e.databaseDetail.name))])]),t("el-form-item",{attrs:{label:"采购状态"}},[t("span",[a._v(a._s(e.findLabelByValue(e.selectStore.selects.order_type,e.databaseDetail.orderType)))])]),t("el-form-item",{attrs:{label:"起始年"}},[t("span",[a._v(a._s(e.databaseDetail.year??"-"))])]),t("el-form-item",{attrs:{label:"数据库出版商"}},[t("span",[a._v(a._s(e.databaseDetail.supplierName||"-"))])]),t("el-form-item",{attrs:{label:"学科分类"}},[t("span",{attrs:{title:e.findLabelByValue(e.subjectEscs,e.databaseDetail.subjectEsc)}},[a._v(" "+a._s(e.findLabelByValue(e.subjectEscs,e.databaseDetail.subjectEsc))+" ")])]),t("el-form-item",{attrs:{label:"语言"}},[t("span",[a._v(a._s(e.findLabelByValue(e.selectStore.selects.lan,e.databaseDetail.lan)))])]),t("el-form-item",{attrs:{label:"文献类型"}},[t("span",{attrs:{title:e.findLabelByValue(e.docTypes,e.databaseDetail.type)}},[a._v(" "+a._s(e.findLabelByValue(e.docTypes,e.databaseDetail.type))+" ")])]),t("el-form-item",{attrs:{label:"数据库简介"}},[t("span",[a._v(a._s(e.databaseDetail.briefIntro||"-"))])])],1),t("div",{staticClass:"flex justify-between gap-10px items-end border-1 border-[#E4E6FC] p-10px"},[t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseDetailLoading,expression:"isDatabaseDetailLoading"}],staticClass:"w-[calc(100%-30px)]",attrs:{data:e.databaseDetail.list,columns:e.detailColumns},scopedSlots:a._u([{key:"year-th",fn:function(s){return[t("span",{staticClass:"flex items-center justify-center"},[t("span",{staticClass:"text-red-300"},[a._v("*")]),a._v(" "+a._s(s.column.label)+" ")])]}},{key:"orderType-th",fn:function(s){return[t("span",{staticClass:"flex items-center justify-center"},[t("span",{staticClass:"text-red-300"},[a._v("*")]),a._v(" "+a._s(s.column.label)+" ")])]}},{key:"amount-th",fn:function(s){return[t("span",{staticClass:"flex items-center justify-center"},[t("span",{staticClass:"text-red-300"},[a._v("*")]),a._v(" "+a._s(s.column.label)+" ")])]}},{key:"year",fn:function(s){return[t("el-date-picker",{staticClass:"!w-140px",attrs:{type:"year",placeholder:"选择年份",size:"medium","value-format":"yyyy",clearable:""},model:{value:s.row.year,callback:function(n){a.$set(s.row,"year",n)},expression:"scope.row.year"}})]}},{key:"orderType",fn:function(s){return[t("el-select",{attrs:{placeholder:"请选择",size:"medium",clearable:""},model:{value:s.row.orderType,callback:function(n){a.$set(s.row,"orderType",n)},expression:"scope.row.orderType"}},a._l(e.selectStore.selects.order_type,function(n){return t("el-option",{key:n.value,attrs:{label:n.label,value:n.value}})}),1)]}},{key:"amount",fn:function(s){return[t("el-input",{attrs:{placeholder:"请输入金额",size:"medium",clearable:""},model:{value:s.row.amount,callback:function(n){a.$set(s.row,"amount",n)},expression:"scope.row.amount"}})]}},{key:"filePath",fn:function(s){return[t("div",{staticClass:"flex justify-center items-center"},[s.row.filePath?t("div",{staticClass:"flex-1 truncate cursor-pointer text-[#6777EF] hover:underline",on:{click:e.downLoadContract}},[a._v(" "+a._s(s.row.filePath)+" ")]):a._e(),t("el-upload",{ref:"uploadfile",attrs:{action:"/",multiple:!1,"http-request":n=>e.uploadFile(n,s.row),"show-file-list":!1,"before-upload":e.beforeUpload}},[t("d-button",{attrs:{type:"primary-light",border:"",size:"mini"}},[a._v(" 上传 ")])],1)],1)]}},{key:"btns",fn:function(s){return[t("d-button",{attrs:{type:"danger-light",icon:"el-icon-vip-icon-shanchu1",size:"mini",confirm:{content:"是否确认删除？"}},on:{click:function(n){return e.delContract(s)}}},[a._v(" 删除 ")])]}}])}),t("div",{staticClass:"border-1 border-[#E4E6FC] w-36px h-36px bg-white flex items-center justify-center cursor-pointer flex-shrink-0 rounded-3px",on:{click:function(s){return s.stopPropagation(),e.handleAdd.apply(null,arguments)}}},[t("img",{staticClass:"w-5 h-5",attrs:{src:T,alt:""}})])],1)],1)},j=[],U=x(z,F,j,!1,null,"6fc307d0",null,null);const B=U.exports;const M={__name:"index",setup(u){const a=A.useSelect(),{databaseName:t,databaseList:e,isDatabaseListLoading:s,getDatabaseList:n,total:f,pagination:r,onCurrentPageChange:l,onPageSizeChange:i,handleSearch:y}=E(),d=c(!1),g=c(""),h=async m=>{g.value=m.id,await L(),d.value=!0},C=async m=>{try{(await b.databaseApi.saveDatabase(m)).statusCode===200&&(p.message({type:"success",message:"保存成功！",showClose:!0,center:!0}),await n(),d.value=!1)}catch(v){console.log(v)}},w=()=>{d.value=!1};return(async()=>await n())(),{__sfc:!0,selectStore:a,databaseName:t,databaseList:e,isDatabaseListLoading:s,getDatabaseList:n,total:f,pagination:r,onCurrentPageChange:l,onPageSizeChange:i,handleSearch:y,isDialogVisible:d,databaseIdForEdit:g,handleEdit:h,handleConfirm:C,handleCancel:w,listColumns:P,DialogDatabaseDetail:B,findLabelByValue:S}}};var N=function(){var a=this,t=a._self._c,e=a._self._setupProxy;return t("div",[t("d-card",{attrs:{"content-class":"text-blue-500","title-class":"justify-between"},scopedSlots:a._u([{key:"title",fn:function(){return[t("div",{staticClass:"text-[#6777EF]"},[t("d-icon",{attrs:{name:"el-icon-vip-biaoge"}},[a._v("数据库管理")])],1)]},proxy:!0}])},[t("div",{staticClass:"flex items-center gap-20px mb-5"},[t("el-input",{staticClass:"!w-400px",attrs:{placeholder:"请输入数据库名称",size:"medium",clearable:""},on:{clear:e.handleSearch},model:{value:e.databaseName,callback:function(s){e.databaseName=s},expression:"databaseName"}}),t("d-button",{attrs:{type:"primary",icon:"el-icon-vip-sousuo"},on:{click:e.handleSearch}},[a._v(" 查找 ")])],1),t("d-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isDatabaseListLoading,expression:"isDatabaseListLoading"}],attrs:{data:e.databaseList,"use-sticky-header":"",columns:e.listColumns,pagination:{max:1/0,pageSizes:[10,20,50,100,150,200],total:e.total,pageSize:e.pagination.pageSize,currentPage:e.pagination.pageIndex,onCurrentPageChange:e.onCurrentPageChange,onPageSizeChange:e.onPageSizeChange},"null-text":"-"},scopedSlots:a._u([{key:"lan",fn:function(s){return[t("span",[a._v(a._s(e.findLabelByValue(e.selectStore.selects.lan,s.row.lan)))])]}},{key:"orderType",fn:function(s){return[t("span",[a._v(a._s(e.findLabelByValue(e.selectStore.selects.order_type,s.row.orderType)))])]}},{key:"btns",fn:function(s){return[t("d-button",{staticClass:"!mr-2",attrs:{size:"mini",icon:"el-icon-vip-bianjizuopin",type:"primary-light"},on:{click:function(n){return e.handleEdit(s.row)}}},[a._v(" 编辑 ")])]}}])})],1),e.isDialogVisible?t(e.DialogDatabaseDetail,{attrs:{"database-id":e.databaseIdForEdit,visible:e.isDialogVisible},on:{"update:visible":function(s){e.isDialogVisible=s},confirm:e.handleConfirm,cancel:e.handleCancel}}):a._e()],1)},J=[],q=x(M,N,J,!1,null,"742b2d34",null,null);const O=q.exports;export{O as default};
