function l(r,t){if(typeof r!="object"||r===null)return t;if(typeof t!="object"||t===null)return r;const a=Array.isArray(r)?[...r]:{...r};return Object.keys(t).forEach(s=>{const n=t[s],e=a[s];Array.isArray(n)&&Array.isArray(e)?a[s]=n.map((i,o)=>l(e[o]||{},i)):typeof n=="object"&&!Array.isArray(n)?a[s]=l(e||{},n):a[s]=n}),a}const c=(r,t=0)=>{if(isNaN(r)||r===null||r===void 0)return"0";const s=Number(r).toFixed(t).toString().replace(/,/g,"").split(".");let n=s[0];const e=s.length>1?`.${s[1]}`:"";return n=n.replace(/\B(?=(\d{3})+(?!\d))/g,","),n+e},u=(r="#6777EF",t)=>{r=r.replace("#",""),r.length===3&&(r=r.split("").map(e=>e+e).join(""));const a=parseInt(r.substring(0,2),16),s=parseInt(r.substring(2,4),16),n=parseInt(r.substring(4,6),16);return`rgba(${a}, ${s}, ${n}, ${t})`},f=(r=[],t,a="-")=>{const s=n=>{const e=r.find(i=>i.value===n);return e?e.label:a};return t!=null&&t.includes(";")?t.split(";").map(s).join(";"):s(t)};export{l as d,f,u as h,c as t};
