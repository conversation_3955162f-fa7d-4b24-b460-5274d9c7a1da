import _ from 'lodash';

export function useAsyncComputed(mapper, options) {
  const computedValue = ref();
  const loading = ref(false);

  if (!options) {
    watchEffect(async () => {
      loading.value = true;
      const newValue = await mapper();
      computedValue.value = newValue;
      loading.value = false;
    });
    return [loading, computedValue];
  }

  const debouncedTask = _.debounce(async () => {
    const newValue = await mapper();
    computedValue.value = newValue;
    loading.value = false;
  }, options?.debounce);

  if (options?.trigger) {
    watch(
      options.trigger,
      () => {
        loading.value = true;
        debouncedTask();
      },
      { immediate: true },
    );
  }

  return [loading, computedValue];
}
