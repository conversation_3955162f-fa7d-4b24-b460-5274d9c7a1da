import store from '@/store';
import hook from '@/hooks';

export const DPermission = {
  install(Vue) {
    Vue.directive('permission', {
      inserted(el, binding) {
        const { value } = binding;
        // const permissionStore = store.usePermission();
        // const route = hook.useRoute();
        // let list = [];

        // const findCurrentRoutePermissionList = (tree) => {
        //   for (const node of tree.permissionNodes || []) {
        //     if (node.router === route.value.path) {
        //       list = node.listPermission;
        //       return;
        //     }
        //     findCurrentRoutePermissionList(node.permissionNodes || []);
        //   }
        // };
        // findCurrentRoutePermissionList(permissionStore.tree);
        // const hasPermission = permissionStore.tree.listPermission.includes(value);
        // if (!hasPermission) el.remove();
        const hasPermission = isPermitted(value);
        // 清除添加了disabled修饰符的元素的点击事件
        if (!hasPermission && binding.modifiers.disabled) {
          // eslint-disable-next-line no-self-assign
          el.outerHTML = el.outerHTML;
          return;
        }
        if (!hasPermission) el.remove();
      },
    });
  },
};

export const isPermitted = (value) => {
  const permissionStore = store.usePermission();
  return permissionStore.tree.listPermission?.includes(value);
};

export const executeIfPermitted = (permission, callback) => {
  if (isPermitted(permission)) {
    callback();
  } else {
    DMessage.message({ type: 'error', message: '当前没有该操作权限，请联系管理员！', showClose: true, center: true });
  }
};
