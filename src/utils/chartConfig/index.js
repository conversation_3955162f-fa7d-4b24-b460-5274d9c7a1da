// 导入所有图表配置函数
import { createLine<PERSON><PERSON> } from './createLine<PERSON><PERSON>';
import { createBar<PERSON><PERSON> } from './createBar<PERSON><PERSON>';
import { createPie<PERSON><PERSON> } from './createPie<PERSON>hart';
import { createScatter<PERSON><PERSON> } from './createScatter<PERSON><PERSON>';
import { createFunnel<PERSON><PERSON> } from './createFunnel<PERSON><PERSON>';
import { createLiquidfill<PERSON><PERSON> } from './createLiquidfill<PERSON><PERSON>';
import { createTreemap<PERSON><PERSON> } from './createTreemap<PERSON><PERSON>';
import { createWordcloud<PERSON><PERSON> } from './createWordcloudC<PERSON>';

// 导出所有图表配置函数
export {
  createLine<PERSON><PERSON>,
  createBar<PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON>,
  create<PERSON><PERSON>ter<PERSON><PERSON>,
  createF<PERSON>nel<PERSON><PERSON>,
  createLiquid<PERSON><PERSON><PERSON>,
  createTree<PERSON><PERSON><PERSON><PERSON>,
  createWord<PERSON>loud<PERSON><PERSON>,
};

// 默认导出所有函数
export default {
  createLine<PERSON><PERSON>,
  createB<PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON>,
  create<PERSON>cat<PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  createLiquid<PERSON><PERSON><PERSON>,
  createTree<PERSON><PERSON><PERSON><PERSON>,
  createWord<PERSON>loud<PERSON><PERSON>,
};
