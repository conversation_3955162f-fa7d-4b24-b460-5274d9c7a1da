import { deepMerge, toThousandsSeparator } from '../helpers';

/**
 * 创建柱状图的通用配置
 * @param {Object} data - 图表数据
 *    @param {string[]} data.xAxisData - X轴类目数据
 *    @param {Array} data.barData - 柱状图数据数组，每项包含 name 和 data 属性
 * @param {Object} [options] - 自定义配置项
 *    @param {string[]} [options.colors] - 颜色数组，默认 ['#6777EF', '#36BDFF', '#F2A940', '#62AC00']
 *    @param {boolean} [options.horizontal] - 是否为横向柱状图，默认 false
 *    @param {Object} [options.customConfig] - 深度合并的自定义配置（支持任意层级）
 * @returns {Object} ECharts配置对象
 */
export function createBarChart(data, options = {}) {
  const { colors = ['#6777EF', '#36BDFF', '#F2A940', '#62AC00'], horizontal = false, customConfig = {} } = options;

  const { xAxisData, barData } = data;

  // 生成默认系列配置
  const defaultSeries = barData.map((item, index) => ({
    name: item.name,
    type: 'bar',
    data: item.data,
    color: colors[index % colors.length],
    barMaxWidth: 60,
    itemStyle: {
      borderRadius: [5, 5, 5, 5], // [topLeft, topRight, bottomRight, bottomLeft]
    },
    // 可以根据需要添加更多默认配置
    label: {
      show: false,
      position: horizontal ? 'right' : 'top',
      formatter: (params) => toThousandsSeparator(params.value),
    },
    emphasis: {
      focus: 'series',
    },
  }));

  // 基础配置
  const baseConfig = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      // formatter: (params) => {
      //   let result = `${params[0].axisValue}<br/>`;
      //   params.forEach((param) => {
      //     result += `${param.marker} ${param.seriesName}: ${toThousandsSeparator(param.value)}<br/>`;
      //   });
      //   return result;
      // },
    },
    legend: {
      data: barData.map((item) => item.name),
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      bottom: 0,
    },
    grid: {
      top: 10,
      bottom: 30,
      left: horizontal ? 0 : 0,
      right: horizontal ? 0 : 0,
      containLabel: true,
    },
    // 根据方向设置轴
    xAxis: horizontal
      ? {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            color: '#D7DADB',
            type: 'dashed',
          },
        },
        axisTick: { show: false },
        axisLabel: {
          formatter: (value) => toThousandsSeparator(value),
          color: '#909399',
        },
      }
      : {
        type: 'category',
        data: xAxisData,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#D7DADB',
            width: 2,
          },
        },
        axisTick: { show: false },
        axisLabel: {
          rotate: xAxisData.length > 10 ? 45 : 0,
          textStyle: {
            color: '#909399',
            fontSize: 12,
          },
        },
      },
    yAxis: horizontal
      ? {
        type: 'category',
        data: xAxisData,
        axisLine: {
          show: true,
          lineStyle: { color: '#D7DADB', width: 2 },
        },
        axisTick: { show: false },
        axisLabel: {
          color: '#909399',
        },
      }
      : {
        type: 'value',
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          formatter: (value) => toThousandsSeparator(value),
          textStyle: {
            color: '#909399',
            fontSize: 12,
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#D7DADB',
            type: 'dashed',
          },
        },
      },
    series: defaultSeries,
  };

  // 深度合并自定义配置
  return deepMerge(baseConfig, customConfig);
}

export default createBarChart;
