import { deepMerge } from '../helpers';

/**
 * 创建词云图的通用配置
 * @param {Object} data - 图表数据
 *    @param {Array} data.wordcloudData - 词云数据数组，每项包含 name 和 value 属性
 * @param {Object} [options] - 自定义配置项
 *    @param {string[]} [options.colors] - 颜色数组，默认 ['#6777EF', '#36BDFF', '#F2A940', '#62AC00']
 *    @param {number} [options.sizeRange] - 字体大小范围，默认 [12, 60]
 *    @param {number} [options.rotationRange] - 旋转角度范围，默认 [-90, 90]
 *    @param {string} [options.shape] - 词云形状，默认 'circle'
 *    @param {Object} [options.customConfig] - 深度合并的自定义配置（支持任意层级）
 * @returns {Object} ECharts配置对象
 */
export function createWordcloudChart(data, options = {}) {
  const {
    colors = ['#6777EF', '#36BDFF', '#F2A940', '#62AC00'],
    sizeRange = [12, 60],
    rotationRange = [-90, 90],
    shape = 'pentagon',
    customConfig = {},
  } = options;

  const { wordcloudData } = data;

  // 基础配置
  const baseConfig = {
    series: [
      {
        type: 'wordCloud',
        // 词云形状
        shape: shape,
        // 词云大小
        left: 'center',
        top: 'center',
        width: '100%',
        height: '100%',
        // 文字样式
        textStyle: {
          fontWeight: 'normal',
          // 文字颜色
          color: function () {
            return colors[Math.floor(Math.random() * colors.length)];
          },
        },
        // 字体大小范围
        sizeRange: sizeRange,
        // 旋转角度范围
        rotationRange: rotationRange,
        // 旋转步进角度
        rotationStep: 45,
        // 网格大小，影响词云密度
        gridSize: 8,
        // 绘制超时时间
        drawOutOfBound: false,
        // 是否允许重叠
        layoutAnimation: true,
        // 数据
        data: wordcloudData.map((item) => ({
          name: item.name,
          value: item.value,
          // 鼠标悬停样式
          emphasis: {
            textStyle: {
              fontWeight: 'bold',
              opacity: 1,
            },
          },
        })),
      },
    ],
    // 提示框配置
    tooltip: {
      show: true,
      trigger: 'item',
      formatter: function (params) {
        return `${params.name}: ${params.value}`;
      },
    },
    // 动画配置
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',
    animationDelay: function (idx) {
      return idx * 100;
    },
  };

  // 深度合并自定义配置
  return deepMerge(baseConfig, customConfig);
}

export default createWordcloudChart;
