import { deepMerge, toThousandsSeparator } from '../helpers';

/**
 * 创建漏斗图的通用配置
 * @param {Object} data - 图表数据
 *    @param {Array} data.funnelData - 漏斗图数据数组，每项包含 name 和 value 属性
 * @param {Object} [options] - 自定义配置项
 *    @param {string[]} [options.colors] - 颜色数组，默认 ['#6777EF', '#36BDFF', '#F2A940', '#62AC00']
 *    @param {boolean} [options.sort] - 排序方式，可选 'ascending'(升序), 'descending'(降序), 'none'(不排序)，默认 'descending'
 *    @param {number} [options.gap] - 漏斗图各层间距，默认 2
 *    @param {Object} [options.customConfig] - 深度合并的自定义配置（支持任意层级）
 * @returns {Object} ECharts配置对象
 */
export function createFunnelChart(data, options = {}) {
  const {
    colors = ['#6777EF', '#36BDFF', '#F2A940', '#62AC00'],
    sort = 'descending',
    gap = 2,
    customConfig = {},
  } = options;

  const { funnelData } = data;

  // 基础配置
  const baseConfig = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        return `${params.data.name}: ${params.data.value}`;
      },
    },
    legend: {
      data: funnelData.map((item) => item.name),
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      bottom: 0,
      // orient: 'vertical',
    },
    series: [
      {
        name: '漏斗图',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: Math.max(...funnelData.map((item) => item.value)) * 1.1,
        minSize: '0%',
        maxSize: '100%',
        sort: sort,
        gap: gap,
        label: {
          show: true,
          position: 'inside',
          formatter: (params) => {
            return `${params.data.name}: ${params.value}`;
          },
          fontSize: 14,
          color: '#fff',
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid',
          },
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
        },
        emphasis: {
          label: {
            fontSize: 16,
            fontWeight: 'bold',
          },
        },
        data: funnelData,
        color: colors,
      },
    ],
  };

  // 深度合并自定义配置
  return deepMerge(baseConfig, customConfig);
}

export default createFunnelChart;
