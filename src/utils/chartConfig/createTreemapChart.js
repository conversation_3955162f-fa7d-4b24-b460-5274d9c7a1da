import { deepMerge, hexToRgba } from '../helpers';

/**
 * 创建矩形树图的通用配置
 * @param {Object} data - 图表数据
 *    @param {Array} data.treeData - 树图数据，层级结构的数组，每项包含 name, value 和可选的 children 属性
 * @param {Object} [options] - 自定义配置项
 *    @param {string[]} [options.colors] - 颜色数组，默认 ['#6777EF', '#36BDFF', '#F2A940', '#62AC00']
 *    @param {boolean} [options.showLabel] - 是否显示标签，默认 true
 *    @param {number} [options.levels] - 显示的层级数，默认 1
 *    @param {boolean} [options.roam] - 是否开启缩放和平移，默认 false
 *    @param {Object} [options.customConfig] - 深度合并的自定义配置（支持任意层级）
 * @returns {Object} ECharts配置对象
 */
export function createTreemapChart(data, options = {}) {
  const {
    colors = ['#6777EF', '#00ABFF', '#B566FF', '#2067FF', '#D8A500', '#3877FF', '#00BEBE', '#636BFF'],
    showLabel = true,
    levels = 1,
    roam = false,
    customConfig = {},
  } = options;

  const { treeData } = data;

  // 基础配置
  const baseConfig = {
    tooltip: {
      show: true,
    },
    series: [
      {
        type: 'treemap',
        data: treeData.map((item, index) => ({ ...item, itemStyle: { color: colors[index % colors.length] } })),
        // 视觉配置
        visibleMin: 300,
        label: {
          show: showLabel,
          formatter: '{b}: {c}',
          position: 'inside',
          fontSize: 14,
          color: '#fff',
        },
        // 高亮样式
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
          },
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        // // 层级配置
        // levels: Array.from({ length: levels }, (_, index) => ({
        //   itemStyle: {
        //     borderColor: '#fff',
        //     borderWidth: 1,
        //     gapWidth: 1,
        //     color: colors[index % colors.length],
        //   },
        //   upperLabel: {
        //     show: index === 0,
        //     height: 30,
        //   },
        // })),
        // 交互配置
        roam: roam,
        // 动画配置
        animationDuration: 1000,
        animationEasing: 'quinticInOut',
      },
    ],
  };

  // 深度合并自定义配置
  return deepMerge(baseConfig, customConfig);
}

export default createTreemapChart;
