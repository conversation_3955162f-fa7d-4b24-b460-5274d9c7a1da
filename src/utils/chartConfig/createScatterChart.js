import { deepMerge, hexToRgba, toThousandsSeparator } from '../helpers';

/**
 * 创建散点图的通用配置
 * @param {Object} data - 图表数据
 *    @param {Array} data.scatterData - 散点图数据数组，每项包含 name 和 data 属性，data 是二维数组 [[x1,y1], [x2,y2], ...]
 * @param {Object} [options] - 自定义配置项
 *    @param {string[]} [options.colors] - 颜色数组，默认 ['#6777EF', '#36BDFF', '#F2A940', '#62AC00']
 *    @param {boolean} [options.showSymbol] - 是否显示标记点，默认 true
 *    @param {string} [options.symbolSize] - 标记点大小，默认 10
 *    @param {string} [options.minSymbolSize] - 最小标记点大小，默认 6
 *    @param {string} [options.maxSymbolSize] - 最大标记点大小，默认 50
 *    @param {Object} [options.customConfig] - 深度合并的自定义配置（支持任意层级）
 * @returns {Object} ECharts配置对象
 */
export function createScatterChart(data, options = {}) {
  const {
    colors = ['#6777EF', '#36BDFF', '#F2A940', '#62AC00'],
    showSymbol = true,
    symbolSize = 10,
    minSymbolSize = 6, // 最小符号大小
    maxSymbolSize = 50, // 最大符号大小
    customConfig = {},
  } = options;

  const { scatterData } = data;

  // 找出所有数据中的最大值和最小值（假设第二个值是我们要映射的值）
  const allValues = [];
  scatterData.forEach((series) => {
    series.data.forEach((point) => {
      if (Array.isArray(point) && point.length >= 2) {
        allValues.push(point[1]);
      }
    });
  });

  const minValue = Math.min(...allValues);
  const maxValue = Math.max(...allValues);

  // 如果最大值等于最小值，则不需要归一化
  const valueRange = maxValue - minValue;
  const sizeRange = maxSymbolSize - minSymbolSize;

  // 归一化函数
  const normalizeSize = (value) => {
    if (valueRange === 0) return (minSymbolSize + maxSymbolSize) / 2;
    return minSymbolSize + (sizeRange * (value - minValue)) / valueRange;
  };

  // 生成默认系列配置
  const defaultSeries = scatterData.map((item, index) => ({
    name: item.name,
    type: 'scatter',
    data: item.data,
    symbolSize:
      valueRange === 0
        ? symbolSize
        : function (dataItem) {
          return normalizeSize(dataItem[1]);
        },
    symbol: showSymbol ? 'circle' : 'none', // 标记的类型
    itemStyle: {
      // 在这里设置边框
      borderColor: colors[index % colors.length],
      borderWidth: 1, // 设置边框宽度
      color: hexToRgba(colors[index % colors.length], 0.5),
    },
    emphasis: {
      focus: 'series',
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        borderWidth: 2,
      },
    },
  }));

  // 其余代码保持不变
  const marker =
    '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:8px;height:8px;background-color:rgba(35,85,236,0.2);border-width:1px;border-style:solid;border-color:rgba(35,85,236,0.5);" ></span>';

  // 基础配置
  const baseConfig = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        console.log(params);
        return `${marker}<span style="color:#909399;vertical-align:middle;">数据库</span>
                <span style="font-size:16px;font-weight:bold;color:#333;vertical-align:middle;">${
  params.seriesName
}</span>
                <br/>
                <div style="width:100%;height:1px;background:#EBEEF5;margin-top:12px;margin-bottom:12px;"></div>
                ${marker}<span style="color:#909399;vertical-align:middle;">不重复文献</span>
                <span style="font-size:16px;font-weight:bold;color:#333333;vertical-align:middle;">${toThousandsSeparator(
    params.value[1],
  )}</span>`;
      },
    },
    legend: {
      data: scatterData.map((item) => item.name),
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      bottom: 0,
    },
    grid: {
      top: 20,
      bottom: 20,
      left: 0,
      right: 0,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      scale: true,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#D7DADB',
          width: 2,
        },
      },
      axisLabel: {
        textStyle: {
          color: '#909399',
        },
      },
      axisTick: { show: true, inside: true },
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        formatter: (value) => toThousandsSeparator(value),
        textStyle: {
          color: '#909399',
          fontSize: 12,
        },
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    series: defaultSeries,
  };

  // 深度合并自定义配置
  return deepMerge(baseConfig, customConfig);
}

export default createScatterChart;
