import { deepMerge } from '../helpers';

/**
 * 创建水球图的通用配置
 * @param {Object} data - 图表数据
 *    @param {number|number[]} data.value - 水球图的值，可以是单个数值(0-1)或数组表示多层水球
 *    @param {string} [data.text] - 水球图中心显示的文本，默认显示百分比
 * @param {Object} [options] - 自定义配置项
 *    @param {string|string[]} [options.colors] - 颜色，可以是单个颜色或颜色数组，默认 ['#6777EF']
 *    @param {boolean} [options.waveAnimation] - 是否开启波浪动画，默认 true
 *    @param {number} [options.amplitude] - 波浪振幅，默认 20px
 *    @param {number} [options.waveSpeed] - 向前移动波长所需的毫秒数，默认 3000毫秒
 *    @param {Object} [options.customConfig] - 深度合并的自定义配置（支持任意层级）
 * @returns {Object} ECharts配置对象
 */
export function createLiquidfillChart(data, options = {}) {
  const { colors = ['#6777EF'], waveAnimation = true, amplitude = 20, waveSpeed = 3000, customConfig = {} } = options;

  const { value, text } = data;

  // 确保值在0-1之间
  const normalizeValue = (val) => {
    if (typeof val !== 'number') return 0;
    return Math.max(0, Math.min(1, val));
  };

  // 处理单个值或数组值
  const values = Array.isArray(value) ? value.map(normalizeValue) : [normalizeValue(value)];

  // 处理单个颜色或颜色数组
  const colorArray = Array.isArray(colors) ? colors : [colors];

  // 基础配置
  const baseConfig = {
    series: [
      {
        type: 'liquidFill',
        radius: '80%',
        center: ['50%', '50%'],
        data: values.map((val, index) => ({
          value: val,
          itemStyle: {
            color: colorArray[index % colorArray.length],
            opacity: 0.8 - index * 0.1, // 透明度随层数递减
          },
          // 确保每层波浪都有动画
          waveAnimation: waveAnimation,
        })),
        // 波浪配置
        amplitude: amplitude,
        waveAnimation: waveAnimation,
        animationDuration: 500,
        animationDurationUpdate: 1000,
        period: waveSpeed,
        // 波浪数量和形状
        waveLength: '80%',
        waveHeight: 30,
        wavesNumber: 3,
        // 外观配置
        outline: {
          show: true,
          borderDistance: 5,
          itemStyle: {
            borderColor: colorArray[0],
            borderWidth: 2,
          },
        },
        // 背景配置
        backgroundStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
        // 标签配置
        label: {
          show: true,
          position: ['50%', '50%'],
          formatter:
            text ||
            function (param) {
              return (param.value * 100).toFixed(2) + '%';
            },
          fontSize: 30,
          fontWeight: 'bold',
          color: colorArray[0],
        },
        // 确保动画开启
        animation: true,
      },
    ],
  };

  // 深度合并自定义配置
  return deepMerge(baseConfig, customConfig);
}

export default createLiquidfillChart;
