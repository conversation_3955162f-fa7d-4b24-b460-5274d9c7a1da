import { deepMerge, hexToRgba } from '../helpers';

/**
 * 创建折线图的通用配置
 * @param {Object} data - 图表数据 { xAxisData: string[], lineData: Array<{ name: string, data: number[] }> }
 * @param {Object} [options] - 自定义配置选项
 * @param {string[]} [options.colors] - 颜色数组，默认为 ['#6777EF', '#B566FF', '#36BDFF']
 * @param {Object} [options.customConfig] - 深度合并的自定义配置（支持任意层级）
 * @returns {Object} ECharts 配置对象
 */
export function createLineChart(data, options = {}) {
  const { colors = ['#6777EF', '#B566FF', '#36BDFF'], customConfig = {} } = options;

  const { xAxisData, lineData } = data;
  // 生成默认系列配置
  const defaultSeries = lineData.map((item, index) => ({
    smooth: true,
    name: item.name,
    type: 'line',
    data: item.data,
    color: colors[index],
    showSymbol: false, // 不显示小圆点
    yAxisIndex: typeof item.yAxisIndex === 'number' ? item.yAxisIndex : 0, // 支持指定y轴
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: hexToRgba(colors[index], 0.3) },
          { offset: 1, color: hexToRgba(colors[index], 0) },
        ],
      },
    },
  }));

  // 基础配置
  const baseConfig = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: lineData.map((item) => item.name),
      icon: 'circle',
      bottom: 0,
      itemGap: 40,
      itemWidth: 8,
      itemHeight: 8,
    },
    grid: {
      top: 20,
      bottom: 20,
      left: 0,
      right: 0,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#D7DADB',
          width: 2, // 设置x轴线宽为2px
        },
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        inside: true, // 设置刻度朝内
      },
      axisLabel: {
        textStyle: {
          color: '#909399',
          fontSize: 12,
        },
      },
    },
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          textStyle: {
            color: '#909399',
            fontSize: 12,
          },
        },
        splitLine: {
          lineStyle: {
            color: '#D7DADB',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        position: 'right',
        axisLabel: {
          textStyle: {
            color: '#909399',
            fontSize: 12,
          },
        },
        splitLine: { show: false }, // 右轴不显示分割线
      },
    ],
    series: defaultSeries,
  };

  // 深度合并自定义配置
  return deepMerge(baseConfig, customConfig);
}

export default createLineChart;
