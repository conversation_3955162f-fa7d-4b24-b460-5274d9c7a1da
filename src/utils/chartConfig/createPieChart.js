import { deepMerge, toThousandsSeparator } from '../helpers';

/**
 * 创建饼图的通用配置
 * @param {Object} data - 图表数据
 *    @param {Array} data.pieData - 饼图数据数组，每项包含 name 和 value 属性
 * @param {Object} [options] - 自定义配置项
 *    @param {string[]} [options.colors] - 颜色数组，默认 ['#6777EF', '#36BDFF', '#F2A940', '#62AC00']
 *    @param {boolean} [options.showTotal] - 是否在中间显示总计，默认 true
 *    @param {Object} [options.customConfig] - 深度合并的自定义配置（支持任意层级）
 * @returns {Object} ECharts配置对象
 */
export function createPieChart(data, options = {}) {
  const { colors = ['#6777EF', '#36BDFF', '#F2A940', '#62AC00'], showTotal = true, customConfig = {} } = options;

  const { pieData } = data;

  // 计算总计
  const total = pieData.reduce((sum, item) => sum + (Number(item.value) || 0), 0);

  // 基础配置
  const baseConfig = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        return `${params.data.name}: ${toThousandsSeparator(params.data.value)}`;
      },
    },
    legend: {
      data: pieData.map((item) => item.name),
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      bottom: 0,
      itemGap: 10,
      padding: [0, 0],
      formatter: (name) => {
        const val = pieData.find((item) => item.name === name)?.value || 0;
        return `${name}`;
      },
    },
    grid: {
      top: 20,
      left: 0,
      right: 0,
      containLabel: true,
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: (params) => `${params.name}: ${params.percent.toFixed(2)}%`,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: true,
        },
        data: pieData,
        color: colors,
      },
    ],
  };

  // 添加中间的总计显示
  if (showTotal) {
    baseConfig.title = [
      {
        text: '总计',
        x: 'center',
        top: '36%',
        textStyle: {
          color: '#6C757D',
          fontSize: 14,
        },
      },
      {
        text: '.................',
        x: 'center',
        top: '41%',
        textStyle: {
          color: '#e5e5e5',
          fontSize: 12,
        },
      },
      {
        text: total,
        x: 'center',
        top: '47%',
        textStyle: {
          fontSize: 28,
          color: '#34395E',
          foontWeight: '800',
        },
      },
    ];
  }

  // 深度合并自定义配置
  return deepMerge(baseConfig, customConfig);
}

export default createPieChart;
