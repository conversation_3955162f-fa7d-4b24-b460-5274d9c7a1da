// 辅助函数：深合并对象
export function deepMerge(target, source) {
  if (typeof target !== 'object' || target === null) return source;
  if (typeof source !== 'object' || source === null) return target;

  const merged = Array.isArray(target) ? [...target] : { ...target };

  Object.keys(source).forEach((key) => {
    const sourceValue = source[key];
    const targetValue = merged[key];

    if (Array.isArray(sourceValue) && Array.isArray(targetValue)) {
      // 数组元素合并：逐个元素深度合并
      merged[key] = sourceValue.map((item, index) => {
        return deepMerge(targetValue[index] || {}, item);
      });
    } else if (typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
      // 对象递归合并
      merged[key] = deepMerge(targetValue || {}, sourceValue);
    } else {
      // 基础类型直接覆盖
      merged[key] = sourceValue;
    }
  });

  return merged;
}

export const toThousandsSeparator = (input, num = 0) => {
  if (isNaN(input) || input === null || input === undefined) return '0';
  const str = Number(input).toFixed(num)
    .toString()
    .replace(/,/g, '');
  const parts = str.split('.');
  let integerPart = parts[0];
  const decimalPart = parts.length > 1 ? `.${parts[1]}` : '';
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return integerPart + decimalPart;
};

export const hexToRgba = (hex = '#6777EF', alpha) => {
  hex = hex.replace('#', '');
  if (hex.length === 3) {
    hex = hex
      .split('')
      .map((char) => char + char)
      .join('');
  }
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

/**
 *
 * @param {*} optionsArray 由带有label和value的对象组成的数组
 * @param {*} value 传入的value 支持两种形式：1. 'value' 2. 'value1,value2'
 * @returns value所对应的label
 */
export const findLabelByValue = (optionsArray = [], v, seperator = '-') => {
  // 定义一个辅助函数来查找单个值的标签
  const findSingleLabel = (val) => {
    const res = optionsArray.find((item) => item.value === val);
    return res ? res.label : seperator;
  };

  // 判断 value 是否包含分号
  if (v?.includes(';')) {
    // 按逗号拆分 value
    const valueList = v.split(';');
    // 分别查找每个值对应的标签，并用逗号拼接结果
    return valueList.map(findSingleLabel).join(';');
  }
  // 处理单个值的情况
  return findSingleLabel(v);
};
