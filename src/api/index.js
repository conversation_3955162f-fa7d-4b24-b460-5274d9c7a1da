/**
 * @summary 请求接口
 * @description
 * 请求接口分两种逻辑:
 *  1. 查询接口: 由store的action调用api函数, 并将数据管理在store中, 由页面返显store的state或调用相关的action
 *    store参考 @/store/modules/permission.js
 *    页面参考 @/views/admin/index.vue
 *  2. 操作接口(增删改): 由页面直接调用api函数, 并做出相应的操作提示, 如成功提示, 失败提示, 重复操作提示等
 *    页面参考 @/views/admin/index
 * @example
 * import api from '@/api';
 *
 * api.permission.getAuth().then(auth => {
 *    console.log(auth);
 * });
*/

import { getExportsFromModules } from '@vpscope/dlib3-utils';

const modules = import.meta.glob('./modules/*.js', { eager: true });
export default getExportsFromModules(modules);
