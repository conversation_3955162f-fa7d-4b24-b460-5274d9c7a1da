import { API } from '../api';
import mock from '@/mock';

export class PermissionAPI extends API {
  static AUTH_URL = '/appcenter/api/baseinfo/getauthinfo';
  static TREE_URL = `/${API.APP_CODE}/api/sys-menu/getMenuListTree`;

  getAuth() {
    return this._request({
      url: PermissionAPI.AUTH_URL,
      params: {
        appcode: API.APP_CODE,
      },
    })
      .then(res => ({
        app: res.data.data.canWeb,
        admin: res.data.data.canAdmin,
      }));
  }

  getTree() {
    return this._request({
      url: PermissionAPI.TREE_URL,
    })
      .then(() => mock.PERMISSION_NODES_MOCK);
    // .then(res => res.data.data); // need API
  }

}

export const permission = new PermissionAPI();
