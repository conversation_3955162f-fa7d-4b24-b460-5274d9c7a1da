import { API } from '../api';

export class DatabaseAPI extends API {
  static DATABASE_LIST = `${APP_CODE}/api/database/list`;
  static DATABASE_DETAIL = `${APP_CODE}/api/database/detail`;
  static DOC_TYPE = `${APP_CODE}/api/knowledge/getDocType`; // 文献类型
  static SUBJECT_ESC = `${APP_CODE}/api/knowledge/getSubjectEsc`; // 学科类型
  static UPLOAD = `${APP_CODE}/api/database/upload`;
  static SAVE = `${APP_CODE}/api/database/save`;

  getDatabaseList(data) {
    return this._request({
      url: DatabaseAPI.DATABASE_LIST,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getDatabaseDetail(id) {
    return this._request({
      url: DatabaseAPI.DATABASE_DETAIL + '/' + id,
      method: 'GET',
    }).then((res) => res.data.data);
  }

  getDocType() {
    return this._request({
      url: DatabaseAPI.DOC_TYPE,
      method: 'GET',
    }).then((res) => res.data.data);
  }

  getSubjectEsc() {
    return this._request({
      url: DatabaseAPI.SUBJECT_ESC,
      method: 'GET',
    }).then((res) => res.data.data);
  }

  uploadFile(data) {
    return this._request({
      url: DatabaseAPI.UPLOAD,
      method: 'POST',
      data,
    }).then((res) => res.data);
  }

  saveDatabase(data) {
    return this._request({
      url: DatabaseAPI.SAVE,
      method: 'POST',
      data,
    }).then((res) => res.data);
  }
}

export const databaseApi = new DatabaseAPI();
