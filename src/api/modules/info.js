import { API } from '../api';

export class InfoAPI extends API {
  static BASE_INFO_URL = '/appcenter/api/baseinfo/getbaseinfo';
  static APP_INFO_URL = '/appcenter/api/baseinfo/getcurrentappinfo';

  getBase() {
    return this._request({
      url: InfoAPI.BASE_INFO_URL,
    })
      .then(res => res.data.data);
  }

  getApp() {
    return this._request({
      url: InfoAPI.APP_INFO_URL,
      params: {
        appcode: API.APP_CODE,
      },
    })
      .then(res => res.data.data);
  }

}

export const info = new InfoAPI();
