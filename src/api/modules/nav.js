import { API } from '../api';

export class NavAPI extends API {
  static TOP_NAV_URL = '/appcenter/api/baseinfo/getmgrtopmenu';

  getTop() {
    return this._request({
      url: NavAPI.TOP_NAV_URL,
    })
      .then(res => ({
        list: res.data.data.appMenuList,
        logo: {
          normal: res.data.data.logoUrl,
          thumb: res.data.data.simpleLogoUrl,
        },
      }));
  }

}

export const nav = new NavAPI();
