import { API } from '../api';

export class OrganizationAPI extends API {
  // #region 机构总览
  static ORGANIZATION_INFO = `/${APP_CODE}/api/backend/search-engine/search/j0005`; // 机构基本信息
  static COLLECTION_COUNT = `/${APP_CODE}/api/backend/search-engine/search/j0001`; // 馆藏数量保障概况
  static KEY_COLLECTION = `/${APP_CODE}/api/backend/search-engine/search/j0002`; // 重要收录保障概况
  static ACHIEVEMENT = `/${APP_CODE}/api/backend/search-engine/search/j0003`; // 成果保障概况
  static LITERATURE_BEHAVIOR = `/${APP_CODE}/api/backend/search-engine/search/j0004`; // 文献行为概况
  getOrganizationInfo() {
    return this._request({
      url: OrganizationAPI.ORGANIZATION_INFO,
      method: 'POST',
      data: {},
    }).then((res) => res.data.data);
  }

  getCollectionCount() {
    return this._request({
      url: OrganizationAPI.COLLECTION_COUNT,
      method: 'POST',
      data: {},
    }).then((res) => res.data.data);
  }

  getKeyCollection() {
    return this._request({
      url: OrganizationAPI.KEY_COLLECTION,
      method: 'POST',
      data: {},
    }).then((res) => res.data.data);
  }

  getAchievement() {
    return this._request({
      url: OrganizationAPI.ACHIEVEMENT,
      method: 'POST',
      data: {},
    }).then((res) => res.data.data);
  }

  getLiteratureBehavior() {
    return this._request({
      url: OrganizationAPI.LITERATURE_BEHAVIOR,
      method: 'POST',
      data: {},
    }).then((res) => res.data.data);
  }
  // #endregion

  // #region 馆藏数量评价 - 数据库
  static DATABASE = `/${APP_CODE}/api/backend/search-engine/search/j1001`; // 馆藏数据库数量以及经费
  static PROCUREMENT = `/${APP_CODE}/api/backend/search-engine/search/j1002`; // 馆藏数据库采购状态的分布情况
  static LANGUAGE = `/${APP_CODE}/api/backend/search-engine/search/j1003`; // 馆藏数据库语言的分布情况
  static SUBJECT = `/${APP_CODE}/api/backend/search-engine/search/j1004`; // 馆藏数据库教育部学科分类（门类级）的分布情况
  static LITERATURE = `/${APP_CODE}/api/backend/search-engine/search/j1005`; // 数据库文献类型分布
  static NON_REPETITIVE = `/${APP_CODE}/api/backend/search-engine/search/j1006`; // 数据库不重复文献分析
  static OA = `/${APP_CODE}/api/backend/search-engine/search/j1007`; // 数据库OA文献分析

  getDatabase(data) {
    return this._request({
      url: OrganizationAPI.DATABASE,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getProcurement(data) {
    return this._request({
      url: OrganizationAPI.PROCUREMENT,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getLanguage(data) {
    return this._request({
      url: OrganizationAPI.LANGUAGE,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getSubject(data) {
    return this._request({
      url: OrganizationAPI.SUBJECT,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getLiterature(data) {
    return this._request({
      url: OrganizationAPI.LITERATURE,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getNonRepetitive(data) {
    return this._request({
      url: OrganizationAPI.NON_REPETITIVE,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getOa(data) {
    return this._request({
      url: OrganizationAPI.OA,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }
  // #endregion

  // #region 馆藏数量评价 - 期刊
  static JOURNAL = `/${APP_CODE}/api/backend/search-engine/search/j1009`; // 馆藏期刊数量
  static JOURNAL_LANGUAGE = `/${APP_CODE}/api/backend/search-engine/search/j1010`; // 期刊语言分布
  static JOURNAL_CARRIER = `/${APP_CODE}/api/backend/search-engine/search/j1011`; // 期刊载体分布
  static JOURNAL_Subject = `/${APP_CODE}/api/backend/search-engine/search/j1021`; // 期刊学科分布
  static JOURNAL_OA = `/${APP_CODE}/api/backend/search-engine/search/j1012`; // OA期刊
  static JOURNAL_STATUS = `/${APP_CODE}/api/backend/search-engine/search/j1013`; // 期刊状态分布

  getJournal(data) {
    return this._request({
      url: OrganizationAPI.JOURNAL,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getJournalLanguage(data) {
    return this._request({
      url: OrganizationAPI.JOURNAL_LANGUAGE,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getJournalCarrier(data) {
    return this._request({
      url: OrganizationAPI.JOURNAL_CARRIER,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getJournalSubject(data) {
    return this._request({
      url: OrganizationAPI.JOURNAL_Subject,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getJournalOa(data) {
    return this._request({
      url: OrganizationAPI.JOURNAL_OA,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getJournalStatus(data) {
    return this._request({
      url: OrganizationAPI.JOURNAL_STATUS,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }
  // #endregion

  // #region 馆藏数量评价 - 图书
  static BOOK_COUNT = `/${APP_CODE}/api/backend/search-engine/search/j1014`; // 馆藏图书数量
  static BOOK_LANGUAGE = `/${APP_CODE}/api/backend/search-engine/search/j1015`; // 图书语种分布
  static BOOK_CARRIER = `/${APP_CODE}/api/backend/search-engine/search/j1016`; // 图书载体分布

  getBookCount(data) {
    return this._request({
      url: OrganizationAPI.BOOK_COUNT,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getBookLanguage(data) {
    return this._request({
      url: OrganizationAPI.BOOK_LANGUAGE,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getBookCarrier(data) {
    return this._request({
      url: OrganizationAPI.BOOK_CARRIER,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }
  // #endregion

  // #region 馆藏数量评价 - 期刊文献
  static ARTICLE_COUNT = `/${APP_CODE}/api/backend/search-engine/search/j1017`; // 期刊文献数量
  static ARTICLE_LANGUAGE = `/${APP_CODE}/api/backend/search-engine/search/j1018`; // 期刊文献语言分布
  static ARTICLE_SUBJECT = `/${APP_CODE}/api/backend/search-engine/search/j1019`; // 期刊文献学科分布
  static ARTICLE_OA = `/${APP_CODE}/api/backend/search-engine/search/j1020`; // OA期刊文献

  getArticleCount(data) {
    return this._request({
      url: OrganizationAPI.ARTICLE_COUNT,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getArticleLanguage(data) {
    return this._request({
      url: OrganizationAPI.ARTICLE_LANGUAGE,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getArticleSubject(data) {
    return this._request({
      url: OrganizationAPI.ARTICLE_SUBJECT,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getArticleOa(data) {
    return this._request({
      url: OrganizationAPI.ARTICLE_OA,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }
  // #endregion

  // #region 重要收录保障评价
  static IMPORTANT_JOURNAL = `/${APP_CODE}/api/backend/search-engine/search/j2001`; // 重要收录期刊保障情况
  static IMPORTANT_PARTITION = `/${APP_CODE}/api/backend/search-engine/search/j2002`; // 重要收录期刊分区保障分析
  static IMPORTANT_DATABASE_TOP10 = `/${APP_CODE}/api/backend/search-engine/search/j2003`; // 未保障重要收录期刊数据库收录TOP10
  static IMPORTANT_ARTICLE = `/${APP_CODE}/api/backend/search-engine/search/j2004`; // 重要收录期刊文献保障情况

  getImportantJournal(data) {
    return this._request({
      url: OrganizationAPI.IMPORTANT_JOURNAL,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getImportantPartition(data) {
    return this._request({
      url: OrganizationAPI.IMPORTANT_PARTITION,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getImportantDatabaseTop10(data) {
    return this._request({
      url: OrganizationAPI.IMPORTANT_DATABASE_TOP10,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getImportantArticle(data) {
    return this._request({
      url: OrganizationAPI.IMPORTANT_ARTICLE,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }
  // #endregion

  // #region 成果数据保障评价 - 学术成果
  static ACHIEVEMENT_DATA = `/${APP_CODE}/api/backend/search-engine/search/j3001`; // 学术成果
  static ACHIEVEMENT_COLLECTION_DATABASE_TOP10 = `/${APP_CODE}/api/backend/search-engine/search/j3002`; // 学术成果收录TOP10（数据库）
  static ACHIEVEMENT_JOURNAL = `/${APP_CODE}/api/backend/search-engine/search/j3003`; // 发文期刊
  static ACHIEVEMENT_JOURNAL_DATABASE_TOP10 = `/${APP_CODE}/api/backend/search-engine/search/j3004`; // 发文期刊收录TOP10（数据库）
  static ACHIEVEMENT_JOURNAL_TOP20 = `/${APP_CODE}/api/backend/search-engine/search/j3005`; // 发文期刊TOP20

  getAchievementData(data) {
    return this._request({
      url: OrganizationAPI.ACHIEVEMENT_DATA,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getAchievementCollectionDatabaseTop10(data) {
    return this._request({
      url: OrganizationAPI.ACHIEVEMENT_COLLECTION_DATABASE_TOP10,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getAchievementJournal(data) {
    return this._request({
      url: OrganizationAPI.ACHIEVEMENT_JOURNAL,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getAchievementJournalDatabaseTop10(data) {
    return this._request({
      url: OrganizationAPI.ACHIEVEMENT_JOURNAL_DATABASE_TOP10,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getAchievementJournalTop20(data) {
    return this._request({
      url: OrganizationAPI.ACHIEVEMENT_JOURNAL_TOP20,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }
  // #endregion

  // #region 成果数据保障评价 - 参考文献
  static REFERENCE_DATA = `/${APP_CODE}/api/backend/search-engine/search/j3006`; // 参考文献
  static REFERENCE_TOP20 = `/${APP_CODE}/api/backend/search-engine/search/j3007`; // 参考文献TOP20
  static REFERENCE_TOP10 = `/${APP_CODE}/api/backend/search-engine/search/j3008`; // 参考文献期刊收录TOP10（数据库）
  static REFERENCE_JOURNAL = `/${APP_CODE}/api/backend/search-engine/search/j3009`; // 参考期刊
  static REFERENCE_JOURNAL_TOP10 = `/${APP_CODE}/api/backend/search-engine/search/j3010`; // 参考期刊收录TOP10（数据库）
  static REFERENCE_JOURNAL_TOP20 = `/${APP_CODE}/api/backend/search-engine/search/j3011`; // 参考期刊TOP20

  getReferenceData(data) {
    return this._request({
      url: OrganizationAPI.REFERENCE_DATA,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getReferenceTop20(data) {
    return this._request({
      url: OrganizationAPI.REFERENCE_TOP20,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getReferenceTop10(data) {
    return this._request({
      url: OrganizationAPI.REFERENCE_TOP10,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getReferenceJournal(data) {
    return this._request({
      url: OrganizationAPI.REFERENCE_JOURNAL,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getReferenceJournalTop10(data) {
    return this._request({
      url: OrganizationAPI.REFERENCE_JOURNAL_TOP10,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }

  getReferenceJournalTop20(data) {
    return this._request({
      url: OrganizationAPI.REFERENCE_JOURNAL_TOP20,
      method: 'POST',
      data,
    }).then((res) => res.data.data);
  }
  // #endregion
}

export const organizationApi = new OrganizationAPI();
