import Vue from 'vue';
import App from '@/views/app/index.vue';
import ElementUI from 'element-ui';
import VueRouter from 'vue-router';
import route, { router } from '@/router';
import { createP<PERSON>, PiniaVuePlugin } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

import appFrameRenderer from '@vpscope/dlib3-app-frame-render';
import { DPermission } from '@/utils/permission.js';
import DUi from '@vpscope/dlib3-ui';
import { storage, getApplicationConfig, attempt, afterTokenReady, Token, request, logout } from '@vpscope/dlib3-utils';
import 'virtual:windi.css';
import '@/assets/styles/base.scss';
import '@/assets/styles/element-variables.scss';

storage.local.set('env:app-cli', 1); // 用于banner组件判断是否是cli模式

const render = async () => {
  Vue.use(DUi);
  Vue.use(PiniaVuePlugin);
  Vue.use(VueRouter);
  Vue.use(ElementUI);
  Vue.use(DPermission);

  Vue.prototype.APP_CODE = APP_CODE;

  const pinia = createPinia();
  pinia.use(piniaPluginPersistedstate);

  new Vue({
    pinia,
    router,
    render: (h) => h(App),
  }).$mount('#root');

  router.addRoutes(route.app);
  // const pathname = await storage.get('pathname');
  // router.push(pathname || route.app[0].path);
};

(async () => {
  await afterTokenReady();
  const token = await Token.getInstance().getValue();
  if (token) storage.local.set('token', token);
  const appConfig = await getApplicationConfig();
  window.axios = request;
  window.logoutCallback = logout;
  window.accessToken = await Token.getInstance().getBasicValue();
  window.orgcode = appConfig.ORG_CODE;
  window.Vue = Vue;

  appFrameRenderer.renderHeaderFooter(
    {
      containerHeaderId: 'vp-header',
      containerFooterId: 'vp-footer',
      components: 'pagination;breadcrumb;banner',
      baseInfo: {
        apiDomainAndPort: appConfig.API_BASE_URL,
        casBaseUrl: appConfig.CAS_BASE_URL,
        orgTokenLink: appConfig.BASIC_TOKEN_URL,
        orgCode: appConfig.ORG_CODE,
      },
      router,
      headerFooterConfig: [],
    },
    async (callbacks) => {
      try {
        await Promise.all(callbacks?.map((cb) => cb(Vue)));
      } catch (err) {
        console.error(err);
      } finally {
        render();
      }
    },
  );
})();
