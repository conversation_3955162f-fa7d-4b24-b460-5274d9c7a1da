import Vue from 'vue';
import App from '@/views/admin/index.vue';
import ElementUI from 'element-ui';
import VueRouter from 'vue-router';
import { createPinia, PiniaVuePlugin } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import { router } from '@/router';
import { DPermission } from '@/utils/permission.js';
import _ from 'lodash';
import { afterTokenReady } from '@vpscope/dlib3-utils';
import DUi from '@vpscope/dlib3-ui';
import DDatavis from '@vpscope/dlib3-datavis';
import 'virtual:windi.css';
import '@/assets/styles/base.scss';
import '@/assets/styles/element-variables.scss';

(async () => {
  await afterTokenReady();
  Vue.use(DUi);
  Vue.use(DDatavis);
  Vue.use(ElementUI);
  Vue.use(PiniaVuePlugin);
  Vue.use(VueRouter);
  Vue.use(DPermission);

  Vue.prototype.APP_CODE = APP_CODE;
  const pinia = createPinia();
  pinia.use(piniaPluginPersistedstate);

  new Vue({
    pinia: pinia,
    router,
    render: h => h(App),
  }).$mount('#root');

})();


