<script setup name="d-breadcrumb">
import store from '@/store';
import hook from '@/hooks';

const navStore = store.useNav();
const route = hook.useRoute();
const infoStore = store.useInfo();

const homeRoute = '/' + APP_CODE + '/admin-home';
const currentBreadcrumb = computed(() => {
  return navStore.routes.flatBy('children', Infinity).find((r) => r.path === route.value.path)?.meta?.breadcrumb ?? [];
});
</script>

<template>
  <div class="flex justify-between items-center">
    <nav class="text-white text-sm select-none mb-4">
      <router-link :to="homeRoute">
        <i class="el-icon-s-home mr-1" />
      </router-link>
      <i class="el-icon-arrow-right" />
      <router-link :to="homeRoute">{{ infoStore.app.appName }}</router-link>
      <template v-for="b in currentBreadcrumb">
        <span :key="b.label">
          <i class="el-icon-arrow-right" />
          <router-link
            v-if="b.path"
            :to="b.path"
          >
            {{ b.label }}
          </router-link>
          <span v-else>{{ b.label }}</span>
        </span>
      </template>
    </nav>
    <div class="flex justify-between items-center search-wrapper -mt-25px">
      <el-select
        v-model="model"
        placeholder="请选择"
        size="small"
      >
        <el-option
          v-for="item in [
            { label: '图书', value: '1' },
            { label: '期刊', value: '2' },
            { label: '数据库', value: '3' },
          ]"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <span class="text-[#A4AAB7]">|</span>
      <el-input
        v-model="search"
        placeholder="刊名、ISSN"
        size="small"
      ></el-input>
      <d-button
        icon="el-icon-vip-sousuo"
        type="primary"
        size="small"
      >
        查找
      </d-button>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.search-wrapper {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 3px;
  overflow: hidden;
  :deep(.el-input__inner) {
    border: none;
    background: transparent;
  }
  :deep(.el-button--small) {
    border-radius: 0 3px 3px 0;
  }
}
</style>
