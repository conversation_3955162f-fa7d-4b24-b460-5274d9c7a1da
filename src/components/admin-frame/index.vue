<script setup name="admin-frame">
import DHeader from './header.vue';
import DFooter from './footer.vue';
import DSider from './sider.vue';
import DBreadcrumb from './breadcrumb.vue';
import store from '@/store';

const isCollapse = ref(false);

const clearStoreCache = () => {

};
</script>

<template>
  <section class="w-full h-screen flex flex-col">
    <d-header :is-collapse="isCollapse" />
    <section class="flex flex-1">
      <!-- <aside class="overflow-y-auto h-[calc(100vh-40px)]"> -->
      <d-sider
        :is-collapse.sync="isCollapse"
        @clear:storecache="clearStoreCache"
      />
      <!-- </aside> -->
      <main
        id="main-container"
        class="flex-1 p-6 overflow-auto h-[calc(100vh-40px)]"
      >
        <d-breadcrumb />
        <slot />
        <d-footer />
      </main>
    </section>
  </section>
</template>

<style scoped>
main {
  background: #f4f7fc url('./assets/main-bg.png') no-repeat 100% 0 / 100% auto;
}
</style>
