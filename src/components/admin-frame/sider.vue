<script setup name="d-sider">
import store from '@/store';
import hook from '@/hooks';

const props = defineProps({
  isCollapse: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:isCollapse', 'clear:storecache']);

const route = hook.useRoute();
const router = hook.useRouter();
const navStore = store.useNav();
const infoStore = store.useInfo();
const expandedRouteNames = ref([]);

const scale = computed(() => {
  const titleLen = infoStore.app.appBackName?.length || 1;
  if (props.isCollapse && titleLen > 4) return 4 / titleLen;
  return 1;
});

const tempExpandedRouteNames = ref([]);

const onCollapse = () => {
  emit('update:isCollapse', !props.isCollapse);
  if (!props.isCollapse) {
    tempExpandedRouteNames.value = expandedRouteNames.value;
    expandedRouteNames.value = [];
  } else {
    expandedRouteNames.value = tempExpandedRouteNames.value;
  }
};

const onExpand = (name) => {
  if (expandedRouteNames.value.includes(name)) {
    expandedRouteNames.value = expandedRouteNames.value.filter((n) => n !== name);
  } else {
    expandedRouteNames.value.push(name);
  }
};

watch(
  () => navStore.routes,
  () => {
    expandedRouteNames.value = navStore.routes.filter((r) => r.children?.length).map((r) => r.name);
  },
  { immediate: true },
);

const handleClickNav = (path, isExternalRoute) => {
  if (isExternalRoute) {
    return window.open(path, '_blank');
  }
  if (path === route.value.path) {
    return;
  }
  // eslint-disable-next-line vue/custom-event-name-casing
  emit('clear:storecache');
  router.value.push(path); // done
};

const goToVersionDetail = () => {
  if (infoStore.app.logUrl) {
    location.href = infoStore.app.logUrl;
  }
};
</script>

<template>
  <aside
    class="select-none w-[240px] transition-all h-[calc(100vh-40px)] flex flex-col hide-scrollbar"
    :class="{ 'w-[90px]': props.isCollapse }"
  >
    <!-- 标题 -->
    <div
      class="w-50 h-20 box-border bg-[#f4f7fc] rounded-lg mt-4.5 ml-5 relative flex flex-col justify-center transition-all flex-shrink-0"
      :class="{ 'w-22.5 h-22.5 rounded-none border-b border-b-[#f4f7fc] !m-0 bg-transparent': props.isCollapse }"
    >
      <div
        class="text-[#3e54ee] border-l-[#3e54ee] border-l-4 h-10 absolute top-5 left-7.5 pl-4 flex flex-col justify-between"
        :class="{ 'h-9 border-none !pl-0 top-1/2 !left-1/2 transform gap-1 origin-top mt-2': props.isCollapse }"
        :style="{
          transform: `scale(${scale}) ${props.isCollapse ? 'translate(-50%, -50%)' : ''}`,
        }"
      >
        <div
          class="text-2xl leading-[1em] font-bold truncate"
          :class="{ 'text-[18px] w-90px px-10px': props.isCollapse }"
        >
          {{ infoStore.app.appBackName }}
        </div>
        <div
          class="text-xs leading-[1em] cursor-pointer"
          :class="{ 'px-10px': props.isCollapse }"
          @click="goToVersionDetail"
        >
          {{ infoStore.app.appVersion }}
        </div>
      </div>
      <d-icon
        class="!absolute bottom-5 right-2.5 cursor-pointer text-xs text-[#6c757d]"
        :class="{ 'bottom-6.5': props.isCollapse }"
        :name="!props.isCollapse ? 'el-icon-vip-shouqi1' : 'el-icon-vip-zhankai1'"
        @click.native="onCollapse"
      />
    </div>

    <!-- 导航 -->
    <nav class="mt-4.5 flex-1 hide-scrollbar font-bold z-99 overflow-y-auto w-[260px] pointer-events-none">
      <template v-for="navRoute in navStore.routes.filter((r) => !r.hidden)">
        <!-- 不带可视children的菜单 -->
        <a
          v-if="!navRoute.children?.length || (navRoute.children.length && navRoute.children.every((r) => r.hidden))"
          class="route-wrapper pointer-events-auto cursor-pointer block"
          :key="navRoute.path"
          :class="{ '!w-[90px] px-2': props.isCollapse }"
          :href="navRoute.path"
          @click.prevent="handleClickNav(navRoute.path, navRoute?.isExternalRoute)"
        >
          <div
            class="route"
            :class="{
              '!px-0 justify-center text-xs': props.isCollapse,
              active: route.path === navRoute.path || route.meta.parentPath === navRoute.path,
            }"
            :to="navRoute.path"
          >
            <d-icon
              v-show="!props.isCollapse"
              class="mr-4 text-[13px]"
              name="el-icon-vip-daohanglanmu"
            />
            {{ navRoute.name }}
            <div
              v-show="!props.isCollapse"
              class="signal"
            />
          </div>
        </a>

        <!-- 带可视children的菜单 -->
        <div
          v-else
          :key="navRoute.name"
          class="route-wrapper !h-auto relative pointer-events-auto cursor-pointer"
          :class="{
            '!w-[90px] px-2': props.isCollapse,
            active: navRoute.children.some(
              (child) => route.path === child.path || route.meta?.parentPath === child.path
            ),
          }"
        >
          <a
            class="route !h-10 !shadow-none truncate w-full inline-block"
            :class="{
              '!px-0 justify-center text-xs': props.isCollapse,
              active: navRoute.children.some(
                (child) => route.path === child.path || route.meta?.parentPath === child.path
              ),
            }"
            @click.prevent="onExpand(navRoute.name)"
          >
            <d-icon
              v-show="!props.isCollapse"
              class="mr-4 text-[13px]"
              name="el-icon-vip-daohanglanmu"
            />
            {{ navRoute.name }}
            <d-icon
              v-show="!props.isCollapse"
              class="mr-4 text-[10px] absolute -right-12 transition-all transform rotate-270"
              name="el-icon-vip-xialaxuanzekuangjiantou"
              :class="{
                'rotate-360': expandedRouteNames.includes(navRoute.name),
              }"
            />
          </a>
          <!-- 非折叠状态的子菜单 -->
          <d-collapse-transition
            v-if="!isCollapse"
            :show="expandedRouteNames.includes(navRoute.name)"
          >
            <div class="pb-2">
              <!-- 菜单点击的跳转 -->
              <a
                v-for="child in navRoute.children.filter((r) => !r.hidden)"
                class="route-wrapper block"
                :key="child.path"
                :href="child.path"
                @click.prevent="handleClickNav(child.path, child?.isExternalRoute)"
              >
                <div
                  class="route px-0 relative !shadow-none"
                  :class="{
                    '!px-0 justify-center text-xs': props.isCollapse,
                    active: route.path === child.path || route.meta?.parentPath === child.path,
                  }"
                  :to="child.path"
                >
                  <div
                    class="child-node"
                    :class="{
                      '!pl-11 mr-2': !props.isCollapse,
                    }"
                  >
                    {{ child.name }}
                  </div>
                  <div
                    v-show="!props.isCollapse"
                    class="signal !h-full"
                  />
                </div>
                <div
                  class="mask"
                  :class="{
                    'opacity-100': route.path === child.path || route.meta?.parentPath === child.path,
                  }"
                />
              </a>
            </div>
          </d-collapse-transition>
          <!-- 折叠状态的子菜单 -->
          <div
            class="sub-router-wrap pb-2 absolute top-0 left-[90px] z-[9999] pointer-events-auto"
            v-else
          >
            <div class="bg-white shadow-sm m-2 p-2 rounded">
              <a
                v-for="child in navRoute.children.filter((r) => !r.hidden)"
                class="route-wrapper rounded my-2 hover:bg-[#e3ebf7] block"
                :class="{
                  '!w-[140px]': props.isCollapse,
                  ' bg-[#e3ebf7]': route.path === child.path || route.meta?.parentPath === child.path,
                }"
                :key="child.path"
                :href="child.path"
                @click.prevent="handleClickNav(child.path, child?.isExternalRoute)"
              >
                <div
                  class="route px-0 relative !shadow-none"
                  :class="{
                    '!px-0 justify-center text-xs': props.isCollapse,
                    active: route.path === child.path || route.meta?.parentPath === child.path,
                  }"
                  :to="child.path"
                >
                  <div
                    :class="{
                      '!pl-11 mr-2': !props.isCollapse,
                    }"
                  >
                    {{ child.name }}
                  </div>
                  <div
                    v-show="!props.isCollapse"
                    class="signal !h-full"
                  />
                </div>
              </a>
            </div>
          </div>
        </div>
      </template>
    </nav>
  </aside>
</template>
<style lang="scss" scoped>
.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}
.route-wrapper {
  @apply 'w-[245px] h-10 text-[13px] transition-all relative';
  .sub-router-wrap {
    transform: scale(0);
    opacity: 0;
    transition: all 0.3s;
    transform-origin: top left;
  }
  &:hover .sub-router-wrap {
    // display: block;
    opacity: 1;
    transform: scale(1);
  }
  .mask {
    @apply 'absolute left-4 top-0 right-4 h-full rounded bg-[#e3ebf7] pointer-events-none opacity-0 transition-all';
  }

  &.active {
    @apply 'bg-white bg-opacity-60 !text-primary children:text-primary';
    box-shadow: 1px 2px 5px rgba(0, 0, 0, 0.05);
    background: rgba(255, 255, 255, 0.6);
  }

  .route {
    @apply 'block w-full h-full flex items-center text-[#6c757d] relative px-12.5 z-99 rounded-sm truncate hover:(bg-white bg-opacity-60 text-primary children:text-primary) transition-all';

    .signal {
      @apply 'h-[15px] w-0.5 bg-primary absolute right-[5px] opacity-0';
    }

    &:hover,
    &.active {
      @apply 'bg-white bg-opacity-60 !text-primary children:text-primary';
      box-shadow: 1px 2px 5px rgba(0, 0, 0, 0.05);
      background: rgba(255, 255, 255, 0.6);

      .signal {
        opacity: 1;
      }

      & + .mask {
        @apply opacity-100;
      }

      .child-node {
        @apply 'w-full h-full flex items-center';
      }
    }
  }
}
</style>
