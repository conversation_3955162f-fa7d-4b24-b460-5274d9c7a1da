<script setup name="d-header">
import store from '@/store';
import hook from '@/hooks';
import { logout, storage } from '@vpscope/dlib3-utils';

const props = defineProps({
  isCollapse: {
    type: Boolean,
    default: false,
  },
});

/** 头像处理 */
const handleImgURL = (imgURL = '') => {
  return !imgURL.startsWith('http') ? storage.local.get('fileUrl') + imgURL : imgURL;
};

const imgError = ref(false);
const onImageError = (event) => {
  imgError.value = true;
};

const isImageExist = async (url) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};

const infoStore = store.useInfo();
const navStore = store.useNav();
const getFilePath = hook.useGetFilePath();
const [, hasImage] = hook.useAsyncComputed(() => isImageExist(handleImgURL(infoStore.base.userInfo?.photo)));

const navWidth = computed(() => {
  if (!navStore.top?.list) return 0;
  const padding = navStore.top.list.length * 40;
  const text = navStore.top.list.map((item) => item.appName).join('').length * 14;
  return padding + text;
});

const getBaseUrl = () => {
  return storage.local.get('urlInfo')?.find((item) => item.code === 'index')?.path;
};

navStore.updateTop();
</script>

<template>
  <header class="h-10 shadow flex items-center justify-between">
    <div
      class="w-[240px] h-full flex items-center justify-center px-7 py-2 transition-all"
      :class="{ 'w-[90px] px-2': props.isCollapse }"
    >
      <img
        class="w-full h-full object-contain"
        :src="getFilePath(navStore.top.logo?.[!props.isCollapse ? 'normal' : 'thumb'])"
      />
    </div>
    <section class="flex-1 h-full flex justify-between overflow-x-hidden">
      <nav class="flex-1 overflow-x-auto">
        <div :style="{ width: `${navWidth}px` }" class="leading-40px">
          <a
            class="h-full inline-block px-5 text-sm leading-10 text-[#6c757d] break-words border-box"
            v-for="item in navStore.top.list"
            :id="item.appId"
            :key="item.appId"
            :href="item.backendUrl"
          >
            {{ item.appName }}
          </a>
        </div>
      </nav>
      <div class="flex items-center float-right pr-6 text-[#6c757d] text-sm gap-2">
        <a
          :href="`${getBaseUrl()}/index?page=1`"
          class="px-2"
          target="_blank"
        >
          回到首页
        </a>
        <a
          :href="`${getBaseUrl()}/usermanage/web_library`"
          class="px-2"
          target="_blank"
        >
          个人图书馆
        </a>
        <div
          class="w-7 h-7 inline-block p-[3px] flex items-center"
          :class="{
            'bg-[#cc2c8c0]': hasImage,
          }"
        >
          <img
            v-if="!imgError"
            class="rounded-full"
            :src="handleImgURL(infoStore.base.userInfo?.photo)"
            @error="onImageError"
          />
          <d-icon
            v-else
            name="el-icon-vip-a-Group1143"
            class="text-[28px] text-gray-300"
          />
        </div>
        <span class="mx-2">{{ infoStore.base.userInfo?.name }}</span>
        <d-icon
          name="el-icon-vip-tuichu"
          class="hover:text-dark-900 cursor-pointer"
          @click.native="logout"
        />
      </div>
    </section>
  </header>
</template>

<style lang="scss" scoped>
.shadow {
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.08);
}
</style>
