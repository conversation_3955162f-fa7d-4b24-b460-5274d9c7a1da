<script setup>
import { getCurrentInstance } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    required: true,
  },
  showFooter: {
    type: Boolean,
    default: true,
  },
  confirmText: {
    type: String,
    default: '添加',
  },
  confirmIcon: {
    type: String,
    default: 'el-icon-vip-gou2',
  },
});

const emit = defineEmits(['update:visible', 'cancel', 'confirm', 'before-close']);

const handleChangeVisible = (v) => {
  emit('update:visible', v);
};

const handleCancel = () => {
  emit('cancel');
};
const { proxy } = getCurrentInstance();

const handleConfirm = () => {
  emit('confirm');
};
const handleBeforeClose = () => {
  // eslint-disable-next-line vue/custom-event-name-casing
  emit('before-close');
};
</script>
<template>
  <d-drawer
    :visible="props.visible"
    modal
    @update:visible="($event) => handleChangeVisible($event)"
    :before-close="handleBeforeClose"
  >
    <div class="drawer-content h-full">
      <div class="h-[40px] leading-[40px] text-sm pl-5 text-[white] border-b border-[#EBEEF5] bg-transparent">
        {{ props.title }}
      </div>
      <div class="p-5 h-[calc(100%-40px)]">
        <div class="bg-white rounded-[3px] p-[10px] h-full overflow-auto">
          <slot />
        </div>
      </div>
    </div>
    <template
      #footer
      v-if="props.showFooter"
    >
      <div class="bg-white w-full text-center text-white font-bold flex">
        <div
          class="bg-[#F1F1F1] flex-1 h-[50px] flex items-center justify-center text-sm text-[#6D6D6D] cursor-pointer"
          @click="handleCancel"
        >
          <d-icon name="el-icon-vip-quxiao">取消</d-icon>
        </div>
        <div
          class="bg-[#5871E6] flex-1 h-[50px] flex items-center justify-center text-sm cursor-pointer"
          @click="handleConfirm"
        >
          <d-icon :name="props.confirmIcon">{{ props.confirmText }}</d-icon>
        </div>
      </div>
    </template>
  </d-drawer>
</template>

<style lang="scss" scoped>
::v-deep(.el-drawer) {
  width: 900px !important;
}
.drawer-content {
  background: linear-gradient(180deg, #6777ef 0%, rgba(255, 255, 255, 0) 150px);
}
</style>
