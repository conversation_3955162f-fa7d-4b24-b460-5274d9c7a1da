<template>
  <div>
    <d-card
      content-class="text-blue-500 pt-0"
      title-class="justify-between"
    >
      <template #title>
        <div>标题</div>
        <d-button @click="showDrawer">按钮</d-button>
      </template>
      <div>内容aaaa</div>
      <div>内容bbbb</div>
    </d-card>

    <drawer-base
      title="测试标题"
      :visible="isVisible"
      @before-close="handleBeforeClose"
      @cancel="handleCancel"
    >
      <div
        v-for="item in 200"
        :key="item"
      >
        {{ item }}
      </div>
    </drawer-base>
  </div>
</template>

<script setup>
import DrawerBase from '@/components/drawer-base';

const isVisible = ref(false);
const showDrawer = () => {
  console.log('showDrawer');
  isVisible.value = true;
};

const handleBeforeClose = () => {
  console.log('handleBeforeClose');
};

const handleCancel = () => {
  console.log('cancel');
};
</script>

<style lang="scss" scoped></style>
