<template>
  <div class="menu">
    <div
      v-for="(item, index) in menuList"
      :key="index"
      class="menu-item"
    >
      <!-- 一级菜单 -->
      <div
        class="menu-title"
        :class="{
          clickable: item.children && item.children.length > 0,
          active: (!item.children || item.children.length === 0) && selectedMainIndex === index,
        }"
        @click="handleMainClick(index, item)"
      >
        {{ item.title }}
        <span
          class="arrow bg-white rounded-7px flex items-center justify-center"
          v-if="item.children && item.children.length > 0"
          :class="{ rotated: openIndex === index }"
        >
          <i class="el-icon-arrow-up text-[#6777EF]"></i>
        </span>
      </div>

      <!-- 子菜单 -->
      <transition name="slide">
        <div
          v-show="openIndex === index"
          class="submenu"
        >
          <div
            v-for="(sub, subIndex) in item.children"
            :key="`${index}-${subIndex}`"
            class="submenu-item"
            :class="{ active: selectedSubKey === `${index}-${subIndex}` }"
            @click="selectedSubKey = `${index}-${subIndex}`"
          >
            {{ sub }}
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const selectedMainIndex = ref(null);
const selectedSubKey = ref(null);
const openIndex = ref(null);

const menuList = [
  {
    title: '05/ 馆藏数量评价',
    children: [
      '美视电影学院美视电影学院美视电影学院',
      '馆藏数据库重复文献分析',
      '馆藏数据库重复文献分析',
      '馆藏数据库重复文献分析',
    ],
  },
  {
    title: '05/ 馆藏数量评价',
    children: ['馆藏数据库重复文献分析'],
  },
  {
    title: '06/ 馆藏数量评价',
    children: [],
  },
];

const toggle = (index) => {
  openIndex.value = openIndex.value === index ? null : index;
};

const handleMainClick = (index, item) => {
  if (item.children && item.children.length > 0) {
    openIndex.value = openIndex.value === index ? null : index;
  } else {
    selectedMainIndex.value = index;
    selectedSubKey.value = null; // 清除子菜单选中状态
  }
};
</script>

<style scoped>
.menu-item:not(:last-child) {
  margin-bottom: 10px;
  background: #f1f1f1;
  border-radius: 5px;
}
.menu-title {
  height: 36px;
  padding: 0 15px;
  border-radius: 4px;
  background: #d9e1ff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #404040;
}
.menu-title.clickable {
  cursor: pointer;
}

.arrow {
  width: 14px;
  height: 14px;
  transition: transform 0.3s ease;
}

.arrow.rotated {
  transform: rotate(180deg);
}

/* slide 动画 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}
.slide-enter-from,
.slide-leave-to {
  max-height: 0;
  opacity: 0;
}
.slide-enter-to,
.slide-leave-from {
  max-height: 500px; /* 可根据内容实际最大高度调整 */
  opacity: 1;
}

.submenu {
  background: #fff;
}
.submenu-item {
  padding: 9px 30px;
  font-size: 14px;
  border-radius: 4px;
  background: rgba(225, 230, 255, 0.3);
  cursor: pointer;
}
.submenu-item:first-child {
  margin-top: 2px;
}
.submenu-item:not(:last-child) {
  margin-bottom: 2px;
}
.menu-title.active,
.submenu-item.active {
  color: #6777ef;
  outline: 1px solid rgba(103, 119, 239, 0.75);
}
</style>
