<template>
  <h1 v-if="level === 1">{{ content }}</h1>
  <h2 v-else-if="level === 2">{{ content }}</h2>
  <h3 v-else-if="level === 3">{{ content }}</h3>
  <h4 v-else-if="level === 4">{{ content }}</h4>
  <h5 v-else-if="level === 5">{{ content }}</h5>
  <h6 v-else-if="level === 6">{{ content }}</h6>
</template>

<script setup>
const props = defineProps({
  content: {
    type: String,
    required: true,
  },
  level: {
    type: Number,
    required: true,
  },
});
</script>

<style lang="scss" scoped></style>
