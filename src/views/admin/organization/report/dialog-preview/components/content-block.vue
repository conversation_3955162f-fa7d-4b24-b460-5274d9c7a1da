<template>
  <div class="content-block">
    <div v-loading="loading">
      <component
        :is="componentType"
        :content="renderedContent"
        :level="block.level"
        :table-headers="block.tableHeaders"
      />
    </div>
  </div>
</template>

<script setup>
import TocComponent from './toc.vue';
import CoverComponent from './cover.vue';
import HeadingComponent from './heading.vue';
import ParagraphComponent from './paragraph.vue';
import TableComponent from './table.vue';

const props = defineProps({
  block: {
    type: Object,
    required: true,
  },
});

const renderedContent = ref(props.block.content);
const loading = ref(true);
const componentType = computed(() => {
  const typeMap = {
    toc: TocComponent,
    cover: CoverComponent,
    heading: HeadingComponent,
    paragraph: ParagraphComponent,
    table: TableComponent,
  };
  return typeMap[props.block.type] || 'div';
});

const replaceVariables = (content, data) => {
  return content.replace(/\{\{\s*(\w+)\s*\}\}/g, (match, key) => {
    return data[key] !== undefined ? String(data[key]) : match;
  });
};

const getComponentData = async () => {
  try {
    const config = {
      method: props.block.api.method,
      url: props.block.api.url,
    };

    if (props.block.api.method === 'GET') {
      config.params = props.block.api.params;
    } else {
      config.data = props.block.api.body;
    }

    // TODO: 调用接口
    loading.value = true;
    setTimeout(() => {
      renderedContent.value = replaceVariables(props.block.content, {
        data1: '张三',
        data2: 18,
      });
      loading.value = false;
    }, 2000);
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

(async () => {
  if (props.block.api) {
    await getComponentData();
  } else {
    loading.value = false;
  }
})();
</script>

<style lang="scss" scoped></style>
