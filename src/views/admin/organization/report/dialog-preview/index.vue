<template>
  <d-dialog
    title="报告预览"
    type="ok"
    :show-footer="false"
    width="1200px"
    :visible="props.visible"
    @update:visible="emit('update:visible', $event)"
  >
    <div class="flex justify-between gap-5">
      <div class="toc w-220px">
        <collapse-menu></collapse-menu>
      </div>
      <div class="content flex-1 h-840px bg-[#F1F3F7] relative flex justify-center items-center">
        <div class="w-550px h-780px overflow-y-auto">
          <content-block
            v-for="(block, index) in blocks"
            :key="index"
            :block="block"
          />
        </div>
        <div class="arrow-previous bg-[#3C4B5D]">
          <d-icon name="el-icon-vip-a-zuojiantou2"></d-icon>
        </div>
        <div class="arrow-next bg-[#3C4B5D]">
          <d-icon name="el-icon-vip-youjiantou"></d-icon>
        </div>
      </div>
    </div>
  </d-dialog>
</template>

<script setup>
import CollapseMenu from './components/collapse-menu.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

import ContentBlock from './components/content-block.vue';

const blocks = ref([
  {
    type: 'paragraph',
    content: '截止2024年12月31日，刊表内含{{data1}}万种期刊，{{data2}}万篇期刊文献',
    api: {
      url: '/api/chapter1',
      method: 'GET',
    },
  },
  {
    type: 'heading',
    level: 2,
    content: '期刊分类',
    api: null,
  },
]);
</script>

<style lang="scss" scoped>
[class^='arrow-'] {
  color: white;
  width: 40px;
  height: 40px;
  display: inline-block;
  border-radius: 50%;
  cursor: pointer;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrow-previous {
  left: 20px;
}
.arrow-next {
  right: 20px;
}
</style>
