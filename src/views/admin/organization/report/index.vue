<template>
  <div class="overflow-hidden flex justify-center">
    <!-- TODO:后期需要加上float-left -->
    <div class="w-462px h-804px mr-5 rounded-3px bg-[rgba(103,119,239,0.05)] flex flex-col items-center">
      <div class="w-400px h-568px bg-white mt-45px">
        <div class="img w-400px h-408px pt-25px">
          <div class="ml-30px flex">
            <img
              class="w-26px h-26px"
              src="./imgs/logo.png"
              alt=""
            />
            <div class="text-white flex flex-col justify-center ml-8px">
              <p class="text-xs">xxx大学图书馆</p>
              <p class="text-[4px]">UNIVERSITY LIBRARY</p>
            </div>
          </div>
        </div>
        <div class="describe text-[#10308D] font-bold text-base px-30px">
          <div class="mt-5">xxx大学图书馆</div>
          <!-- <div>2024年度</div> -->
          <div>馆藏资源绩效分析报告（机构馆藏）</div>

          <!-- <div class="text-[#000000] text-[10px] mt-26px font-normal mb-4px">xx大学图书馆情报服务团队</div> -->
          <div class="mt-26px flex justify-between text-[#000000] text-[10px] font-normal">
            <div>2020-10</div>
            <!-- <div class="text-[#666666] text-[8px]">报告编号：CQU123155234</div> -->
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-30px">
        <div
          class="flex flex-col items-center cursor-pointer"
          @click="handlePreviewReport"
        >
          <img
            class="w-30px h-25px"
            src="./imgs/eye.png"
            alt=""
          />
          <div class="text-[#3C4B5D] text-sm mt-10px">报告预览</div>
        </div>
      </div>
    </div>
    <!-- <div class="overflow-hidden">
      <card-chart title="报告导出记录">
        <template #operation-left>
          <div class="flex gap-20px items-center">
            <el-input
              v-model="operatorName"
              placeholder="操作者"
              size="medium"
              class="!w-280px"
            ></el-input>
            <el-select
              v-model="exportFormat"
              placeholder="导出格式"
              size="medium"
              class="!w-180px"
            >
              <el-option
                v-for="item in [{ label: 'pdf', value: 'pdf' }]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-date-picker
              v-model="timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              size="medium"
              class="!w-280px"
            ></el-date-picker>
            <d-button
              type="primary"
              icon="el-icon-vip-sousuo"
            >
              查找
            </d-button>
          </div>
        </template>
        <d-table
          :data="reportHistoryList"
          :columns="databaseColumns"
        ></d-table>
      </card-chart>
    </div> -->

    <dialog-preview :visible.sync="previewVisible"></dialog-preview>
  </div>
</template>

<script setup>
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import DialogPreview from './dialog-preview/index.vue';

const previewVisible = ref(false);
const operatorName = ref('');
const exportFormat = ref('');
const timeRange = ref('');
const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});
const reportHistoryList = ref([
  {
    operator: '张三',
    exportFormat: 'pdf',
    exportTime: '2024-01-01 12:00:00',
  },
]);
const databaseColumns = ref([
  {
    label: '操作者',
    prop: 'operator',
    align: 'center',
  },
  {
    label: '导出格式',
    prop: 'exportFormat',
    align: 'center',
  },
  {
    label: '导出时间',
    prop: 'exportTime',
    align: 'center',
  },
]);

const handlePreviewReport = () => {
  // 显示弹框
  previewVisible.value = true;
};
</script>

<style lang="scss" scoped>
.img {
  background-image: url(./imgs/cover.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
</style>
