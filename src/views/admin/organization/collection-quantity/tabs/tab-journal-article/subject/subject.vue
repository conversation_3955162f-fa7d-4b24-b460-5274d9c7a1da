<template>
  <card-chart
    title="期刊文献学科分布"
    describe="馆藏不同学科分类下的电子期刊文献数量。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <div class="flex gap-10px">
          <el-date-picker
            v-model="year"
            type="year"
            placeholder="请选择年份"
            :clearable="false"
            @change="getData"
          ></el-date-picker>

          <el-select
            v-model="subject"
            placeholder="请选择学科分类"
            :clearable="false"
            @change="getData"
          >
            <el-option
              v-for="item in selectStore.selects.edu_category"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-select
            v-model="language"
            placeholder="请选择语言"
            @change="getData"
            clearable
          >
            <el-option
              v-for="item in selectStore.selects.lan"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch
            v-model="containOA"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">去除重复</span>
          <el-switch
            v-model="removeDuplicates"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="verticalBarOption"
      v-if="displayType === 'chart'"
      v-loading="isSubjectDataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="subjectData"
      :columns="subjectColumns"
      v-loading="isSubjectDataLoading"
      null-text="-"
    ></d-table>
  </card-chart>
</template>

<script setup>
import { createBarChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import store from '@/store';
import { useSubject } from './useSubject';
import { subjectColumns } from '../columns';

const selectStore = store.useSelect();
const { subjectData, isSubjectDataLoading, getSubjectData } = useSubject();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const subject = ref('1');
const language = ref('');
const containOA = ref('1');
const removeDuplicates = ref('1');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

// 示例数据
const categories = computed(() => subjectData.value.map((item) => item.domain_name));
const seriesData = computed(() => [{ name: '期刊文献（篇）', data: subjectData.value.map((item) => item.bookcount) }]);

// 创建纵向柱状图配置
const verticalBarOption = computed(() => {
  return createBarChart(
    {
      xAxisData: categories.value,
      barData: seriesData.value,
    },
    {
      customConfig: {},
    },
  );
});

const getData = async () => {
  await getSubjectData({
    year: year.value,
    eduCategory: subject.value,
    language: language.value,
    oaFlag: containOA.value,
    distinctFlag: removeDuplicates.value,
  });
};
getData();
</script>

<style lang="scss" scoped></style>
