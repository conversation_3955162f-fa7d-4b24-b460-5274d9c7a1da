import api from '@/api';

export const useOa = () => {
  const oaData = ref([]);
  const isOaDataLoading = ref(false);
  const getOaData = async (body) => {
    isOaDataLoading.value = true;
    try {
      const res = await api.organizationApi.getArticleOa(body);
      oaData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isOaDataLoading.value = false;
    }
  };

  return {
    oaData,
    isOaDataLoading,
    getOaData,
  };
};
