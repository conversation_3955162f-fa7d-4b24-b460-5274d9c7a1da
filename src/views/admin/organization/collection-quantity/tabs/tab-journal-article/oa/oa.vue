<template>
  <card-chart
    title="OA期刊文献"
    describe="Open Access Journal，公开获取（免费）的电子期刊文献。"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <div class="flex gap-10px">
          <el-date-picker
            v-model="year"
            type="year"
            placeholder="请选择年份"
            value-format="yyyy"
            :clearable="false"
            @change="getData"
          ></el-date-picker>

          <el-select
            v-model="language"
            placeholder="请选择语言"
            clearable
            @change="getData"
          >
            <el-option
              v-for="item in selectStore.selects.lan"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">去除重复</span>
          <el-switch
            v-model="removeDuplicates"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="customTextLiquidOption"
      v-loading="isOaDataLoading"
    ></dv-chart>
  </card-chart>
</template>

<script setup>
import { createLiquidfillChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useOa } from './useOa';
import store from '@/store';
// import Decimal from 'decimal.js';

const { oaData, isOaDataLoading, getOaData } = useOa();
const selectStore = store.useSelect();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const language = ref('');
const removeDuplicates = ref('1');

const oaPercentage = computed(() => {
  if (oaData.value.length === 0) return 0;
  return oaData.value[0].value;
});

// 自定义文本水球图
const customTextLiquidOption = computed(() => {
  // const v = new Decimal(oaPercentage.value / 100).toFixed(4);
  const v = (oaPercentage.value / 100).toFixed(4);
  console.log(v, 'v');

  return createLiquidfillChart(
    {
      value: Number(v),
      text: 'OA期刊文献收录率',
    },
    {
      customConfig: {
        series: [
          {
            label: {
              fontSize: 20,
              formatter: function (param) {
                return 'OA期刊文献收录率\n' + (param.value * 100).toFixed(2) + '%';
              },
            },
          },
        ],
      },
    },
  );
});

const getData = async () => {
  await getOaData(
    Object.fromEntries(
      Object.entries({
        year: year.value,
        language: language.value,
        distinctFlag: removeDuplicates.value,
      }).filter(([, v]) => v),
    ),
  );
};

getData();
</script>

<style lang="scss" scoped></style>
