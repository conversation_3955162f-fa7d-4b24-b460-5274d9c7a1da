<template>
  <div>
    <journal-article class="mb-5"></journal-article>
    <language class="mb-5"></language>
    <subject class="mb-5"></subject>
    <!-- <oa></oa> -->
    <div class="flex gap-20px mb-20px">
      <div class="flex-1 min-w-300px">
        <oa></oa>
      </div>
      <div class="flex-1 min-w-300px"></div>
    </div>
  </div>
</template>

<script setup>
import JournalArticle from './journal-article/journal-article.vue';
import Language from './language/language.vue';
import Subject from './subject/subject.vue';
import Oa from './oa/oa.vue';
</script>

<style lang="scss" scoped></style>
