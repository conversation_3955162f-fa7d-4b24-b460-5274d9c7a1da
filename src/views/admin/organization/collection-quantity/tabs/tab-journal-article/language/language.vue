<template>
  <card-chart
    title="期刊文献语言分布"
    describe="馆藏中文、外文电子期刊文献数量。"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          :clearable="false"
          value-format="yyyy"
          @change="getData"
          size="medium"
        ></el-date-picker>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch
            v-model="containOA"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">去除重复</span>
          <el-switch
            v-model="removeDuplicates"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <div class="flex gap-20px">
      <div class="flex-1 min-w-300px">
        <dv-chart
          class="h-300px"
          :option="languagePieData"
          v-loading="isLanguageDataLoading"
        ></dv-chart>
      </div>
      <div class="flex-1 min-w-300px">
        <dv-chart
          class="h-300px"
          :option="verticalBarOption"
          v-loading="isLanguageDataLoading"
        ></dv-chart>
      </div>
    </div>
  </card-chart>
</template>

<script setup>
import { createPieChart, createBarChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useLanguage } from './useLanguage';

const { languageData, isLanguageDataLoading, getLanguageData } = useLanguage();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const containOA = ref('1');
const removeDuplicates = ref('1');

const pieData0 = computed(() => {
  return languageData.value
    .filter((item) => item.showtype === '0')
    .map((item) => ({
      name: item.name,
      value: item.value,
    }));
});
const languagePieData = computed(() => {
  return createPieChart(
    { pieData: pieData0.value },
    {
      showTotal: false,
      customConfig: {
        series: [
          {
            radius: ['0%', '70%'],
            emphasis: {
              label: {
                show: false,
              },
            },
          },
        ],
      },
    },
  );
});

const type1Data = computed(() => {
  return languageData.value.filter((item) => item.showtype === '1');
});
const categories = computed(() => type1Data.value.map((item) => item.name));
const seriesData = computed(() => [{ name: '期刊文献（篇）', data: type1Data.value.map((item) => item.value) }]);

// 创建纵向柱状图配置
const verticalBarOption = computed(() => {
  return createBarChart(
    {
      xAxisData: categories.value,
      barData: seriesData.value,
    },
    {
      customConfig: {},
    },
  );
});

const getData = async () => {
  await getLanguageData(
    Object.fromEntries(
      Object.entries({
        year: year.value,
        oaFlag: containOA.value,
        distinctFlag: removeDuplicates.value,
      }).filter(([, v]) => v),
    ),
  );
};

getData();
</script>

<style lang="scss" scoped></style>
