<template>
  <card-chart
    title="图书载体分布"
    describe="纸质、电子馆藏图书数量"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          value-format="yyyy"
          :clearable="false"
          @change="getData"
          size="medium"
        ></el-date-picker>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">去除重复</span>
          <el-switch
            v-model="removeDuplicates"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <div class="flex gap-20px">
      <div class="flex-1 min-w-300px">
        <dv-chart
          class="h-300px"
          :option="oaPieData"
          v-loading="isCarrierDataLoading"
        ></dv-chart>
      </div>
      <div class="flex-1 min-w-300px">
        <dv-chart
          class="h-300px"
          :option="oaPieDataWithTotal"
          v-loading="isCarrierDataLoading"
        ></dv-chart>
      </div>
    </div>
  </card-chart>
</template>

<script setup>
import { createPieChart, createBarChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useCarrier } from './useCarrier';

const { carrierData, isCarrierDataLoading, getCarrierData } = useCarrier();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const removeDuplicates = ref('1');

const pieData0 = computed(() => {
  return carrierData.value
    .filter((item) => item.showtype === '0')
    .map((item) => ({
      name: item.name,
      value: item.value,
    }));
});
const pieData1 = computed(() => {
  return carrierData.value
    .filter((item) => item.showtype === '1')
    .map((item) => ({
      name: item.name,
      value: item.value,
    }));
});
const oaPieData = computed(() => {
  return createPieChart(
    { pieData: pieData0.value },
    {
      showTotal: false,
      customConfig: {
        series: [
          {
            radius: ['0%', '70%'],
            emphasis: {
              label: {
                show: false,
              },
            },
          },
        ],
      },
    },
  );
});
const oaPieDataWithTotal = computed(() => {
  return createPieChart(
    { pieData: pieData1.value },
    {
      customConfig: {
        series: [
          {
            emphasis: {
              label: {
                show: true,
              },
            },
          },
        ],
      },
    },
  );
});

const getData = async () => {
  await getCarrierData({
    year: year.value,
    distinctFlag: removeDuplicates.value,
  });
};

(async () => {
  getData();
})();
</script>

<style lang="scss" scoped></style>
