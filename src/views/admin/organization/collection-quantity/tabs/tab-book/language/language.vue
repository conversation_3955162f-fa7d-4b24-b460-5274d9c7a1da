<template>
  <card-chart
    title="图书语种分布"
    describe="中文、外文馆藏图书数量"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          value-format="yyyy"
          @change="getData"
          :clearable="false"
          size="medium"
        ></el-date-picker>
      </div>
    </template>
    <div class="flex gap-20px">
      <div class="flex-1 min-w-300px">
        <dv-chart
          class="h-300px"
          :option="languagePieData"
          v-loading="isLanguageDataLoading"
        ></dv-chart>
      </div>
      <div class="flex-1 min-w-300px">
        <dv-chart
          class="h-300px"
          :option="verticalBarOption"
          v-loading="isLanguageDataLoading"
        ></dv-chart>
      </div>
    </div>
  </card-chart>
</template>

<script setup>
import { createPie<PERSON><PERSON>, createBarChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { computed } from 'vue';
import { useLanguage } from './useLanguage';

const { languageData, isLanguageDataLoading, getLanguageData } = useLanguage();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);

const sourceData = computed(() => {
  return languageData.value.filter((item) => item.showtype === '0');
});

const pieData = computed(() => {
  return sourceData.value.map((item) => ({
    name: item.name,
    value: item.value || 0,
  }));
});
const languagePieData = computed(() => {
  return createPieChart(
    { pieData: pieData.value },
    {
      showTotal: false,
      customConfig: {
        series: [
          {
            radius: ['0%', '70%'],
            emphasis: {
              label: {
                show: false,
              },
            },
          },
        ],
      },
    },
  );
});

const type1Data = computed(() => {
  return languageData.value.filter((item) => item.showtype === '1');
});
const categories = computed(() => type1Data.value.map((item) => item.name));
const seriesData = computed(() => {
  return [
    {
      name: '图书数量（种）',
      data: type1Data.value.map((item) => item.value || 0),
    },
  ];
});

// 创建纵向柱状图配置
const verticalBarOption = computed(() => {
  return createBarChart(
    {
      xAxisData: categories.value,
      barData: seriesData.value,
    },
    {
      customConfig: {},
    },
  );
});

const getData = async () => {
  await getLanguageData({
    year: year.value,
  });
};

(async () => {
  getData();
})();
</script>

<style lang="scss" scoped></style>
