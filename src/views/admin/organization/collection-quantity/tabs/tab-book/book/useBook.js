import api from '@/api';

export const useBook = () => {
  const bookData = ref([]);
  const isBookDataLoading = ref(false);
  const getBookData = async (body) => {
    isBookDataLoading.value = true;
    try {
      const res = await api.organizationApi.getBookCount(body);
      bookData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isBookDataLoading.value = false;
    }
  };

  return {
    bookData,
    isBookDataLoading,
    getBookData,
  };
};
