import api from '@/api';

export const useSubject = () => {
  const subjectData = ref([]);
  const isSubjectDataLoading = ref(false);
  const getSubjectData = async (body) => {
    isSubjectDataLoading.value = true;
    try {
      const res = await api.organizationApi.getJournalSubject(body);
      subjectData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isSubjectDataLoading.value = false;
    }
  };

  return {
    subjectData,
    isSubjectDataLoading,
    getSubjectData,
  };
};
