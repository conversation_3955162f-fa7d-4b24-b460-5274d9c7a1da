export const journalColumns = [
  {
    label: '年份',
    prop: 'year',
    align: 'center',
    sortable: true,
  },
  {
    label: '期刊种数',
    prop: 'totalcate',
    sortable: true,
  },
  {
    label: '期刊册数',
    prop: 'totalcount',
    sortable: true,
  },
  {
    label: '中文期刊种数',
    prop: 'zhcate',
    sortable: true,
  },
  {
    label: '中文期刊册数',
    prop: 'zhcount',
    sortable: true,
  },
  {
    label: '外文期刊种数',
    prop: 'uncate',
    sortable: true,
  },
  {
    label: '外文期刊册数',
    prop: 'uncount',
    sortable: true,
  },
];
export const subjectColumns = [
  {
    label: '学科',
    prop: 'domain_name',
    sortable: true,
  },
  {
    label: '期刊数量（种）',
    prop: 'catecount',
    sortable: true,
  },
  {
    label: '期刊数量（册）',
    prop: 'bookcount',
    sortable: true,
  },
];
export const statusColumns = [
  {
    label: '年份',
    prop: 'year',
    align: 'center',
    sortable: true,
  },
  {
    label: '期刊种数',
    prop: 'totalcate',
    sortable: true,
  },
  {
    label: '现刊种数',
    prop: 'xkcate',
    sortable: true,
  },
  {
    label: '过刊种数',
    prop: 'gkcate',
    sortable: true,
  },
  {
    label: '停刊种数',
    prop: 'tkcate',
    sortable: true,
  },
];
