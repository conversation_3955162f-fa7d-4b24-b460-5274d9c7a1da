<template>
  <div>
    <journal class="mb-5"></journal>
    <language class="mb-5"></language>
    <carrier class="mb-5"></carrier>
    <subject class="mb-5"></subject>

    <div class="flex gap-5 mb-5">
      <div class="w-550px max-w-700px">
        <oa></oa>
      </div>
      <div class="flex-1 min-w-860px">
        <status></status>
      </div>
    </div>
  </div>
</template>

<script setup>
import { createLineChart } from '@/utils/chartConfig';
import Journal from './journal/journal.vue';
import Language from './language/language.vue';
import Carrier from './carrier/carrier.vue';
import Subject from './subject/subject.vue';
import Oa from './oa/oa.vue';
import Status from './status/status.vue';
</script>

<style lang="scss" scoped></style>
