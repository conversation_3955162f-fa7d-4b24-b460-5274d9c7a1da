import api from '@/api';

export const useCarrier = () => {
  const carrierData = ref([]);
  const isCarrierDataLoading = ref(false);
  const getCarrierData = async (body) => {
    isCarrierDataLoading.value = true;
    try {
      const res = await api.organizationApi.getJournalCarrier(body);
      carrierData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isCarrierDataLoading.value = false;
    }
  };

  return {
    carrierData,
    isCarrierDataLoading,
    getCarrierData,
  };
};
