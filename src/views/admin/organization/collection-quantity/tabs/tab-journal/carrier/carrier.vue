<template>
  <card-chart
    title="期刊载体分布"
    describe="纸质、电子馆藏期刊数量"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          value-format="yyyy"
          @change="getData"
          :clearable="false"
        ></el-date-picker>

        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch
            v-model="containOA"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">去除重复</span>
          <el-switch
            v-model="removeDuplicates"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <div class="flex gap-20px">
      <div class="flex-1 min-w-300px">
        <dv-chart
          class="h-300px"
          :option="oaPieData"
          v-loading="isCarrierDataLoading"
        ></dv-chart>
      </div>
      <div class="flex-1 min-w-300px">
        <dv-chart
          class="h-300px"
          :option="oaPieDataWithTotal"
          v-loading="isCarrierDataLoading"
        ></dv-chart>
      </div>
    </div>
  </card-chart>
</template>

<script setup>
import { createPieChart, createBarChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { computed } from 'vue';
import { useCarrier } from './useCarrier';

const { carrierData, isCarrierDataLoading, getCarrierData } = useCarrier();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const containOA = ref('1');
const removeDuplicates = ref('1');

const sourceData = computed(() => {
  return carrierData.value;
});

const pieDataCate = computed(() => sourceData.value.filter((item) => item.showtype === '0'));
const pieDataCount = computed(() => sourceData.value.filter((item) => item.showtype === '1'));

const oaPieData = computed(() => {

  return createPieChart(
    { pieData: pieDataCate.value },
    {
      showTotal: false,
      customConfig: {
        series: [
          {
            radius: ['0%', '70%'],
            emphasis: {
              label: {
                show: false,
              },
            },
          },
        ],
      },
    },
  );
});
const oaPieDataWithTotal = computed(() => {
  return createPieChart(
    { pieData: pieDataCount.value },
    {
      customConfig: {
        series: [
          {
            radius: ['40%', '70%'],
            emphasis: {
              label: {
                show: true,
              },
            },
          },
        ],
      },
    },
  );
});

const getData = async () => {
  await getCarrierData({
    year: year.value,
    oaFlag: containOA.value,
    distinctFlag: removeDuplicates.value,
  });
};

(async () => {
  getData();
})();
</script>

<style lang="scss" scoped></style>
