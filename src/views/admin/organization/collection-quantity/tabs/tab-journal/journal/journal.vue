<template>
  <card-chart
    title="期刊"
    describe="馆藏期刊数量"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          size="medium"
          :clearable="false"
          @change="getData"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch
            v-model="containOA"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">去除重复</span>
          <el-switch
            v-model="removeDuplicates"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="lineChartData"
      v-if="displayType === 'chart'"
      v-loading="isJournalDataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="journalData"
      :columns="journalColumns"
      v-loading="isJournalDataLoading"
      null-text="-"
    ></d-table>
  </card-chart>
</template>

<script setup>
import { createLineChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useJournal } from './useJournal';
import { journalColumns } from '../columns';
import dayjs from 'dayjs';

const { journalData, isJournalDataLoading, getJournalData } = useJournal();

const now = dayjs();
const fiveYearsAgo = dayjs().subtract(5, 'year');
const timeRange = ref([fiveYearsAgo.format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]); // 默认近5年

const containOA = ref('1');
const removeDuplicates = ref('1');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});

const chartData = computed(() => [
  {
    name: '期刊数量（种）',
    data: journalData.value.map((item) => {
      const total = Number(item.totalcate);
      return isNaN(total) ? 0 : total;
    }),
  },
  {
    name: '期刊数量（册）',
    data: journalData.value.map((item) => {
      const total = Number(item.totalcount);
      return isNaN(total) ? 0 : total;
    }),
  },
]);
const xAxisData = computed(() => journalData.value.map((item) => item.year));

const lineChartData = computed(() => {
  const chart = {
    xAxisData: xAxisData.value,
    lineData: chartData.value,
  };
  return createLineChart(chart, {
    customConfig: {
      // legend: {
      //   right: 20, // 调整图例位置
      // },
    },
  });
});

const getData = async () => {
  await getJournalData({
    startTime: timeRange.value[0],
    endTime: timeRange.value[1],
    oaFlag: containOA.value,
    distinctFlag: removeDuplicates.value,
  });
};
(async () => {
  getData();
})();
</script>

<style lang="scss" scoped></style>
