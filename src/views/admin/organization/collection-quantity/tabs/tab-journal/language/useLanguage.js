import api from '@/api';

export const useLanguage = () => {
  const languageData = ref([]);
  const isLanguageDataLoading = ref(false);
  const getLanguageData = async (body) => {
    isLanguageDataLoading.value = true;
    try {
      const res = await api.organizationApi.getJournalLanguage(body);
      languageData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isLanguageDataLoading.value = false;
    }
  };

  return {
    languageData,
    isLanguageDataLoading,
    getLanguageData,
  };
};
