import api from '@/api';

export const useStatus = () => {
  const statusData = ref([]);
  const isStatusDataLoading = ref(false);
  const getStatusData = async (body) => {
    isStatusDataLoading.value = true;
    try {
      const res = await api.organizationApi.getJournalStatus(body);
      statusData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isStatusDataLoading.value = false;
    }
  };

  return {
    statusData,
    isStatusDataLoading,
    getStatusData,
  };
};
