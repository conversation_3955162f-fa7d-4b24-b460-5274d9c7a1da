<template>
  <card-chart
    title="期刊状态分布"
    describe="统计现刊、过刊、停刊数量。现刊：非停刊期刊，可访问年份是最近2年的，反之过刊。停刊：注销刊号的期刊。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <div class="flex gap-10px">
          <el-date-picker
            v-model="timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            size="medium"
            :clearable="false"
            value-format="yyyy-MM-dd"
            @change="getData"
          ></el-date-picker>

          <el-select
            v-model="language"
            placeholder="请选择语言"
            clearable
            @change="getData"
            class="!w-120px"
            size="medium"
          >
            <el-option
              v-for="item in selectStore.selects.lan"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch
            v-model="containOA"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">去除重复</span>
          <el-switch
            v-model="removeDuplicates"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="lineChartData"
      v-if="displayType === 'chart'"
      v-loading="isStatusDataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="statusData"
      :columns="statusColumns"
      v-loading="isStatusDataLoading"
      null-text="-"
    >
      <template #xkcate-th="scope">
        <span>{{ scope.column.label }}</span>
        <el-tooltip>
          <template v-slot:content>
            <div>
              指期刊的可访问年份是最近2年的（ 假设当前日期为2025年3月21日，期刊在2024年1月1日起出版过，并且非停刊）
            </div>
          </template>
          <d-icon
            name="el-icon-vip-icon-tishi41"
            class="text-primary align-middle"
          />
        </el-tooltip>
      </template>
      <template #gkcate-th="scope">
        <span>{{ scope.column.label }}</span>
        <el-tooltip>
          <template v-slot:content>
            <div>
              指期刊的可访问年份是最近2年的（ 假设当前日期为2025年3月21日，期刊在2024年1月1日起未出版过，并且非停刊）
            </div>
          </template>
          <d-icon
            name="el-icon-vip-icon-tishi41"
            class="text-primary align-middle"
          />
        </el-tooltip>
      </template>
      <template #tkcate-th="scope">
        <span>{{ scope.column.label }}</span>
        <el-tooltip>
          <template v-slot:content>
            <div>已经注销刊号的期刊</div>
          </template>
          <d-icon
            name="el-icon-vip-icon-tishi41"
            class="text-primary align-middle"
          />
        </el-tooltip>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { createLineChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useStatus } from './useStatus';
import dayjs from 'dayjs';
import store from '@/store';
import { statusColumns } from '../columns';

const selectStore = store.useSelect();

const { statusData, isStatusDataLoading, getStatusData } = useStatus();

const now = dayjs();
const fiveYearsAgo = dayjs().subtract(5, 'year');
const timeRange = ref([fiveYearsAgo.format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]); // 默认近5年

const language = ref('');
const containOA = ref('1');
const removeDuplicates = ref('1');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});

const chartData = computed(() => [
  { name: '现刊（种）', data: statusData.value.map((item) => item.xkcate ?? 0) },
  { name: '过刊（种）', data: statusData.value.map((item) => item.gkcate ?? 0) },
  { name: '停刊（种）', data: statusData.value.map((item) => item.tkcate ?? 0) },
]);
const xAxisData = computed(() => statusData.value.map((item) => item.year));

const lineChartData = computed(() => {
  const chart = {
    xAxisData: xAxisData.value,
    lineData: chartData.value,
  };
  return createLineChart(chart, {
    customConfig: {
      // legend: {
      //   right: 20, // 调整图例位置
      // },
    },
  });
});

const getData = () => {
  getStatusData(
    Object.fromEntries(
      Object.entries({
        startTime: timeRange.value[0],
        endTime: timeRange.value[1],
        language: language.value,
        oaFlag: containOA.value,
        distinctFlag: removeDuplicates.value,
      }).filter(([, v]) => v),
    ),
  );
};
(async () => {
  getData();
})();
</script>

<style lang="scss" scoped></style>
