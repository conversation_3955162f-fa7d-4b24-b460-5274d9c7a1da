import api from '@/api';

export const useNonRepetitive = () => {
  const nonRepetitiveData = ref([]);
  const isNonRepetitiveDataLoading = ref(false);
  const getNonRepetitiveData = async (body) => {
    isNonRepetitiveDataLoading.value = true;
    try {
      const res = await api.organizationApi.getNonRepetitive(body);
      nonRepetitiveData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isNonRepetitiveDataLoading.value = false;
    }
  };

  return {
    nonRepetitiveData,
    isNonRepetitiveDataLoading,
    getNonRepetitiveData,
  };
};
