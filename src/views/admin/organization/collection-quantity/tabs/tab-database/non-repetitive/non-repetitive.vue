<template>
  <card-chart
    title="数据库不重复文献分析"
    describe="分析馆藏不重复文献的情况，即在馆藏文献中，仅出现过1次。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-10px">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          value-format="yyyy"
          @change="getData"
          :clearable="false"
        ></el-date-picker>

        <el-select
          v-model="docType"
          placeholder="请选择文献类型"
          @change="getData"
        >
          <el-option
            v-for="item in [
              { label: '期刊', value: '2' },
              { label: '期刊文献', value: '3' },
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-select
          v-model="language"
          placeholder="请选择语言"
          clearable
          @change="getData"
        >
          <el-option
            v-for="item in selectStore.selects.lan"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="scatterOption"
      v-if="displayType === 'chart'"
      v-loading="isNonRepetitiveDataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="nonRepetitiveData"
      :columns="nonRepetitiveColumns"
      v-loading="isNonRepetitiveDataLoading"
      null-text="-"
    >
      <template #indicator_ratio="scope">
        <span>{{ scope.row.indicator_ratio ?? 0 }}%</span>
      </template>
      <template #indicator_sec_ratio="scope">
        <span>{{ scope.row.indicator_sec_ratio }}%</span>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { createScatterChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useNonRepetitive } from './useNonRepetitive';
import store from '@/store';
import { nonRepetitiveColumns } from '../columns';

const selectStore = store.useSelect();
const { nonRepetitiveData, isNonRepetitiveDataLoading, getNonRepetitiveData } = useNonRepetitive();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const docType = ref('2');
const language = ref('');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

const getData = () => {
  getNonRepetitiveData(
    Object.fromEntries(
      Object.entries({
        year: year.value,
        language: language.value,
        docType: docType.value,
      }).filter(([, v]) => v),
    ),
  );
};
(async () => {
  getData();
})();

const scatterData = computed(() => [
  {
    name: '数据库',
    data: nonRepetitiveData.value.map((item) => [item.dbname, Number(item.indicator_value)]),
  },
]);

// 创建散点图配置
const scatterOption = computed(() => {
  return createScatterChart(
    {
      scatterData: scatterData.value,
    },
    {
      colors: ['#6777EF', '#F2A940'],
      symbolSize: 12,
      customConfig: {
        legend: { show: false },
      },
    },
  );
});
</script>

<style lang="scss" scoped></style>
