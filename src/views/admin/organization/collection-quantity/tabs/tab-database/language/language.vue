<template>
  <card-chart
    title="数据库语言类型分布"
    describe="馆藏数据库语言的分布情况"
    show-export
  >
    <template #operation-left>
      <el-date-picker
        v-model="year"
        type="year"
        value-format="yyyy"
        placeholder="请选择年份"
        @change="handleYearChange"
        :clearable="false"
      ></el-date-picker>
    </template>
    <dv-chart
      class="h-300px"
      :option="procurementStauts"
      v-loading="isLanguageDataLoading"
    ></dv-chart>
  </card-chart>
</template>

<script setup>
import { createPieChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useLanguage } from './useLanguage';

const { languageData, isLanguageDataLoading, getLanguageData } = useLanguage();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);

const handleYearChange = () => {
  getLanguageData(year.value);
};

const sourceData = computed(() => {
  return languageData.value;
});

const pieData = computed(() => {
  return sourceData.value.map((item) => ({
    name: item.name,
    value: item.value,
  }));
});
const procurementStauts = computed(() => {
  return createPieChart(
    { pieData: pieData.value },
    {
      customConfig: {
        // series: [{ radius: ['0%', '30%'] }],
      },
    },
  );
});

(async () => {
  getLanguageData(year.value);
})();
</script>

<style lang="scss" scoped></style>
