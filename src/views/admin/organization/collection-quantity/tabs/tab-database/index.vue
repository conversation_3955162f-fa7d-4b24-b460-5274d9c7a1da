<template>
  <div>
    <database class="mb-20px"></database>

    <div class="flex gap-20px mb-20px">
      <div class="flex-1 min-w-300px">
        <procurement></procurement>
      </div>
      <div class="flex-1 min-w-300px">
        <language></language>
      </div>
    </div>
    <div class="flex gap-20px mb-20px">
      <div class="flex-1 min-w-300px">
        <literature></literature>
      </div>
      <div class="flex-1 min-w-300px">
        <subject></subject>
      </div>
    </div>
    <non-repetitive class="mb-20px"></non-repetitive>
    <oa></oa>
  </div>
</template>

<script setup>
import Database from './database/database.vue';
import Procurement from './procurement/procurement.vue';
import Language from './language/language.vue';
import Literature from './literature/literature.vue';
import Subject from './subject/subject.vue';
import NonRepetitive from './non-repetitive/non-repetitive.vue';
import Oa from './oa/oa.vue';
</script>

<style lang="scss" scoped></style>
