import api from '@/api';

export const useDatabase = () => {
  const databaseData = ref([]);
  const isDatabaseDataLoading = ref(false);
  const getDatabaseData = async (timeRange) => {
    isDatabaseDataLoading.value = true;
    try {
      const body = {
        startTime: timeRange[0],
        endTime: timeRange[1],
      };
      const res = await api.organizationApi.getDatabase(body);
      databaseData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isDatabaseDataLoading.value = false;
    }
  };

  return {
    databaseData,
    isDatabaseDataLoading,
    getDatabaseData,
  };
};
