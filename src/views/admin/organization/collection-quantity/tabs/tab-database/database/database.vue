<template>
  <card-chart
    title="数据库"
    describe="馆藏数据库数量以及经费"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <el-date-picker
        v-model="timeRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="pickerOptions"
        size="medium"
        value-format="yyyy-MM-dd"
        @change="handleDateChange"
        :clearable="false"
      ></el-date-picker>
    </template>
    <dv-chart
      class="h-300px"
      :option="lineChartData"
      v-if="displayType === 'chart'"
      v-loading="isDatabaseDataLoading"
    ></dv-chart>

    <d-table
      v-if="displayType === 'table'"
      :data="databaseTableData"
      :columns="databaseColumns"
      v-loading="isDatabaseDataLoading"
      null-text="-"
    >
      <template #dbamt-th="scope">
        <span>{{ scope.column.label }}</span>
        <el-tooltip>
          <template v-slot:content>
            <div>数据库的合同金额</div>
          </template>
          <d-icon
            name="el-icon-vip-icon-tishi41"
            class="text-primary align-middle"
          />
        </el-tooltip>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { createLineChart, createBarChart, createScatterChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useDatabase } from './useDatabase';
import { databaseColumns } from '../columns';
import dayjs from 'dayjs';

const now = dayjs();
const fiveYearsAgo = dayjs().subtract(5, 'year');
const timeRange = ref([fiveYearsAgo.format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]); // 默认近5年

const { databaseData, isDatabaseDataLoading, getDatabaseData } = useDatabase();

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});

const handleDateChange = (v) => {
  getDatabaseData(timeRange.value);
};

const chartData = computed(() => {
  return [
    {
      name: '数据库数量（个）',
      data: databaseData.value.map((item) => {
        const count = Number(item.dbcount);
        return isNaN(count) ? 0 : count;
      }),
    },
    {
      name: '数据库经费（万元）',
      data: databaseData.value.map((item) => {
        const amt = Number(item.dbamt);
        return isNaN(amt) ? 0 : amt;
      }),
    },
  ];
});

const xAxisData = computed(() => {
  return databaseData.value.map((item) => item.year);
});

const lineChartData = computed(() => {
  const chart = {
    xAxisData: xAxisData.value,
    lineData: chartData.value,
  };
  return createLineChart(chart, {
    customConfig: {},
  });
});

const databaseTableData = computed(() => {
  return databaseData.value;
});

(async () => {
  getDatabaseData(timeRange.value);
})();
</script>

<style lang="scss" scoped></style>
