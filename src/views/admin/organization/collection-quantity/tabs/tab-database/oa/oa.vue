<template>
  <card-chart
    title="数据库OA文献分析"
    describe="统计数据库内的OA文献数量，以数据库实际标记为准。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-10px">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          value-format="yyyy"
          @change="getData"
          :clearable="false"
          size="medium"
        ></el-date-picker>

        <el-select
          v-model="docType"
          placeholder="请选择文献类型"
          @change="getData"
          size="medium"
        >
          <el-option
            v-for="item in [
              { label: '期刊', value: '2' },
              { label: '期刊文献', value: '3' },
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-select
          v-model="language"
          placeholder="请选择语言"
          @change="getData"
          clearable
          size="medium"
        >
          <el-option
            v-for="item in selectStore.selects.lan"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="basicTreemapOption"
      :events="{
        click: handleTreemapClick,
      }"
      v-if="displayType === 'chart'"
      v-loading="isOaDataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="oaData"
      :columns="oaColumns"
      v-loading="isOaDataLoading"
      null-text="-"
    >
      <template #indicator_ratio="scope">
        <span>{{ scope.row.indicator_ratio ?? 0 }}%</span>
      </template>
      <template #indicator_sec_ratio="scope">
        <span>{{ scope.row.indicator_sec_ratio }}%</span>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { ref, computed } from 'vue';
import { createTreemapChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { oaColumns } from '../columns';
import { useOa } from './useOa';
import store from '@/store';

const selectStore = store.useSelect();

const { oaData, isOaDataLoading, getOaData } = useOa();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const docType = ref('2');
const language = ref('');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

const getData = () => {
  getOaData(
    Object.fromEntries(
      Object.entries({
        year: year.value,
        language: language.value,
        docType: docType.value,
      }).filter(([, v]) => v),
    ),
  );
};
(async () => {
  getData();
})();

// 基础矩形树图数据
const basicTreeData = computed(() =>
  oaData.value.map((item) => ({
    name: item.dbname,
    value: Number(item.indicator_value),
  })),
);

// 基础矩形树图配置
const basicTreemapOption = computed(() => {
  return createTreemapChart(
    { treeData: basicTreeData.value },
    {
      showLabel: true,
      roam: false,
      customConfig: {
        series: [
          {
            width: '100%',
            height: '100%',
            nodeClick: false,
            breadcrumb: {
              show: false,
            },
          },
        ],
      },
    },
  );
});

// 多层级矩形树图数据

// 存储点击信息
const clickInfo = ref(null);

// 处理点击事件
const handleTreemapClick = (params) => {
  if (params.data) {
    // 获取点击的节点信息
    clickInfo.value = {
      name: params.data.name,
      value: params.data.value,
      path: params.treePathInfo.map((item) => item.name).join(' > '),
      level: params.treePathInfo.length - 1,
      // 保存原始数据，以便进行更多操作
      rawData: params.data,
    };

    console.log('点击了矩形树图节点:', params);

    // 这里可以添加更多的业务逻辑
    // 例如：打开详情面板、下钻到下一级、触发数据加载等
  }
};
</script>

<style scoped></style>
