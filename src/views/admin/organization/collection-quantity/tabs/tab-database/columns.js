export const databaseColumns = [
  {
    label: '年份',
    prop: 'year',
    align: 'center',
    sortable: true,
  },
  {
    label: '数据库数量（个）',
    prop: 'dbcount',
    sortable: true,
  },
  {
    label: '数据库经费（万元）',
    prop: 'dbamt',
    sortable: true,
  },
];

export const nonRepetitiveColumns = [
  {
    label: '数据库',
    prop: 'dbname',
    sortable: true,
  },
  {
    label: '文献类型',
    prop: 'docname',
    sortable: true,
  },
  {
    label: '语言',
    prop: 'language',
    sortable: true,
    align: 'center',
  },
  {
    label: '文献数量',
    prop: 'indicator_total',
    sortable: true,
  },
  {
    label: '不重复文献',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '不重复率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: '重复文献',
    prop: 'indicator_sec_value',
    sortable: true,
  },
  {
    label: '重复率',
    prop: 'indicator_sec_ratio',
    sortable: true,
  },
];

export const oaColumns = [
  {
    label: '数据库',
    prop: 'dbname',
    sortable: true,
  },
  {
    label: '文献类型',
    prop: 'docname',
    sortable: true,
  },
  {
    label: '语言',
    prop: 'language',
    sortable: true,
    align: 'center',
  },
  {
    label: '文献数量',
    prop: 'indicator_total',
    sortable: true,
  },
  {
    label: '非OA文献',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '非OA率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: 'OA文献',
    prop: 'indicator_sec_value',
    sortable: true,
  },
  {
    label: 'OA率',
    prop: 'indicator_sec_ratio',
    sortable: true,
  },
];
