<template>
  <card-chart
    title="数据库文献类型分布"
    describe="馆藏数据库文献类型的分布情况"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-10px">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          @change="getData"
          :clearable="false"
        ></el-date-picker>

        <el-select
          v-model="orderType"
          placeholder="请选择采购状态"
          clearable
          @change="getData"
        >
          <el-option
            v-for="item in selectStore.selects.order_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-select
          v-model="language"
          placeholder="请选择语言"
          clearable
          @change="getData"
        >
          <el-option
            v-for="item in selectStore.selects.lan"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="verticalBarOption"
      v-loading="isLiteratureDataLoading"
    ></dv-chart>
  </card-chart>
</template>

<script setup>
import { createBarChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import store from '@/store';
import { useLiterature } from './useLiterature';

const selectStore = store.useSelect();
const { literatureData, isLiteratureDataLoading, getLiteratureData } = useLiterature();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const orderType = ref('');
const language = ref('');

const getData = () => {
  getLiteratureData(
    Object.fromEntries(
      Object.entries({
        year: year.value,
        language: language.value,
        orderType: orderType.value,
      }).filter(([, v]) => v),
    ),
  );
};
(async () => {
  getData();
})();

// 示例数据
const categories = computed(() => {
  return literatureData.value.map((item) => item.name);
});
const seriesData = computed(() => {
  return [{ name: '数据库数量', data: literatureData.value.map((item) => item.value) }];
});

// 创建纵向柱状图配置
const verticalBarOption = computed(() => {
  return createBarChart(
    {
      xAxisData: categories.value,
      barData: seriesData.value,
    },
    {
      customConfig: {},
    },
  );
});
</script>

<style lang="scss" scoped></style>
