import api from '@/api';

export const useLiterature = () => {
  const literatureData = ref([]);
  const isLiteratureDataLoading = ref(false);
  const getLiteratureData = async (body) => {
    isLiteratureDataLoading.value = true;
    try {
      const res = await api.organizationApi.getLiterature(body);
      literatureData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isLiteratureDataLoading.value = false;
    }
  };

  return {
    literatureData,
    isLiteratureDataLoading,
    getLiteratureData,
  };
};
