<template>
  <card-chart
    title="数据库采购状态分布"
    describe="馆藏数据库采购状态的分布情况"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-10px">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          value-format="yyyy"
          @change="handleYearChange"
          :clearable="false"
          size="medium"
        ></el-date-picker>

        <el-select
          v-model="language"
          placeholder="请选择语言"
          @change="handleLanguageChange"
          clearable
          size="medium"
        >
          <el-option
            v-for="item in selectStore.selects.lan"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="procurementStauts"
      v-loading="isProcurementDataLoading"
    ></dv-chart>
  </card-chart>
</template>

<script setup>
import { createPieChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import store from '@/store';
import { useProcurement } from './useProcurement';

const { procurementData, isProcurementDataLoading, getProcurementData } = useProcurement();

const selectStore = store.useSelect();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const language = ref('');

const handleYearChange = (v) => {
  getData();
};
const handleLanguageChange = (v) => {
  getData();
};

const sourceData = computed(() => {
  return procurementData.value;
});

const pieData = computed(() => {
  return sourceData.value.map((item) => ({
    name: item.name,
    value: item.value,
  }));
});
const procurementStauts = computed(() => {
  return createPieChart(
    { pieData: pieData.value },
    {
      showTotal: false,
      customConfig: {
        series: [
          {
            radius: ['0%', '70%'],
            emphasis: {
              label: {
                show: false,
              },
            },
          },
        ],
      },
    },
  );
});

const getData = () => {
  getProcurementData(
    Object.fromEntries(
      Object.entries({
        year: year.value,
        language: language.value,
      }).filter(([, v]) => v),
    ),
  );
};

(async () => {
  getData();
})();
</script>

<style lang="scss" scoped></style>
