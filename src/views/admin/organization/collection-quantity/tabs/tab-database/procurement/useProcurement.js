import api from '@/api';

export const useProcurement = () => {
  const procurementData = ref([]);
  const isProcurementDataLoading = ref(false);
  const getProcurementData = async ({ year, language }) => {
    isProcurementDataLoading.value = true;
    try {
      const res = await api.organizationApi.getProcurement({
        year,
        language,
      });
      procurementData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isProcurementDataLoading.value = false;
    }
  };

  return {
    procurementData,
    isProcurementDataLoading,
    getProcurementData,
  };
};
