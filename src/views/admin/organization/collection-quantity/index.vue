<template>
  <div>
    <div class="h-11 flex gap-10px">
      <div
        class="tab-item cursor-pointer h-9 leading-9 bg-[#F0F4FF] text-[#697FFA] px-4 rounded-3px text-sm"
        v-for="tab in tabs"
        :key="tab.name"
        @click="handleTabClick(tab)"
        :class="{
          active: tab.name === currentTabName,
        }"
      >
        {{ tab.label }}
      </div>
    </div>
    <tab-database
      class="mt-5"
      v-if="currentTabName === '1'"
    ></tab-database>
    <tab-journal
      class="mt-5"
      v-if="currentTabName === '2'"
    ></tab-journal>
    <tab-book
      class="mt-5"
      v-if="currentTabName === '3'"
    ></tab-book>
    <tab-journal-article
      class="mt-5"
      v-if="currentTabName === '4'"
    ></tab-journal-article>
  </div>
</template>

<script setup>
import TabDatabase from './tabs/tab-database/index.vue';
import TabJournal from './tabs/tab-journal/index.vue';
import TabBook from './tabs/tab-book/index.vue';
import TabJournalArticle from './tabs/tab-journal-article/index.vue';

const tabs = ref([
  {
    name: '1',
    label: '数据库',
  },
  {
    name: '2',
    label: '期刊',
  },
  {
    name: '3',
    label: '图书',
  },
  {
    name: '4',
    label: '期刊文献',
  },
]);

const currentTabName = ref('1');

const handleTabClick = (tab) => {
  currentTabName.value = tab.name;
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  overflow: visible;
}
.tab-item.active {
  color: white;
  background: #697ffa;
}
</style>
