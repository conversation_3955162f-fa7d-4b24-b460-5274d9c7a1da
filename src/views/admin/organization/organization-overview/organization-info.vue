<template>
  <div
    class="text-sm text-[#3C4B5D]"
    v-loading="isOrganizationInfoLoading"
  >
    <div
      class="indent-2em"
      v-for="(item, index) in organizationInfo"
      :key="index"
    >
      {{ item }}
    </div>
  </div>
</template>

<script setup>
import api from '@/api';

const organizationInfo = ref([]);
const isOrganizationInfoLoading = ref(false);
const getOrganizationInfo = async () => {
  isOrganizationInfoLoading.value = true;
  const res = await api.organizationApi.getOrganizationInfo();
  const detail = res?.list?.[0]?.detail || '';
  organizationInfo.value = detail.split('<br/>');
  isOrganizationInfoLoading.value = false;
};

(async () => {
  await getOrganizationInfo();
})();
</script>

<style lang="scss" scoped></style>
