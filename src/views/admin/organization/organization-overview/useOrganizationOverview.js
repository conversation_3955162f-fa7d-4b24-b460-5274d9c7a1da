import api from '@/api';

export const useOrganizationOverview = () => {
  const collectionCountList = ref([]);
  const isCollectionCountListLoading = ref(false);
  const getCollectionCountList = async () => {
    try {
      isCollectionCountListLoading.value = true;
      const res = await api.organizationApi.getCollectionCount();
      collectionCountList.value = res.list;
      isCollectionCountListLoading.value = false;
    } catch (error) {
      console.log(error);
    }
  };

  const keyCollectionList = ref([]);
  const isKeyCollectionListLoading = ref(false);
  const getKeyCollectionList = async () => {
    try {
      isKeyCollectionListLoading.value = true;
      const res = await api.organizationApi.getKeyCollection();
      keyCollectionList.value = res.list;
      isKeyCollectionListLoading.value = false;
    } catch (error) {
      console.log(error);
    }
  };

  const achievementList = ref([]);
  const isAchievementListLoading = ref(false);
  const getAchievementList = async () => {
    try {
      isAchievementListLoading.value = true;
      const res = await api.organizationApi.getAchievement();
      achievementList.value = res.list;
      isAchievementListLoading.value = false;
    } catch (error) {
      console.log(error);
    }
  };

  const literatureBehaviorList = ref([]);
  const isLiteratureBehaviorListLoading = ref(false);
  const getLiteratureBehaviorList = async () => {
    try {
      isLiteratureBehaviorListLoading.value = true;
      const res = await api.organizationApi.getLiteratureBehavior();
      literatureBehaviorList.value = res.list;
      isLiteratureBehaviorListLoading.value = false;
    } catch (error) {
      console.log(error);
    }
  };

  return {
    collectionCountList,
    isCollectionCountListLoading,
    getCollectionCountList,

    keyCollectionList,
    isKeyCollectionListLoading,
    getKeyCollectionList,

    achievementList,
    isAchievementListLoading,
    getAchievementList,

    literatureBehaviorList,
    isLiteratureBehaviorListLoading,
    getLiteratureBehaviorList,
  };
};
