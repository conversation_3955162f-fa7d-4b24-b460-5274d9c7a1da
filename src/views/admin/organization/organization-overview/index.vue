<template>
  <div>
    <div class="organization-info">
      <!-- TODO：循环生成 -->
      <card-chart title="馆藏数量保障概况">
        <div
          class="flex gap-5 flex-wrap"
          v-loading="isCollectionCountListLoading"
        >
          <card-indicator
            v-for="(item, index) in collectionCountList"
            :key="index"
            :data="item"
            class="responsive-width"
          ></card-indicator>
        </div>
      </card-chart>
      <card-chart title="重要收录保障概况">
        <div
          class="flex gap-5 flex-wrap"
          v-loading="isKeyCollectionListLoading"
        >
          <card-indicator
            v-for="(item, index) in keyCollectionList"
            :key="index"
            :data="item"
            class="responsive-width"
          ></card-indicator>
        </div>
      </card-chart>
      <card-chart title="成果保障概况">
        <div
          class="flex gap-5 flex-wrap"
          v-loading="isAchievementListLoading"
        >
          <card-indicator
            v-for="(item, index) in achievementList"
            :key="index"
            :data="item"
            class="responsive-width"
          ></card-indicator>
        </div>
      </card-chart>
      <card-chart title="文献行为概况">
        <div
          class="flex gap-5 flex-wrap"
          v-loading="isLiteratureBehaviorListLoading"
        >
          <card-indicator
            v-for="(item, index) in literatureBehaviorList"
            :key="index"
            :data="item"
            class="responsive-width"
          ></card-indicator>
        </div>
      </card-chart>
    </div>
  </div>
</template>

<script setup>
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import OrganizationInfo from './organization-info.vue';
import CardIndicator from '../components/card-indicator.vue';
import { useOrganizationOverview } from './useOrganizationOverview';

const {
  collectionCountList,
  isCollectionCountListLoading,
  getCollectionCountList,
  keyCollectionList,
  isKeyCollectionListLoading,
  getKeyCollectionList,
  achievementList,
  isAchievementListLoading,
  getAchievementList,
  literatureBehaviorList,
  isLiteratureBehaviorListLoading,
  getLiteratureBehaviorList,
} = useOrganizationOverview();

(async () => {
  getCollectionCountList();
  getKeyCollectionList();
  getAchievementList();
  getLiteratureBehaviorList();
})();
</script>

<style lang="scss" scoped>
.responsive-width {
  width: calc((100% - 120px) / 7);
}
.organization-info {
  .card-chart:not(:last-child) {
    margin-bottom: 20px;
  }
}
</style>
