<template>
  <div class="card-chart">
    <div class="head flex items-center h-54px text-sm text-[#34395E] px-20px">
      <div class="font-bold">{{ title }}</div>
      <div class="title-describe ml-10px">{{ describe }}</div>
    </div>

    <div
      class="operation flex items-center justify-between px-20px h-36px mb-14px"
      v-if="showChart || showTable || showExport || $slots['operation-left']"
    >
      <div class="left">
        <slot name="operation-left"></slot>
      </div>
      <div class="right">
        <div class="flex gap-10px">
          <div class="flex items-center border border-[#D4D7DF] h-30px rounded-15px w-84px bg-[#F2F4F5]" v-if="showChart || showTable">
            <div
              class="flex-1 flex items-center justify-center h-28px rounded-14px cursor-pointer hover:bg-white"
              :class="{
                active: displayType === 'chart',
              }"
              v-if="showChart"
              @click="handleShowChart"
            >
              <d-icon
                class="text-[#2D3240]"
                name="el-icon-vip-tubiao"
              ></d-icon>
            </div>
            <div
              class="flex-1 flex items-center justify-center h-28px rounded-14px cursor-pointer hover:bg-white"
              :class="{
                active: displayType === 'table',
              }"
              v-if="showTable"
              @click="handleShowTable"
            >
              <d-icon
                class="text-[#2D3240]"
                name="el-icon-vip-biaoge1"
              ></d-icon>
            </div>
          </div>

          <div
            class="flex-1 flex items-center justify-center h-30px w-30px rounded-15px cursor-pointer hover:bg-white bg-[#F2F4F5]"
            v-if="showExport"
          >
            <d-icon
              class="text-[#6777EF]"
              name="el-icon-vip-fenxiang3"
              @click="handleExport"
            ></d-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="w-full px-20px pb-20px">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  describe: {
    type: String,
  },
  showChart: {
    type: Boolean,
    default: false,
  },
  showTable: {
    type: Boolean,
    default: false,
  },
  showExport: {
    type: Boolean,
    default: false,
  },
  displayType: {
    type: String,
    default: 'chart',
  },
});
const emits = defineEmits(['toggle-type', 'export']);

const handleShowChart = () => {
  // eslint-disable-next-line vue/custom-event-name-casing
  emits('toggle-type', 'chart');
};
const handleShowTable = () => {
  // eslint-disable-next-line vue/custom-event-name-casing
  emits('toggle-type', 'table');
};
const handleExport = () => {
  emits('export');
};
</script>

<style lang="scss" scoped>
.card-chart {
  box-shadow: 0px 2px 15px 0px rgba(103, 119, 239, 0.1);
  border-radius: 3px 3px 3px 3px;
  .head {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 19px;
      width: 3px;
      height: 16px;
      background: #6777ef;
      border-radius: 0px 2px 2px 0px;
    }
  }
}
.active {
  background-color: #fff;
}
</style>
