<template>
  <div class="h-80px bg-[#F7F8FE] flex flex-col justify-center items-center rounded-3px">
    <div class="indicator-value flex justify-center items-center">
      <div class="text-[#34395E] font-bold text-2xl">{{ toThousandsSeparator(data.indicator_value) }}</div>
      <div
        class="text-[#404040] text-base ml-2px"
        v-if="data.indicator_ratio"
      >
        /{{ data.indicator_ratio }}%
      </div>
    </div>
    <div class="w-full indicator-name text-[#404040] text-sm truncate flex items-center justify-center">
      <span>{{ data.indicator_label }}（{{ data.unit }}）</span>
      <el-tooltip v-if="data.remark">
        <template v-slot:content>
          <div>{{ data.remark }}</div>
        </template>
        <d-icon
          name="el-icon-vip-icon-tishi41"
          class="text-primary"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { toThousandsSeparator } from '@/utils/helpers';

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});
</script>

<style lang="scss" scoped></style>
