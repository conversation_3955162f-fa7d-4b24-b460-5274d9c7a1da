export const academicAchievementsColumns = [
  {
    label: '年份',
    prop: 'year',
    align: 'center',
    sortable: true,
  },
  {
    label: '学术成果（篇）',
    prop: 'indicator_total',
    sortable: true,
  },
  {
    label: '馆藏保障量（篇）',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '保障率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: '未保障量（篇）',
    prop: 'indicator_sec_value',
    sortable: true,
  },
];

export const achievementsTop10Columns = [
  {
    label: '数据库',
    prop: 'dbname',
    sortable: true,
  },
  {
    label: '学术成果收录量（篇）',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '收录率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: '是否保障',
    prop: 'is_cover',
    sortable: true,
  },
];

export const journalColumns = [
  {
    label: '年份',
    prop: 'year',
    align: 'center',
    sortable: true,
  },
  {
    label: '发文期刊（种）',
    prop: 'indicator_total',
    sortable: true,
  },
  {
    label: '馆藏保障量（种）',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '保障率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: '未保障量（种）',
    prop: 'indicator_sec_value',
    sortable: true,
  },
];

export const journalTop10Columns = [
  {
    label: '数据库',
    prop: 'dbname',
    sortable: true,
  },
  {
    label: '发文期刊收录量（种）',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '收录率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: '是否保障',
    prop: 'is_cover',
    sortable: true,
  },
];
export const journalTop20Columns = [
  {
    label: '刊名',
    prop: 'title',
    sortable: true,
  },
  {
    label: 'ISSN',
    prop: 'issn',
    sortable: true,
  },
  {
    label: '发文量',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '是否保障',
    prop: 'is_cover',
    sortable: true,
  },
  {
    label: '收录数据库',
    prop: 'dbname',
    sortable: true,
  },
];
