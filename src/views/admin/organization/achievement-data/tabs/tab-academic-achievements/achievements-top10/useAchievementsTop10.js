import api from '@/api';

export const useAchievementsTop10 = () => {
  const achievementsTop10Data = ref([]);
  const isAchievementsTop10DataLoading = ref(false);
  const getAchievementsTop10Data = async (body) => {
    isAchievementsTop10DataLoading.value = true;
    try {
      const res = await api.organizationApi.getAchievementCollectionDatabaseTop10(body);
      achievementsTop10Data.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isAchievementsTop10DataLoading.value = false;
    }
  };

  return {
    achievementsTop10Data,
    isAchievementsTop10DataLoading,
    getAchievementsTop10Data,
  };
};
