<template>
  <card-chart
    title="发文期刊TOP20"
    describe="发文量前20的期刊。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          value-format="yyyy"
          :clearable="false"
          @change="getData"
          size="medium"
        ></el-date-picker>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch
            v-model="containOA"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="wordCloudOption"
      :events="{
        click: handleWordCloudClick,
      }"
      v-if="displayType === 'chart'"
      v-loading="isJournalTop20DataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="journalTop20Data"
      :columns="journalTop20Columns"
      v-loading="isJournalTop20DataLoading"
      null-text="-"
    >
      <template #is_cover="scope">
        <el-tag
          v-if="scope.row.is_cover === '1'"
          type="success"
        >
          是
        </el-tag>
        <el-tag
          v-else
          type="danger"
        >
          否
        </el-tag>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { createWordcloudChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useJournalTop20 } from './useJournalTop20';
import { journalTop20Columns } from '../columns';
import { computed } from 'vue';

const { journalTop20Data, isJournalTop20DataLoading, getJournalTop20Data } = useJournalTop20();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const containOA = ref('1');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

// 词云数据
const journalData = computed(() => {
  return journalTop20Data.value.map((item) => ({
    name: `《${item.title}》`,
    value: Number(item.indicator_value),
  }));
});

// 词云图配置
const wordCloudOption = computed(() => {
  return createWordcloudChart(
    { wordcloudData: journalData.value },
    {
      colors: ['#6777EF', '#36BDFF', '#F2A940', '#62AC00', '#FC544B'],
      sizeRange: [14, 50],
      rotationRange: [0, 0],
      shape: 'circle',
      customConfig: {},
    },
  );
});

// 处理点击事件
const handleWordCloudClick = (params) => {
  if (params.data) {
    console.log('点击了词云节点:', params.data.name, params.data.value);
    // 这里可以添加更多的业务逻辑
    // 例如：打开详情面板、触发数据加载等
  }
};

const getData = async () => {
  await getJournalTop20Data({
    year: year.value,
    oaFlag: containOA.value,
  });
};

getData();
</script>

<style scoped></style>
