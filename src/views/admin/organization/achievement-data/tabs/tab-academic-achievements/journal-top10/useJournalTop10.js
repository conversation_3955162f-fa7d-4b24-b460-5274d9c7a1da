import api from '@/api';

export const useJournalTop10 = () => {
  const journalTop10Data = ref([]);
  const isJournalTop10DataLoading = ref(false);
  const getJournalTop10Data = async (body) => {
    isJournalTop10DataLoading.value = true;
    try {
      const res = await api.organizationApi.getAchievementJournalDatabaseTop10(body);
      journalTop10Data.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isJournalTop10DataLoading.value = false;
    }
  };

  return {
    journalTop10Data,
    isJournalTop10DataLoading,
    getJournalTop10Data,
  };
};
