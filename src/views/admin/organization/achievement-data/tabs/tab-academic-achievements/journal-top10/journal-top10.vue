<template>
  <card-chart
    title="发文期刊收录TOP10（数据库）"
    describe="收录机构发文期刊数量最多的前10个数据库。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          value-format="yyyy"
          :clearable="false"
          @change="getData"
        ></el-date-picker>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch
            v-model="containOA"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="basicTreemapOption"
      :events="{
        click: handleTreemapClick,
      }"
      v-if="displayType === 'chart'"
      v-loading="isJournalTop10DataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="journalTop10Data"
      :columns="journalTop10Columns"
      v-loading="isJournalTop10DataLoading"
      null-text="-"
    >
      <template #is_cover="scope">
        <el-tag
          v-if="scope.row.is_cover === '1'"
          type="success"
        >
          是
        </el-tag>
        <el-tag
          v-else
          type="danger"
        >
          否
        </el-tag>
      </template>

      <template #indicator_ratio="scope">
        <span>{{ scope.row.indicator_ratio ?? 0 }}%</span>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { createTreemapChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useJournalTop10 } from './useJournalTop10';
import { journalTop10Columns } from '../columns';
import { computed } from 'vue';

const { journalTop10Data, isJournalTop10DataLoading, getJournalTop10Data } = useJournalTop10();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const containOA = ref('1');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

// 基础矩形树图数据
const basicTreeData = computed(() => {
  return journalTop10Data.value.map((item) => ({
    name: item.dbname,
    value: Number(item.indicator_value),
  }));
});

// 基础矩形树图配置
const basicTreemapOption = computed(() => {
  return createTreemapChart(
    { treeData: basicTreeData.value },
    {
      showLabel: true,
      roam: false,
      customConfig: {
        series: [
          {
            width: '100%',
            height: '100%',
            nodeClick: false,
            breadcrumb: {
              show: false,
            },
          },
        ],
      },
    },
  );
});

// 多层级矩形树图数据

// 存储点击信息
const clickInfo = ref(null);

// 处理点击事件
const handleTreemapClick = (params) => {
  if (params.data) {
    // 获取点击的节点信息
    clickInfo.value = {
      name: params.data.name,
      value: params.data.value,
      path: params.treePathInfo.map((item) => item.name).join(' > '),
      level: params.treePathInfo.length - 1,
      // 保存原始数据，以便进行更多操作
      rawData: params.data,
    };

    console.log('点击了矩形树图节点:', params);

    // 这里可以添加更多的业务逻辑
    // 例如：打开详情面板、下钻到下一级、触发数据加载等
  }
};

const getData = async () => {
  await getJournalTop10Data({
    year: year.value,
    oaFlag: containOA.value,
  });
};

getData();
</script>

<style scoped></style>
