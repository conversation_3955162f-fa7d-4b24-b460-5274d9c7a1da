<template>
  <div>
    <academic-achievements class="mb-20px"></academic-achievements>
    <achievements-top10 class="mb-20px"></achievements-top10>
    <journal class="mb-20px"></journal>
    <journal-top10 class="mb-20px"></journal-top10>
    <journal-top20 class="mb-20px"></journal-top20>
  </div>
</template>

<script setup>
import AcademicAchievements from './academic-achievements/academic-achievements.vue';
import AchievementsTop10 from './achievements-top10/achievements-top10.vue';
import Journal from './journal/journal.vue';
import JournalTop10 from './journal-top10/journal-top10.vue';
import JournalTop20 from './journal-top20/journal-top20.vue';
</script>

<style lang="scss" scoped></style>
