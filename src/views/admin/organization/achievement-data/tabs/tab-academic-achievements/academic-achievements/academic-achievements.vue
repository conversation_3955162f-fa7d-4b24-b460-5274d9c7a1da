<template>
  <card-chart
    title="学术成果"
    describe="机构学术成果数量及保障情况。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          size="medium"
          value-format="yyyy"
          :clearable="false"
          @change="getData"
        ></el-date-picker>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch
            v-model="containOA"
            active-value="1"
            inactive-value="0"
            @change="getData"
          ></el-switch>
        </div>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="lineChartData"
      v-if="displayType === 'chart'"
      v-loading="isAcademicAchievementsDataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="academicAchievementsData"
      :columns="academicAchievementsColumns"
      v-loading="isAcademicAchievementsDataLoading"
      null-text="-"
    >
      <template #indicator_ratio="scope">
        <span>{{ scope.row.indicator_ratio ?? 0 }}%</span>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { createLineChart, createBarChart, createScatterChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useAcademicAchievements } from './useAcademicAchievements';
import { academicAchievementsColumns } from '../columns';
import dayjs from 'dayjs';

const { academicAchievementsData, isAcademicAchievementsDataLoading, getAcademicAchievementsData } =
  useAcademicAchievements();

const now = dayjs();
const fiveYearsAgo = dayjs().subtract(5, 'year');
const timeRange = ref([fiveYearsAgo.format('YYYY'), now.format('YYYY')]); // 默认近5年

const containOA = ref('1');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});

const chartData = computed(() => [
  { name: '学术成果（篇）', data: academicAchievementsData.value.map((item) => item.indicator_total ?? 0) },
  { name: '学术成果保障量（篇）', data: academicAchievementsData.value.map((item) => item.indicator_value ?? 0) },
  {
    name: '学术成果保障率 （%）',
    data: academicAchievementsData.value.map((item) => item.indicator_ratio ?? 0),
    yAxisIndex: 1,
  },
]);
const xAxisData = computed(() => academicAchievementsData.value.map((item) => item.year));

const lineChartData = computed(() => {
  const chart = {
    xAxisData: xAxisData.value,
    lineData: chartData.value,
  };
  return createLineChart(chart, {
    customConfig: {
      yAxis: [
        {},
        {
          axisLabel: {
            formatter: (value) => value + '%',
          },
        },
      ],
      tooltip: {
        formatter: (params) => {
          // 特殊处理第三条折线
          if (params.length > 2) {
            const thirdLine = params[2];
            // 为第三条折线添加百分比格式
            return params
              .map((item, index) => {
                if (index === 2) {
                  return `${item.marker} ${item.seriesName}: ${Number(item.value).toFixed(1)}%`;
                }
                return `${item.marker} ${item.seriesName}: ${item.value}`;
              })
              .join('<br/>');
          }
          return params.map((item) => `${item.marker} ${item.seriesName}: ${item.value}`).join('<br/>');
        },
      },
    },
  });
});

const getData = async () => {
  await getAcademicAchievementsData({
    startTime: timeRange.value[0],
    endTime: timeRange.value[1],
    oaFlag: containOA.value,
  });
};

getData();
</script>

<style lang="scss" scoped></style>
