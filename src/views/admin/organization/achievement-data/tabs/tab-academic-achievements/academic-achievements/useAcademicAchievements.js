import api from '@/api';

export const useAcademicAchievements = () => {
  const academicAchievementsData = ref([]);
  const isAcademicAchievementsDataLoading = ref(false);
  const getAcademicAchievementsData = async (body) => {
    isAcademicAchievementsDataLoading.value = true;
    try {
      const res = await api.organizationApi.getAchievementData(body);
      academicAchievementsData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isAcademicAchievementsDataLoading.value = false;
    }
  };

  return {
    academicAchievementsData,
    isAcademicAchievementsDataLoading,
    getAcademicAchievementsData,
  };
};
