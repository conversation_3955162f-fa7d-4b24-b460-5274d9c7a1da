import api from '@/api';

export const useJournalTop20 = () => {
  const journalTop20Data = ref([]);
  const isJournalTop20DataLoading = ref(false);

  const getJournalTop20Data = async (body) => {
    isJournalTop20DataLoading.value = true;
    try {
      const res = await api.organizationApi.getReferenceJournalTop20(body);
      journalTop20Data.value = res.list || [];
    } catch (error) {
      console.log(error);
    } finally {
      isJournalTop20DataLoading.value = false;
    }
  };

  return {
    journalTop20Data,
    isJournalTop20DataLoading,
    getJournalTop20Data,
  };
};
