import api from '@/api';

export const useReferences = () => {
  const referencesData = ref([]);
  const isReferencesDataLoading = ref(false);
  const getReferencesData = async (body) => {
    isReferencesDataLoading.value = true;
    try {
      const res = await api.organizationApi.getReferenceData(body);
      referencesData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isReferencesDataLoading.value = false;
    }
  };

  return {
    referencesData,
    isReferencesDataLoading,
    getReferencesData,
  };
};
