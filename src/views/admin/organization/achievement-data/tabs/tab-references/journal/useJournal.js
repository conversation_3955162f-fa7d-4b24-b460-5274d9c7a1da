import api from '@/api';

export const useJournal = () => {
  const journalData = ref([]);
  const isJournalDataLoading = ref(false);
  const getJournalData = async (body) => {
    isJournalDataLoading.value = true;
    try {
      const res = await api.organizationApi.getReferenceJournal(body);
      journalData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isJournalDataLoading.value = false;
    }
  };

  return {
    journalData,
    isJournalDataLoading,
    getJournalData,
  };
};
