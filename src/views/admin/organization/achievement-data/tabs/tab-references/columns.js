export const referencesColumns = [
  {
    label: '年份',
    prop: 'year',
    align: 'center',
    sortable: true,
  },
  {
    label: '参考文献（篇）',
    prop: 'indicator_total',
    sortable: true,
  },
  {
    label: '参考次数',
    prop: 'indicator_count',
    sortable: true,
  },
  {
    label: '参考成本（元）',
    prop: 'cost',
    sortable: true,
  },
  {
    label: '馆藏保障量（篇）',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '保障率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: '未保障量（篇）',
    prop: 'indicator_sec_value',
    sortable: true,
  },
];

export const referencesTop20Columns = [
  {
    prop: 'doctitle',
    label: '标题',
    sortable: true,
  },
  {
    prop: 'indicator_value',
    label: '参考次数',
    sortable: true,
  },
  {
    prop: 'is_cover',
    label: '是否保障',
    sortable: true,
  },
  {
    prop: 'jutitle',
    label: '来源期刊',
    sortable: true,
  },
  {
    prop: 'dbname',
    label: '所在数据库',
    sortable: true,
  },
];

export const referencesTop10Columns = [
  {
    prop: 'dbname',
    label: '数据库',
    sortable: true,
  },
  {
    prop: 'indicator_value',
    label: '参考文献收录数量（篇）',
    sortable: true,
  },
  {
    prop: 'indicator_ratio',
    label: '收录率',
    sortable: true,
  },
  {
    prop: 'is_cover',
    label: '是否保障',
    sortable: true,
  },
];
export const journalColumns = [
  {
    label: '年份',
    prop: 'year',
    align: 'center',
    sortable: true,
  },
  {
    label: '参考期刊（种）',
    prop: 'indicator_total',
    sortable: true,
  },
  {
    label: '馆藏保障量（种）',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '保障率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: '未保障量（种）',
    prop: 'indicator_sec_value',
    sortable: true,
  },
];

export const journalTop10Columns = [
  {
    prop: 'dbname',
    label: '数据库',
    sortable: true,
  },
  {
    prop: 'indicator_value',
    label: '参考期刊收录数量（种）',
    sortable: true,
  },
  {
    prop: 'indicator_ratio',
    label: '收录率',
    sortable: true,
  },
  {
    prop: 'is_cover',
    label: '是否保障',
    sortable: true,
  },
];

export const journalTop20Columns = [
  {
    prop: 'title',
    label: '刊名',
    sortable: true,
  },
  {
    prop: 'issn',
    label: 'ISSN',
    sortable: true,
  },
  {
    prop: 'indicator_value',
    label: '参考次数',
    sortable: true,
  },
  {
    prop: 'is_cover',
    label: '是否保障',
    sortable: true,
  },
  {
    prop: 'dbname',
    label: '所在数据库',
    sortable: true,
  },
];
