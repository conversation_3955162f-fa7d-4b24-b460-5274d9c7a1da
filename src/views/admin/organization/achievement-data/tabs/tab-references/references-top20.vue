<template>
  <card-chart
    title="参考文献TOP20"
    describe="参考次数前20的期刊文献。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
        ></el-date-picker>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch v-model="containOA"></el-switch>
        </div>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="wordCloudOption"
      :events="{
        click: handleWordCloudClick,
      }"
    ></dv-chart>
  </card-chart>
</template>

<script setup>
import { createWordcloudChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';

const year = ref('');
const containOA = ref(true);

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});
// 词云数据
const journalData = ref([
  { name: 'Nature', value: 1200 },
  { name: 'Science', value: 1000 },
  { name: 'Cell', value: 900 },
  { name: 'PNAS', value: 850 },
  { name: 'NEJM', value: 800 },
  { name: 'Lancet', value: 750 },
  { name: 'JAMA', value: 700 },
  { name: 'BMJ', value: 650 },
  { name: 'PLoS ONE', value: 600 },
  { name: 'Scientific Reports', value: 550 },
  { name: 'IEEE Transactions', value: 500 },
  { name: 'Advanced Materials', value: 450 },
  { name: 'Chemical Reviews', value: 400 },
  { name: 'Angewandte Chemie', value: 350 },
  { name: 'ACS Nano', value: 300 },
  { name: 'Journal of Biological Chemistry', value: 250 },
  { name: 'Physical Review Letters', value: 200 },
  { name: 'Nucleic Acids Research', value: 150 },
  { name: 'Journal of the American Chemical Society', value: 100 },
  { name: 'Advanced Functional Materials', value: 50 },
]);

// 词云图配置
const wordCloudOption = computed(() => {
  return createWordcloudChart(
    { wordcloudData: journalData.value },
    {
      colors: ['#6777EF', '#36BDFF', '#F2A940', '#62AC00', '#FC544B'],
      sizeRange: [14, 50],
      rotationRange: [0, 0],
      shape: 'circle',
      customConfig: {},
    },
  );
});

// 处理点击事件
const handleWordCloudClick = (params) => {
  if (params.data) {
    console.log('点击了词云节点:', params.data.name, params.data.value);
    // 这里可以添加更多的业务逻辑
    // 例如：打开详情面板、触发数据加载等
  }
};
</script>

<style scoped></style>
