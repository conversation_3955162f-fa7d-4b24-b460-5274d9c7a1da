<template>
  <card-chart
    title="参考期刊收录TOP10（数据库）"
    describe="收录机构参考期刊数量最多的前10个数据库。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          @change="getData"
          value-format="yyyy"
          :clearable="false"
          size="medium"
        ></el-date-picker>
        <div>
          <span class="text-[#34395E] text-sm mr-10px">含OA资源</span>
          <el-switch
            v-model="containOA"
            @change="getData"
            active-value="1"
            inactive-value="0"
          ></el-switch>
        </div>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="basicTreemapOption"
      :events="{
        click: handleTreemapClick,
      }"
      v-if="displayType === 'chart'"
      v-loading="isJournalTop10DataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="journalTop10Data"
      :columns="journalTop10Columns"
      v-loading="isJournalTop10DataLoading"
      null-text="-"
    >
      <template #indicator_ratio="scope">
        <span>{{ scope.row.indicator_ratio ?? 0 }}%</span>
      </template>
      <template #is_cover="scope">
        <el-tag
          v-if="scope.row.is_cover === '1'"
          type="success"
        >
          是
        </el-tag>
        <el-tag
          v-else
          type="danger"
        >
          否
        </el-tag>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { createTreemapChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useJournalTop10 } from './useJournalTop10';
import dayjs from 'dayjs';
import { journalTop10Columns } from '../columns';

const { journalTop10Data, isJournalTop10DataLoading, getJournalTop10Data } = useJournalTop10();

const year = ref(dayjs().format('YYYY'));
const containOA = ref('1');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

// 基础矩形树图配置
const basicTreemapOption = computed(() => {
  return createTreemapChart(
    {
      treeData: journalTop10Data.value.map((item) => ({
        name: item.dbname,
        value: Number(item.indicator_value),
      })),
    },
    {
      showLabel: true,
      roam: false,
      customConfig: {
        series: [
          {
            width: '100%',
            height: '100%',
            nodeClick: false,
            breadcrumb: {
              show: false,
            },
          },
        ],
      },
    },
  );
});

// 存储点击信息
const clickInfo = ref(null);

// 处理点击事件
const handleTreemapClick = (params) => {
  if (params.data) {
    // 获取点击的节点信息
    clickInfo.value = {
      name: params.data.name,
      value: params.data.value,
      path: params.treePathInfo.map((item) => item.name).join(' > '),
      level: params.treePathInfo.length - 1,
      // 保存原始数据，以便进行更多操作
      rawData: params.data,
    };

    console.log('点击了矩形树图节点:', params);
  }
};

const getData = async () => {
  await getJournalTop10Data({
    year: year.value,
    oaFlag: containOA.value,
  });
};

(async () => {
  getData();
})();
</script>

<style scoped></style>
