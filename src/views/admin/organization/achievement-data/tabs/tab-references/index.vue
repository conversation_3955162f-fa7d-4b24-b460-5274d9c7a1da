<template>
  <div>
    <references class="mb-20px"></references>
    <references-top20 class="mb-20px"></references-top20>
    <references-top10 class="mb-20px"></references-top10>
    <journal class="mb-20px"></journal>
    <journal-top10 class="mb-20px"></journal-top10>
    <journal-top20 class="mb-20px"></journal-top20>
  </div>
</template>

<script setup>
import References from './references/references.vue';
import ReferencesTop20 from './references-top20.vue';
import ReferencesTop10 from './references-top10.vue';
import Journal from './journal.vue';
import JournalTop10 from './journal-top10.vue';
import JournalTop20 from './journal-top20.vue';
</script>

<style lang="scss" scoped></style>
