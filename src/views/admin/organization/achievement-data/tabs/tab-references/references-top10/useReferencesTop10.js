import api from '@/api';

export const useReferencesTop10 = () => {
  const referencesTop10Data = ref([]);
  const isReferencesTop10DataLoading = ref(false);

  const getReferencesTop10Data = async (body) => {
    isReferencesTop10DataLoading.value = true;
    try {
      const res = await api.organizationApi.getReferenceTop10(body);
      referencesTop10Data.value = res.list || [];
    } catch (error) {
      console.log(error);
    } finally {
      isReferencesTop10DataLoading.value = false;
    }
  };

  return {
    referencesTop10Data,
    isReferencesTop10DataLoading,
    getReferencesTop10Data,
  };
};
