import api from '@/api';

export const useReferencesTop20 = () => {
  const referencesTop20Data = ref([]);
  const isReferencesTop20DataLoading = ref(false);

  const getReferencesTop20Data = async (body) => {
    isReferencesTop20DataLoading.value = true;
    try {
      const res = await api.organizationApi.getReferenceTop20(body);
      referencesTop20Data.value = res.list || [];
    } catch (error) {
      console.log(error);
    } finally {
      isReferencesTop20DataLoading.value = false;
    }
  };

  return {
    referencesTop20Data,
    isReferencesTop20DataLoading,
    getReferencesTop20Data,
  };
};
