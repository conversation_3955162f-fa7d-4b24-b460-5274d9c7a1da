<template>
  <div>
    <div class="h-11 flex gap-10px">
      <div
        class="tab-item cursor-pointer h-9 leading-9 bg-[#F0F4FF] text-[#697FFA] px-4 rounded-3px text-sm"
        v-for="tab in tabs"
        :key="tab.name"
        @click="handleTabClick(tab)"
        :class="{
          active: tab.name === currentTabName,
        }"
      >
        {{ tab.label }}
      </div>
    </div>
    <tab-academic-achievements v-if="currentTabName === '1'"></tab-academic-achievements>
    <tab-references v-if="currentTabName === '2'"></tab-references>
  </div>
</template>

<script setup>
import TabAcademicAchievements from './tabs/tab-academic-achievements/index.vue';
import TabReferences from './tabs/tab-references/index.vue';

const tabs = ref([
  {
    name: '1',
    label: '学术成果',
  },
  {
    name: '2',
    label: '参考文献',
  },
]);

const currentTabName = ref('2');

const handleTabClick = (tab) => {
  currentTabName.value = tab.name;
};
</script>

<style lang="scss" scoped>
.tab-item.active {
  color: white;
  background: #697ffa;
}
</style>
