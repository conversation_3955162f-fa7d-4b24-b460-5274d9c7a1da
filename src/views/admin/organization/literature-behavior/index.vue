<template>
  <div>
    <div>
      <div class="h-11 flex gap-10px">
        <div
          class="tab-item cursor-pointer h-9 leading-9 bg-[#F0F4FF] text-[#697FFA] px-4 rounded-3px text-sm"
          v-for="tab in tabs"
          :key="tab.name"
          @click="handleTabClick(tab)"
          :class="{
            active: tab.name === currentTabName,
          }"
        >
          {{ tab.label }}
        </div>
      </div>
      <tab-database v-if="currentTabName === '1'"></tab-database>
      <tab-document-delivery v-if="currentTabName === '2'"></tab-document-delivery>
      <tab-book-borrowing v-if="currentTabName === '3'"></tab-book-borrowing>
    </div>
  </div>
</template>

<script setup>
import TabDatabase from './tabs/tab-database/index.vue';
import TabDocumentDelivery from './tabs/tab-document-delivery/index.vue';
import TabBookBorrowing from './tabs/tab-book-borrowing';

const tabs = ref([
  {
    name: '1',
    label: '数据库',
  },
  // TODO:暂时注释
  // {
  //   name: '2',
  //   label: '文献传递',
  // },
  {
    name: '3',
    label: '纸书借阅',
  },
]);

const currentTabName = ref('1');

const handleTabClick = (tab) => {
  currentTabName.value = tab.name;
};
</script>

<style lang="scss" scoped>
.tab-item.active {
  color: white;
  background: #697ffa;
}
</style>
