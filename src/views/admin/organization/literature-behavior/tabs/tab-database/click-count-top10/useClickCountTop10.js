import api from '@/api';

export const useClickCountTop10 = () => {
  const clickCountTop10Data = ref([]);
  const isClickCountTop10DataLoading = ref(false);

  const clickCountTop10Columns = [
    {
      prop: 'dbname',
      label: '数据库',
      sortable: true,
    },
    {
      prop: 'indicator_value',
      label: '数据库点击量（次）',
      sortable: true,
    },
  ];

  const getClickCountTop10Data = async (body) => {
    isClickCountTop10DataLoading.value = true;
    try {
      const res = await api.organizationApi.getDatabaseClickTop10(body);
      clickCountTop10Data.value = res.list || [];
    } catch (error) {
      console.log(error);
    } finally {
      isClickCountTop10DataLoading.value = false;
    }
  };

  return {
    clickCountTop10Data,
    isClickCountTop10DataLoading,
    getClickCountTop10Data,
    clickCountTop10Columns,
  };
};
