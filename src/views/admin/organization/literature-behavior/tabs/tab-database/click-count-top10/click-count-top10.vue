<template>
  <card-chart
    title="数据库点击量(TOP10)"
    describe="点击次数前10的数据库。"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          @change="getData"
          value-format="yyyy"
          :clearable="false"
        ></el-date-picker>
      </div>
    </template>
    <d-table
      :data="clickCountTop10Data"
      :columns="clickCountTop10Columns"
      v-loading="isClickCountTop10DataLoading"
      null-text="-"
    ></d-table>
  </card-chart>
</template>

<script setup>
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useClickCountTop10 } from './useClickCountTop10';
import dayjs from 'dayjs';

const { clickCountTop10Data, isClickCountTop10DataLoading, getClickCountTop10Data, clickCountTop10Columns } =
  useClickCountTop10();

const year = ref(dayjs().format('YYYY'));

const getData = async () => {
  await getClickCountTop10Data({
    year: year.value,
  });
};

(async () => {
  getData();
})();
</script>

<style scoped></style>
