<template>
  <card-chart
    title="数据库点击量(TOP10)"
    describe="点击次数前10的数据库。"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
        ></el-date-picker>
      </div>
    </template>
    TODO:表格
  </card-chart>
</template>

<script setup>
import { createTreemapChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';

const year = ref('');
const containOA = ref(true);
</script>

<style scoped></style>
