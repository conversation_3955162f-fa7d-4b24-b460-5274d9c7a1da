<template>
  <card-chart
    title="数据库点击量"
    describe="机构学者点击数据库的次数。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <el-date-picker
        v-model="timeRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="pickerOptions"
        size="medium"
        value-format="yyyy-MM-dd"
        :clearable="false"
        @change="getData"
      ></el-date-picker>
    </template>
    <dv-chart
      class="h-300px"
      :option="lineChartData"
      v-if="displayType === 'chart'"
      v-loading="isClickCountDataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="clickCountData"
      :columns="clickCountColumns"
      v-loading="isClickCountDataLoading"
      null-text="-"
    ></d-table>
  </card-chart>
</template>

<script setup>
import { createLineChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useClickCount } from './useClickCount';
import dayjs from 'dayjs';

const { clickCountData, isClickCountDataLoading, getClickCountData, clickCountColumns } = useClickCount();

const now = dayjs();
const fiveYearsAgo = dayjs().subtract(5, 'year');
const timeRange = ref([fiveYearsAgo.format('YYYY-MM-DD'), now.format('YYYY-MM-DD')]); // 默认近5年

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});

const chartData = computed(() => [
  { name: '点击量', data: clickCountData.value.map((item) => item.indicator_value || 0) },
]);

const xAxisData = computed(() => clickCountData.value.map((item) => item.year || ''));

const lineChartData = computed(() => {
  const chart = {
    xAxisData: xAxisData.value,
    lineData: chartData.value,
  };
  return createLineChart(chart, {
    customConfig: {},
  });
});

const getData = async () => {
  await getClickCountData({
    startTime: timeRange.value[0],
    endTime: timeRange.value[1],
  });
};

(async () => {
  getData();
})();
</script>

<style lang="scss" scoped></style>
