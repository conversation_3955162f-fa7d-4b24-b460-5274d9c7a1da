import api from '@/api';

export const useClickCount = () => {
  const clickCountData = ref([]);
  const isClickCountDataLoading = ref(false);

  const clickCountColumns = [
    {
      prop: 'year',
      label: '年份',
      sortable: true,
    },

    {
      prop: 'indicator_value',
      label: '数据库点击量（次）',
      sortable: true,
    },
  ];

  const getClickCountData = async (body) => {
    isClickCountDataLoading.value = true;
    try {
      const res = await api.organizationApi.getDatabaseClickCount(body);
      clickCountData.value = res.list || [];
    } catch (error) {
      console.log(error);
    } finally {
      isClickCountDataLoading.value = false;
    }
  };

  return {
    clickCountData,
    isClickCountDataLoading,
    getClickCountData,
    clickCountColumns,
  };
};
