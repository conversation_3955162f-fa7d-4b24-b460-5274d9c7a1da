<template>
  <card-chart
    title="文献接收TOP10（文献）"
    describe="接收次数前10的文献。"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          size="medium"
          value-format="yyyy"
          :clearable="false"
        ></el-date-picker>
      </div>
    </template>
    TODO:表格
  </card-chart>
</template>

<script setup>
import { createTreemapChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import dayjs from 'dayjs';

const now = dayjs();
const fiveYearsAgo = dayjs().subtract(5, 'year');
const timeRange = ref([fiveYearsAgo.format('YYYY'), now.format('YYYY')]); // 默认近5年

const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});
</script>

<style scoped></style>
