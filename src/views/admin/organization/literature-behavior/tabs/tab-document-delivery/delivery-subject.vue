<template>
  <card-chart
    title="传递文献学科分布"
    describe="不同学科分类下的文献传递数量。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
        ></el-date-picker>
        <el-select
          v-model="subject"
          placeholder="请选择学科"
        >
          <el-option
            v-for="item in [
              { label: '中文', value: '1' },
              { label: '外文', value: '2' },
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
    </template>
    <dv-chart
      class="h-300px"
      :option="verticalBarOption"
      v-if="displayType === 'chart'"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="tableData"
      :columns="columns"
    ></d-table>
  </card-chart>
</template>

<script setup>
import { createBarChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';

const year = ref('');
const subject = ref('');
const containOA = ref(true);
const removeDuplicates = ref(true);

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

// 示例数据
const categories = ref(['一月', '二月', '三月', '四月', '五月', '六月']);
const seriesData = ref([
  { name: '收入', data: [320, 332, 301, 334, 390, 330] },
  { name: '支出', data: [120, 132, 101, 134, 90, 230] },
]);

// 创建纵向柱状图配置
const verticalBarOption = computed(() => {
  return createBarChart(
    {
      xAxisData: categories.value,
      barData: seriesData.value,
    },
    {
      customConfig: {},
    },
  );
});
</script>

<style lang="scss" scoped></style>
