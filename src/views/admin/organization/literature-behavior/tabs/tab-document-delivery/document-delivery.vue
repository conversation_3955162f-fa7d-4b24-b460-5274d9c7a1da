<template>
  <card-chart
    title="文献传递量"
    describe="机构学者传递文献的次数。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <el-date-picker
        v-model="timeRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="pickerOptions"
        size="medium"
        value-format="yyyy"
        :clearable="false"
      ></el-date-picker>
    </template>
    <dv-chart
      class="h-300px"
      :option="lineChartData"
      v-if="displayType === 'chart'"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="tableData"
      :columns="columns"
    ></d-table>
  </card-chart>
</template>

<script setup>
import { createLineChart, createBarChart, createScatterChart } from '@/utils/chartConfig';
import { ref } from 'vue';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import dayjs from 'dayjs';

const now = dayjs();
const fiveYearsAgo = dayjs().subtract(5, 'year');
const timeRange = ref([fiveYearsAgo.format('YYYY'), now.format('YYYY')]); // 默认近5年

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});

const chartData = ref([
  { name: '访问量', data: [1000, 2000] },
  { name: '成交量', data: [500, 1500] },
]);
const xAxisData = ref(['周一', '周二']);

const lineChartData = computed(() => {
  const chart = {
    xAxisData: xAxisData.value,
    lineData: chartData.value,
  };
  return createLineChart(chart, {
    customConfig: {
      // legend: {
      //   right: 20, // 调整图例位置
      // },
    },
  });
});
</script>

<style lang="scss" scoped></style>
