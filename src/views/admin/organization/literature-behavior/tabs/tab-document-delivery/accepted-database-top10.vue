<template>
  <card-chart
    title="文献接收TOP10（数据库）"
    describe="接收次数前10的数据库。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <el-date-picker
        v-model="year"
        type="year"
        placeholder="请选择年份"
      ></el-date-picker>
    </template>
    <dv-chart
      class="h-300px"
      :option="basicTreemapOption"
      :events="{
        click: handleTreemapClick,
      }"
      v-if="displayType === 'chart'"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="tableData"
      :columns="columns"
    ></d-table>
  </card-chart>
</template>

<script setup>
import { createTreemapChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';

const year = ref('');

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

// 基础矩形树图数据
const basicTreeData = ref([
  {
    name: '图书',
    value: 6000,
  },
  {
    name: '期刊',
    value: 4000,
  },
  {
    name: '数据库',
    value: 3000,
  },
  {
    name: '多媒体',
    value: 2000,
  },
  {
    name: '报纸',
    value: 1000,
  },
]);

// 基础矩形树图配置
const basicTreemapOption = computed(() => {
  return createTreemapChart(
    { treeData: basicTreeData.value },
    {
      showLabel: true,
      roam: false,
      customConfig: {
        series: [
          {
            width: '100%',
            height: '100%',
            nodeClick: false,
            breadcrumb: {
              show: false,
            },
          },
        ],
      },
    },
  );
});

// 多层级矩形树图数据

// 存储点击信息
const clickInfo = ref(null);

// 处理点击事件
const handleTreemapClick = (params) => {
  if (params.data) {
    // 获取点击的节点信息
    clickInfo.value = {
      name: params.data.name,
      value: params.data.value,
      path: params.treePathInfo.map((item) => item.name).join(' > '),
      level: params.treePathInfo.length - 1,
      // 保存原始数据，以便进行更多操作
      rawData: params.data,
    };

    console.log('点击了矩形树图节点:', params);

    // 这里可以添加更多的业务逻辑
    // 例如：打开详情面板、下钻到下一级、触发数据加载等
  }
};
</script>

<style scoped></style>
