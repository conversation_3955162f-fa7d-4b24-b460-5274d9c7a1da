<template>
  <div>
    <document-delivery class="mb-20px"></document-delivery>
    <delivery-article-top10 class="mb-20px"></delivery-article-top10>
    <delivery-database-top10 class="mb-20px"></delivery-database-top10>
    <delivery-subject class="mb-20px"></delivery-subject>
    <accepted-volume class="mb-20px"></accepted-volume>
    <accepted-article-top10 class="mb-20px"></accepted-article-top10>
    <accepted-database-top10 class="mb-20px"></accepted-database-top10>
    <accepted-subject class="mb-20px"></accepted-subject>
  </div>
</template>

<script setup>
import DocumentDelivery from './document-delivery.vue';
import DeliveryArticleTop10 from './delivery-article-top10.vue';
import DeliveryDatabaseTop10 from './delivery-database-top10.vue';
import DeliverySubject from './delivery-subject.vue';
import AcceptedVolume from './accepted-volume.vue';
import AcceptedArticleTop10 from './accepted-article-top10.vue';
import AcceptedDatabaseTop10 from './accepted-database-top10.vue';
import AcceptedSubject from './accepted-subject.vue';
</script>

<style lang="scss" scoped></style>
