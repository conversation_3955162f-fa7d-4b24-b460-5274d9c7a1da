<template>
  <card-chart
    title="纸质图书借阅TOP10"
    describe="借阅次数前10的纸质图书。"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <div class="flex gap-10px">
          <el-date-picker
            v-model="year"
            type="year"
            placeholder="请选择年份"
            @change="getData"
            value-format="yyyy"
            :clearable="false"
            size="medium"
          ></el-date-picker>
        </div>
      </div>
    </template>
    <d-table
      :data="bookBorrowingTop10Data"
      :columns="bookBorrowingTop10Columns"
      v-loading="isBookBorrowingTop10DataLoading"
      null-text="-"
    ></d-table>
  </card-chart>
</template>

<script setup>
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useBookBorrowingTop10 } from './useBookBorrowingTop10';
import dayjs from 'dayjs';

const {
  bookBorrowingTop10Data,
  isBookBorrowingTop10DataLoading,
  getBookBorrowingTop10Data,
  bookBorrowingTop10Columns,
} = useBookBorrowingTop10();

const year = ref(dayjs().format('YYYY'));

const getData = async () => {
  await getBookBorrowingTop10Data({
    year: year.value,
  });
};

(async () => {
  getData();
})();
</script>

<style scoped></style>
