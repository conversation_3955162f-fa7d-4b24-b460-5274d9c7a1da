import api from '@/api';

export const useBookBorrowingTop10 = () => {
  const bookBorrowingTop10Data = ref([]);
  const isBookBorrowingTop10DataLoading = ref(false);

  const bookBorrowingTop10Columns = [
    {
      prop: 'title',
      label: '题名',
      sortable: true,
    },
    {
      prop: 'isbn',
      label: 'ISBN',
      sortable: true,
    },
    {
      prop: 'indicator_value',
      label: '借阅量（次）',
      sortable: true,
    },
  ];

  const getBookBorrowingTop10Data = async (body) => {
    isBookBorrowingTop10DataLoading.value = true;
    try {
      const res = await api.organizationApi.getBookBorrowingTop10(body);
      bookBorrowingTop10Data.value = res.list || [];
    } catch (error) {
      console.log(error);
    } finally {
      isBookBorrowingTop10DataLoading.value = false;
    }
  };

  return {
    bookBorrowingTop10Data,
    isBookBorrowingTop10DataLoading,
    getBookBorrowingTop10Data,
    bookBorrowingTop10Columns,
  };
};
