<template>
  <card-chart
    title="文献传递TOP10（文献）"
    describe="传递次数前10的文献。"
    show-export
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <div class="flex gap-10px">
          <el-date-picker
            v-model="year"
            type="year"
            placeholder="请选择年份"
          ></el-date-picker>
        </div>
      </div>
    </template>
    TODO:表格
  </card-chart>
</template>

<script setup>
import { createTreemapChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';

const year = ref('');

const pickerOptions = ref({
  shortcuts: [
    {
      text: '近3年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 3);
        picker.$emit('pick', [start, end]);
      },
    },
    {
      text: '近5年',
      onClick(picker) {
        const end = new Date();
        const start = new Date();
        start.setFullYear(start.getFullYear() - 5);
        picker.$emit('pick', [start, end]);
      },
    },
  ],
});
</script>

<style scoped></style>
