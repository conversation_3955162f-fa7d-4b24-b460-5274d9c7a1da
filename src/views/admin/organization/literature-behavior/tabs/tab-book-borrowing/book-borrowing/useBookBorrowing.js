import api from '@/api';

export const useBookBorrowing = () => {
  const bookBorrowingData = ref([]);
  const isBookBorrowingDataLoading = ref(false);

  const bookBorrowingColumns = [
    {
      prop: 'year',
      label: '年份',
      sortable: true,
    },
    {
      prop: 'indicator_value',
      label: '借阅量（次）',
      sortable: true,
    },
  ];

  const getBookBorrowingData = async (body) => {
    isBookBorrowingDataLoading.value = true;
    try {
      const res = await api.organizationApi.getBookBorrowingCount(body);
      bookBorrowingData.value = res.list || [];
    } catch (error) {
      console.log(error);
    } finally {
      isBookBorrowingDataLoading.value = false;
    }
  };

  return {
    bookBorrowingData,
    isBookBorrowingDataLoading,
    getBookBorrowingData,
    bookBorrowingColumns,
  };
};
