import api from '@/api';

export const usePartition = () => {
  const partitionData = ref([]);
  const isPartitionDataLoading = ref(false);
  const getPartitionData = async (body) => {
    isPartitionDataLoading.value = true;
    try {
      const res = await api.organizationApi.getImportantPartition(body);
      partitionData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isPartitionDataLoading.value = false;
    }
  };

  return {
    partitionData,
    isPartitionDataLoading,
    getPartitionData,
  };
};
