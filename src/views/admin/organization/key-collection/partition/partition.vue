<template>
  <card-chart
    title="重要收录期刊分区保障分析"
    describe="常见中外文重要收录期刊分区保障情况。"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <div class="flex gap-10px">
          <el-date-picker
            v-model="year"
            type="year"
            placeholder="请选择年份"
            value-format="yyyy"
            :clearable="false"
            @change="getData"
            size="medium"
          ></el-date-picker>

          <el-select
            v-model="partition"
            placeholder="请选择分区"
            @change="getData"
            size="medium"
          >
            <el-option
              v-for="item in selectStore.selects.core_mark_quartile_category"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
    </template>
    <div class="flex gap-20px w-full">
      <div class="flex-1 min-w-300px">
        <dv-chart
          class="h-300px"
          :option="compareFunnelOption"
          v-loading="isPartitionDataLoading"
        ></dv-chart>
      </div>
      <div class="flex-1 min-w-300px">
        <d-table
          :data="partitionData"
          :columns="partitionColumns"
          v-loading="isPartitionDataLoading"
          null-text="-"
        >
          <template #indicator_ratio="scope">
            <span>{{ scope.row.indicator_ratio ?? 0 }}%</span>
          </template>
        </d-table>
      </div>
    </div>
  </card-chart>
</template>

<script setup>
import { createFunnelChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { usePartition } from './usePartition';
import store from '@/store';
import { partitionColumns } from '../columns';
import { computed } from 'vue';

const selectStore = store.useSelect();

const { partitionData, isPartitionDataLoading, getPartitionData } = usePartition();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);
const partition = ref('1');

const topData = computed(() => {
  return partitionData.value.filter((item) => item.data_type === '0');
});
const otherData = computed(() => {
  return partitionData.value.filter((item) => item.data_type === '1');
});
const currentPeriodData = computed(() => [
  ...topData.value.map((item) => ({
    name: item.data_version,
    value: item.indicator_ratio,
  })),
  ...otherData.value.map((item) => {
    return {
      name: item.data_version,
      value: item.indicator_ratio,
    };
  }),
]);

// 创建对比漏斗图配置
const compareFunnelOption = computed(() => {
  return createFunnelChart(
    {
      // 只传入当前周期数据，上一周期数据通过customConfig添加
      funnelData: currentPeriodData.value,
    },
    {
      colors: ['#6777EF', '#36BDFF', '#F2A940', '#62AC00', '#53B666'],
      sort: 'descending',
      gap: 4,
    },
  );
});

const getData = async () => {
  await getPartitionData({
    year: year.value,
    quartileCategory: partition.value,
  });
};

getData();
</script>

<style lang="scss" scoped></style>
