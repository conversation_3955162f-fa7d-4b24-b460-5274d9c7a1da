import api from '@/api';

export const useDatabaseTop10 = () => {
  const databaseTop10Data = ref([]);
  const isDatabaseTop10DataLoading = ref(false);
  const getDatabaseTop10Data = async (body) => {
    isDatabaseTop10DataLoading.value = true;
    try {
      const res = await api.organizationApi.getImportantDatabaseTop10(body);
      databaseTop10Data.value = res.list;
      // databaseTop10Data.value.forEach((item) => {
      //   item.indicator_value = item.indicator_value?.toString();
      // });
    } catch (error) {
      console.log(error);
    } finally {
      isDatabaseTop10DataLoading.value = false;
    }
  };

  return {
    databaseTop10Data,
    isDatabaseTop10DataLoading,
    getDatabaseTop10Data,
  };
};
