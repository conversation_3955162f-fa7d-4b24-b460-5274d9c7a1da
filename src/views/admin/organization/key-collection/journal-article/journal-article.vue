<template>
  <card-chart
    title="重要收录期刊文献保障情况"
    describe="常见中外文重要数据库收录期刊文献的保障量、保障率。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <el-date-picker
        v-model="year"
        type="year"
        placeholder="请选择年份"
        value-format="yyyy"
        :clearable="false"
        @change="getData"
      ></el-date-picker>
    </template>
    <dv-chart
      class="h-300px"
      :option="verticalBarOption"
      v-if="displayType === 'chart'"
      v-loading="isJournalArticleDataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="journalArticleData"
      :columns="journalArticleColumns"
      v-loading="isJournalArticleDataLoading"
      null-text="-"
    >
      <template #indicator_ratio="scope">
        <span>{{ scope.row.indicator_ratio ?? 0 }}%</span>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { createBarChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import { useJournalArticle } from './useJournalArticle';
import { journalArticleColumns } from '../columns';
import { computed } from 'vue';
import { toThousandsSeparator } from '@/utils/helpers';

const { journalArticleData, isJournalArticleDataLoading, getJournalArticleData } = useJournalArticle();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

// 示例数据
const categories = computed(() => journalArticleData.value.map((item) => item.indicator_name));
const seriesData = computed(() => [
  { name: '保障率', data: journalArticleData.value.map((item) => item.indicator_ratio) },
]);
// 创建纵向柱状图配置
const verticalBarOption = computed(() => {
  return createBarChart(
    {
      xAxisData: categories.value,
      barData: seriesData.value,
    },
    {
      horizontal: true,
      customConfig: {
        tooltip: {
          formatter: (params) => {
            let result = `${params[0].axisValue}<br/>`;
            params.forEach((param) => {
              result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`;
            });
            return result;
          },
        },
        xAxis: {
          axisLabel: {
            formatter: (value) => toThousandsSeparator(value) + '%',
            color: '#909399',
          },
        },
      },
    },
  );
});

const getData = async () => {
  await getJournalArticleData({
    year: year.value,
  });
};

getData();
</script>

<style scoped></style>
