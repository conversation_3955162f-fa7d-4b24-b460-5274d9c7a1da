import api from '@/api';

export const useJournalArticle = () => {
  const journalArticleData = ref([]);
  const isJournalArticleDataLoading = ref(false);
  const getJournalArticleData = async (body) => {
    isJournalArticleDataLoading.value = true;
    try {
      const res = await api.organizationApi.getImportantArticle(body);
      journalArticleData.value = res.list;
    } catch (error) {
      console.log(error);
    } finally {
      isJournalArticleDataLoading.value = false;
    }
  };

  return {
    journalArticleData,
    isJournalArticleDataLoading,
    getJournalArticleData,
  };
};
