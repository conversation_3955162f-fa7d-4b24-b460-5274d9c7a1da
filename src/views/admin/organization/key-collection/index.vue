<template>
  <div>

    <journal class="mb-5"></journal>
    <partition class="mb-5"></partition>
    <database-top10 class="mb-5"></database-top10>
    <journal-article class="mb-5"></journal-article>
  </div>
</template>

<script setup>
import Journal from './journal/journal.vue';
import Partition from './partition/partition.vue';
import DatabaseTop10 from './database-top10/database-top10.vue';
import JournalArticle from './journal-article/journal-article.vue';


</script>

<style lang="scss" scoped></style>
