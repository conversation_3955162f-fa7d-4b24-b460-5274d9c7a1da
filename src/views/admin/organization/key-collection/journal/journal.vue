<template>
  <card-chart
    title="重要收录期刊保障情况"
    describe="常见中外文重要收录期刊的保障量、保障率。"
    show-chart
    show-table
    show-export
    :display-type="displayType"
    @toggle-type="handleToggleType"
  >
    <template #operation-left>
      <div class="flex gap-20px items-center">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          :clearable="false"
          value-format="yyyy"
          @change="getData"
          size="medium"
        ></el-date-picker>
      </div>
    </template>
    <dv-chart
      class="h-540px"
      :option="verticalBarOption"
      v-if="displayType === 'chart'"
      v-loading="isJournalDataLoading"
    ></dv-chart>
    <d-table
      v-if="displayType === 'table'"
      :data="journalData"
      :columns="journalColumns"
      v-loading="isJournalDataLoading"
      null-text="-"
    >
      <template #indicator_ratio="scope">
        <span>{{ scope.row.indicator_ratio ?? 0 }}%</span>
      </template>
    </d-table>
  </card-chart>
</template>

<script setup>
import { createBarChart } from '@/utils/chartConfig';
import CardChart from '@/views/admin/organization/components/card-chart.vue';
import dayjs from 'dayjs';
import { useJournal } from './useJournal';
import { toThousandsSeparator } from '@/utils/helpers';
import { journalColumns } from '../columns';

const { journalData, isJournalDataLoading, getJournalData } = useJournal();

const currentYear = new Date().getFullYear()
  .toString();
const year = ref(currentYear);

const displayType = ref('chart');
const handleToggleType = (type) => {
  displayType.value = type;
};

// 示例数据
const categories = computed(() => journalData.value.map((item) => item.indicator_label));
const seriesData = computed(() => [
  { name: '保障率', data: journalData.value.map((item) => item.indicator_ratio || 0) },
]);

// 创建纵向柱状图配置
const verticalBarOption = computed(() => {
  return createBarChart(
    {
      xAxisData: categories.value,
      barData: seriesData.value,
    },
    {
      horizontal: true,
      customConfig: {
        tooltip: {
          formatter: (params) => {
            let result = `${params[0].axisValue}<br/>`;
            params.forEach((param) => {
              result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`;
            });
            return result;
          },
        },
        xAxis: {
          axisLabel: {
            formatter: (value) => toThousandsSeparator(value) + '%',
            color: '#909399',
          },
        },
      },
    },
  );
});

const getData = async () => {
  await getJournalData({
    year: year.value,
  });
};

getData();
</script>

<style lang="scss" scoped></style>
