export const journalColumns = [
  {
    label: '重要收录',
    prop: 'indicator_label',
    sortable: true,
  },
  {
    label: '版本',
    prop: 'data_version',
    sortable: true,
  },
  {
    label: '期刊总量',
    prop: 'indicator_total',
    sortable: true,
  },
  {
    label: '期刊保障量',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '期刊保障率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: '未保障量',
    prop: 'indicator_sec_value',
    sortable: true,
  },
];

export const partitionColumns = [
  {
    label: '重要收录',
    prop: 'data_version',
    sortable: true,
  },
  {
    label: '期刊总量',
    prop: 'indicator_total',
    sortable: true,
  },
  {
    label: '馆藏保障量',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '保障率',
    prop: 'indicator_ratio',
    sortable: true,
  },
  {
    label: '未保障量',
    prop: 'indicator_sec_ratio',
    sortable: true,
  },
];

export const databaseTop10Columns = [
  {
    label: '数据库',
    prop: 'dbname',
    sortable: true,
  },
  {
    label: '未保障重要收录期刊收录量（种）',
    prop: 'indicator_value',
    sortable: true,
  },
];

export const journalArticleColumns = [
  {
    label: '重要收录',
    prop: 'indicator_name',
    sortable: true,
  },
  {
    label: '版本',
    prop: 'data_version',
    sortable: true,
  },
  {
    label: '期刊文献总量',
    prop: 'indicator_total',
    sortable: true,
  },
  {
    label: '馆藏保障量',
    prop: 'indicator_value',
    sortable: true,
  },
  {
    label: '保障率',
    prop: 'indicator_ratio',
    sortable: true,
  },
];
