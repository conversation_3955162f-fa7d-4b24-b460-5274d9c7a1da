<template>
  <div>
    <d-card class="mb-5 text-[#34395E]">
      <template #title>
        <div
          class="tab-top cursor-pointer leading-64px px-10px"
          v-for="tab in TABS_TOP"
          :key="tab.value"
          :class="{
            active: currentTabTop === tab.value,
          }"
          @click="handleChangeTopTab(tab)"
        >
          {{ tab.label }}
        </div>
      </template>
      <organization-info v-if="currentTabTop === 'organization-overview'"></organization-info>
      <collection-info v-if="currentTabTop === 'collection-quantity'"></collection-info>
      <key-collection-info v-if="currentTabTop === 'key-collection'"></key-collection-info>
      <achievement-info v-if="currentTabTop === 'achievement-data'"></achievement-info>
      <literature-info v-if="currentTabTop === 'literature-behavior'"></literature-info>
    </d-card>

    <d-card
      content-class="text-blue-500"
      title-class="justify-between"
    >
      <organization-overview v-if="currentTabTop === 'organization-overview'"></organization-overview>
      <collection-quantity v-if="currentTabTop === 'collection-quantity'"></collection-quantity>
      <key-collection v-if="currentTabTop === 'key-collection'"></key-collection>
      <achievement-data v-if="currentTabTop === 'achievement-data'"></achievement-data>
      <literature-behavior v-if="currentTabTop === 'literature-behavior'"></literature-behavior>
      <report v-if="currentTabTop === 'report'"></report>
    </d-card>
  </div>
</template>

<script setup>
import DrawerBase from '@/components/drawer-base';
import { TABS_TOP } from './constants';
import OrganizationOverview from './organization-overview/index.vue';
import OrganizationInfo from './organization-overview/organization-info.vue';
import CollectionQuantity from './collection-quantity/index.vue';
import CollectionInfo from './collection-quantity/collection-info.vue';
import KeyCollection from './key-collection/index.vue';
import KeyCollectionInfo from './key-collection/key-collection-info.vue';
import AchievementData from './achievement-data/index.vue';
import AchievementInfo from './achievement-data/achievement-info.vue';
import LiteratureBehavior from './literature-behavior/index.vue';
import LiteratureInfo from './literature-behavior/literature-info.vue';
import Report from './report/index.vue';

const currentTabTop = ref('organization-overview');
const handleChangeTopTab = (tab) => {
  currentTabTop.value = tab.value;
};
</script>

<style lang="scss" scoped>
.tab-top {
  &.active {
    position: relative;
    color: #6777ef;
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: #6777ef;
    }
  }
}
</style>
