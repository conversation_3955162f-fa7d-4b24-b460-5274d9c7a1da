<template>
  <div>
    <d-card
      content-class="text-blue-500 "
      title-class="justify-between"
    >
      <template #title>
        <div class="flex gap-20px">
          <el-input
            v-model="keyword"
            placeholder=""
            size="medium"
          ></el-input>
          <d-button>查找</d-button>
        </div>
        <div>
          <d-button
            type="primary-light"
            border
          >
            导出所选
          </d-button>
          <d-button
            type="success-light"
            border
          >
            导出所有
          </d-button>
        </div>
      </template>
      <d-table
        :data="tableData"
        :columns="columns"
        selectable
        :pagination="{
          max: Infinity,
          pageSizes: [10, 20, 50, 100, 150, 200],
          total: total,
          pageSize: pagination.pageSize,
          currentPage: pagination.pageIndex,
          onCurrentPageChange,
          onPageSizeChange,
        }"
      ></d-table>
    </d-card>
  </div>
</template>

<script setup>
import { columns } from './columns';

const keyword = ref('');
const tableData = ref([
  {
    dbname: 'jack',
  },
]);
const total = ref(0);
const pagination = ref({
  pageIndex: 1,
  pageSize: 10,
});
const onCurrentPageChange = (page) => {
  pagination.value.pageIndex = page;
};

const onPageSizeChange = (size) => {
  pagination.value.pageSize = size;
};
</script>

<style lang="scss" scoped></style>
