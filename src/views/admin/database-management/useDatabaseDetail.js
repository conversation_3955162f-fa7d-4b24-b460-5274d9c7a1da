import api from '@/api';

export const useDatabaseDetail = () => {
  const databaseDetail = ref({});
  const isDatabaseDetailLoading = ref(false);
  const getDatabaseDetail = async (id) => {
    isDatabaseDetailLoading.value = true;
    try {
      const res = await api.databaseApi.getDatabaseDetail(id);
      databaseDetail.value = res;
      // databaseDetail.value.list.forEach((item) => {
      //   item.year = item.year?.toString();
      // });
    } catch (error) {
      console.log(error);
    } finally {
      isDatabaseDetailLoading.value = false;
    }
  };

  const docTypes = ref([]);
  const getDocType = async () => {
    try {
      const res = await api.databaseApi.getDocType();
      docTypes.value = res?.map((item) => ({
        label: item.label,
        value: item.type,
      }));
    } catch (error) {
      console.log(error);
    }
  };

  const subjectEscs = ref([]);
  const getSubjectEsc = async () => {
    try {
      const res = await api.databaseApi.getSubjectEsc();
      subjectEscs.value = res?.map((item) => ({
        label: item.domainName,
        value: item.domainIdCode,
      }));
    } catch (error) {
      console.log(error);
    }
  };

  return {
    docTypes,
    getDocType,
    subjectEscs,
    getSubjectEsc,
    databaseDetail,
    isDatabaseDetailLoading,
    getDatabaseDetail,
  };
};
