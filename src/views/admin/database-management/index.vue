<template>
  <div>
    <d-card
      content-class="text-blue-500"
      title-class="justify-between"
    >
      <template #title>
        <div class="text-[#6777EF]">
          <d-icon name="el-icon-vip-biaoge">数据库管理</d-icon>
        </div>
      </template>
      <div class="flex items-center gap-20px mb-5">
        <el-input
          v-model="databaseName"
          placeholder="请输入数据库名称"
          size="medium"
          class="!w-400px"
          clearable
          @clear="handleSearch"
        ></el-input>
        <d-button
          type="primary"
          icon="el-icon-vip-sousuo"
          @click="handleSearch"
        >
          查找
        </d-button>
      </div>

      <d-table
        v-loading="isDatabaseListLoading"
        :data="databaseList"
        use-sticky-header
        :columns="listColumns"
        :pagination="{
          max: Infinity,
          pageSizes: [10, 20, 50, 100, 150, 200],
          total: total,
          pageSize: pagination.pageSize,
          currentPage: pagination.pageIndex,
          onCurrentPageChange,
          onPageSizeChange,
        }"
        null-text="-"
      >
        <template #lan="scope">
          <span>{{ findLabelByValue(selectStore.selects.lan, scope.row.lan) }}</span>
        </template>
        <template #orderType="scope">
          <span>{{ findLabelByValue(selectStore.selects.order_type, scope.row.orderType) }}</span>
        </template>
        <template #btns="scope">
          <d-button
            class="!mr-2"
            size="mini"
            icon="el-icon-vip-bianjizuopin"
            type="primary-light"
            @click="handleEdit(scope.row)"
          >
            编辑
          </d-button>
        </template>
      </d-table>
    </d-card>

    <dialog-database-detail
      v-if="isDialogVisible"
      :database-id="databaseIdForEdit"
      :visible.sync="isDialogVisible"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    ></dialog-database-detail>
  </div>
</template>

<script setup>
import { useDatabaseManagement } from './useDatabaseManagement.js';
import { listColumns } from './columns';
import DialogDatabaseDetail from './dialog-database-detail.vue';
import { findLabelByValue } from '@/utils/helpers';
import store from '@/store';
import api from '@/api';

const selectStore = store.useSelect();

const {
  databaseName,
  databaseList,
  isDatabaseListLoading,
  getDatabaseList,
  total,
  pagination,
  onCurrentPageChange,
  onPageSizeChange,
  handleSearch,
} = useDatabaseManagement();

const isDialogVisible = ref(false);

const databaseIdForEdit = ref('');

const handleEdit = async (row) => {
  databaseIdForEdit.value = row.id;
  await nextTick();
  isDialogVisible.value = true;
};

const handleConfirm = async (list) => {
  // 发起请求，保存
  try {
    const res = await api.databaseApi.saveDatabase(list);
    if (res.statusCode === 200) {
      DMessage.message({ type: 'success', message: '保存成功！', showClose: true, center: true });
      await getDatabaseList();
      isDialogVisible.value = false;
    }
  } catch (error) {
    console.log(error);
  }
};

const handleCancel = () => {
  isDialogVisible.value = false;
};

(async () => {
  await getDatabaseList();
})();
</script>

<style lang="scss" scoped></style>
