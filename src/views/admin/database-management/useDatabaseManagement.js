import api from '@/api';

export const useDatabaseManagement = () => {
  const databaseName = ref('');
  const pagination = ref({
    pageIndex: 1,
    pageSize: 10,
  });

  const databaseList = ref([]);
  const isDatabaseListLoading = ref(false);
  const total = ref(0);
  const getDatabaseList = async () => {
    isDatabaseListLoading.value = true;
    try {
      const res = await api.databaseApi.getDatabaseList({ ...pagination.value, name: databaseName.value });

      databaseList.value = res.list;
      total.value = res.total;
    } catch (error) {
      console.log(error);
    } finally {
      isDatabaseListLoading.value = false;
    }
  };

  const onCurrentPageChange = (page) => {
    pagination.value.pageIndex = page;
    getDatabaseList();
  };

  const onPageSizeChange = (size) => {
    pagination.value.pageSize = size;
    getDatabaseList();
  };

  const handleSearch = () => {
    pagination.value.pageIndex = 1;
    getDatabaseList();
  };

  return {
    databaseName,
    databaseList,
    isDatabaseListLoading,
    getDatabaseList,
    total,
    pagination,
    onCurrentPageChange,
    onPageSizeChange,
    handleSearch,
  };
};
