<template>
  <d-dialog
    title="数据库详情"
    type="ok"
    width="1200px"
    :visible="props.visible"
    @update:visible="emit('update:visible', $event)"
  >
    <el-form
      inline
      :model="databaseDetail"
    >
      <el-form-item label="数据库名称">
        <span>{{ databaseDetail.name }}</span>
      </el-form-item>
      <el-form-item label="采购状态">
        <span>{{ findLabelByValue(selectStore.selects.order_type, databaseDetail.orderType) }}</span>
      </el-form-item>
      <el-form-item label="起始年">
        <span>{{ databaseDetail.year ?? '-' }}</span>
      </el-form-item>
      <el-form-item label="数据库出版商">
        <span>{{ databaseDetail.supplierName || '-' }}</span>
      </el-form-item>
      <el-form-item label="学科分类">
        <span :title="findLabelByValue(subjectEscs, databaseDetail.subjectEsc)">
          {{ findLabelByValue(subjectEscs, databaseDetail.subjectEsc) }}
        </span>
      </el-form-item>
      <el-form-item label="语言">
        <span>{{ findLabelByValue(selectStore.selects.lan, databaseDetail.lan) }}</span>
      </el-form-item>
      <el-form-item label="文献类型">
        <span :title="findLabelByValue(docTypes, databaseDetail.type)">
          {{ findLabelByValue(docTypes, databaseDetail.type) }}
        </span>
      </el-form-item>
      <el-form-item label="数据库简介">
        <span>{{ databaseDetail.briefIntro || '-' }}</span>
      </el-form-item>
    </el-form>

    <div class="flex justify-between gap-10px items-end border-1 border-[#E4E6FC] p-10px">
      <d-table
        class="w-[calc(100%-30px)]"
        v-loading="isDatabaseDetailLoading"
        :data="databaseDetail.list"
        :columns="detailColumns"
      >
        <template #year-th="scope">
          <span class="flex items-center justify-center">
            <span class="text-red-300">*</span>
            {{ scope.column.label }}
          </span>
        </template>
        <template #orderType-th="scope">
          <span class="flex items-center justify-center">
            <span class="text-red-300">*</span>
            {{ scope.column.label }}
          </span>
        </template>
        <template #amount-th="scope">
          <span class="flex items-center justify-center">
            <span class="text-red-300">*</span>
            {{ scope.column.label }}
          </span>
        </template>
        <template #year="scope">
          <el-date-picker
            v-model="scope.row.year"
            type="year"
            placeholder="选择年份"
            size="medium"
            class="!w-140px"
            value-format="yyyy"
            clearable
          ></el-date-picker>
        </template>
        <template #orderType="scope">
          <el-select
            v-model="scope.row.orderType"
            placeholder="请选择"
            size="medium"
            clearable
          >
            <el-option
              v-for="item in selectStore.selects.order_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </template>
        <template #amount="scope">
          <el-input
            v-model="scope.row.amount"
            placeholder="请输入金额"
            size="medium"
            clearable
          ></el-input>
        </template>
        <template #filePath="scope">
          <div class="flex justify-center items-center">
            <div
              class="flex-1 truncate cursor-pointer text-[#6777EF] hover:underline"
              @click="downLoadContract"
              v-if="scope.row.filePath"
            >
              {{ scope.row.filePath }}
            </div>
            <el-upload
              action="/"
              ref="uploadfile"
              :multiple="false"
              :http-request="(fileObject) => uploadFile(fileObject, scope.row)"
              :show-file-list="false"
              :before-upload="beforeUpload"
            >
              <d-button
                type="primary-light"
                border
                size="mini"
              >
                上传
              </d-button>
            </el-upload>
          </div>
        </template>
        <template #btns="scope">
          <d-button
            type="danger-light"
            icon="el-icon-vip-icon-shanchu1"
            size="mini"
            @click="delContract(scope)"
            :confirm="{
              content: '是否确认删除？',
            }"
          >
            删除
          </d-button>
        </template>
      </d-table>
      <div
        class="border-1 border-[#E4E6FC] w-36px h-36px bg-white flex items-center justify-center cursor-pointer flex-shrink-0 rounded-3px"
        @click.stop="handleAdd"
      >
        <img
          src="./imgs/plus.png"
          class="w-5 h-5"
          alt=""
        />
      </div>
    </div>

    <template #footer>
      <d-button
        type="gray-light"
        icon="el-icon-vip-quxiao"
        @click="handleCancel"
      >
        取消
      </d-button>
      <d-button
        type="primary"
        icon="el-icon-vip-baocun1"
        @click="handleConfirm"
      >
        保存
      </d-button>
    </template>
  </d-dialog>
</template>

<script setup>
import { detailColumns } from './columns';
import { useDatabaseDetail } from './useDatabaseDetail';
import store from '@/store';
import { findLabelByValue } from '@/utils/helpers';
import api from '@/api';

const props = defineProps({
  databaseId: {
    type: String,
    required: true,
  },
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible', 'confirm', 'cancel']);
const selectStore = store.useSelect();

const { docTypes, getDocType, subjectEscs, getSubjectEsc, databaseDetail, isDatabaseDetailLoading, getDatabaseDetail } =
  useDatabaseDetail();

const handleConfirm = () => {
  // 校验必填项
  if (!databaseDetail.value.list.every((item) => item.year && item.orderType && item.amount)) {
    DMessage.message({ type: 'error', message: '请填写完整信息！', showClose: true, center: true });
    return;
  }
  // 校验list中的年份是否有相同项
  if (new Set(databaseDetail.value.list.map((item) => item.year)).size !== databaseDetail.value.list.length) {
    DMessage.message({ type: 'error', message: '年份不能重复！', showClose: true, center: true });
    return;
  }
  // 对databaseDetail.value.list进行处理，里面每一项如果没有srcdb，就加上srcdb。
  databaseDetail.value.list.forEach((item) => {
    if (!item.srcdb) {
      item.srcdb = databaseDetail.value.srcdb;
    }
  });
  emit('confirm', databaseDetail.value.list);
};

const handleCancel = () => {
  emit('cancel');
};

const delContract = (scope) => {
  databaseDetail.value.list.splice(scope.$index, 1);
};

const downLoadContract = () => {
  console.log('下载合同');
};

const handleAdd = () => {
  databaseDetail.value.list.push({
    year: '',
    orderType: '',
    amount: '',
    filePath: '',
  });
};

const beforeUpload = (file) => {
  return new Promise((resolve, reject) => {
    // 限制文件大小
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      DMessage.message({ type: 'error', message: '上传文件大小不能超过 10MB!', showClose: true, center: true });
      // eslint-disable-next-line no-promise-executor-return, prefer-promise-reject-errors
      return reject();
    }
    resolve();
  });
};

const uploadFile = async (fileObject, row) => {
  const fd = new FormData();
  fd.append('file ', fileObject.file);
  const res = await api.databaseApi.uploadFile(fd);
  if (res.statusCode === 200) {
    DMessage.message({ type: 'success', message: '上传成功！', showClose: true, center: true });
    // 将文件名在row的filePath里面
    row.filePath = fileObject.file.name;
  } else {
    DMessage.message({ type: 'error', message: '上传失败！', showClose: true, center: true });
  }
};

(async () => {
  await getDocType();
  await getSubjectEsc();
  await getDatabaseDetail(props.databaseId);
})();
</script>

<style lang="scss" scoped>
.el-form--inline .el-form-item:not(:nth-child(3n)) {
  width: 42%;
}
.el-form--inline .el-form-item:last-child {
  width: 100%;
}
.el-form--inline .el-form-item:not(:last-child) :deep(.el-form-item__content) {
  // 单行溢出省略号
  max-width: 240px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.el-form--inline .el-form-item:last-child :deep(.el-form-item__content) {
  width: calc(100% - 82px);
}
</style>
