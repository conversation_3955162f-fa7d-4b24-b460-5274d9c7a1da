import { defineStore } from 'pinia';
import api from '@/api';
import route, { router } from '@/router';
import { usePermission } from './permission';
import { APP_CODE, storage } from '@vpscope/dlib3-utils';

export const useNav = defineStore({
  id: 'nav',
  state: () => ({
    top: {},
  }),
  getters: {
    routes() {
      if (!route.admin.length) return [];
      const permissionStore = usePermission();
      const getChildren = (treeNode) => {
        return (
          treeNode.permissionNodes
            ?.filter((node) => node.router || node.permissionNodes?.length)
            ?.map((node) => {
              const current = route.admin.find((r) => r.path.endsWith(`/${APP_CODE}` + node.router));
              return {
                name: node.name,
                path: '/' + APP_CODE + node.router,
                component: current?.component,
                hidden: current?.hidden,
                pid: node?.pid,
                meta: current?.meta,
                children: getChildren(node) || [],
              };
            }) || []
        );
      };

      const routeConfigs = getChildren(permissionStore.tree);

      if (routeConfigs) {
        const appRoutes = routeConfigs
          .flatBy('children', Infinity)
          // .uniqueBy('path')
          .map((r) => ({ ...r, children: undefined })); // 清除children，防止children中的path和外层以经拍平的path重复

        router.addRoutes([
          {
            path: '/' + APP_CODE,
            redirect: '/' + APP_CODE + '/admin-home',
          },
          ...appRoutes,
        ]);
      }
      return routeConfigs || [];
    },
  },
  actions: {
    async updateTop() {
      this.top = await api.nav.getTop();
    },
  },
});
