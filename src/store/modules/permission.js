import { defineStore } from 'pinia';
import api from '@/api';

export const usePermission = defineStore({
  id: 'permission',
  state: () => ({
    auth: {
      app: false,
      admin: false,
    },
    tree: [],
  }),
  actions: {
    async updateAuth() {
      this.auth = await api.permission.getAuth();
    },
    async updateTree() {
      this.tree = await api.permission.getTree();
    },
  },
});
