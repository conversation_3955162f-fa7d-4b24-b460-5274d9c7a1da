import { defineStore } from 'pinia';
import api from '@/api';
import { storage } from '@vpscope/dlib3-utils';

export const useInfo = defineStore({
  id: 'info',
  state: () => ({
    base: {},
    app: {},
  }),
  actions: {
    async updateBase() {
      this.base = await api.info.getBase();
      storage.set('fileURL', this.base.orgInfo.fileUrl);
      storage.local.set('fileURL', this.base.orgInfo.fileUrl);
    },
    async updateApp() {
      this.app = await api.info.getApp();
    },
  },
});
