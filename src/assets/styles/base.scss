body {
  font-family: PingFang SC, PingFang SC, Microsoft YaHei, helvetica, Arial, Verdana, Sans-serif;
}

* {
  box-sizing: border-box;
}

i {
  font-style: inherit;
}

.text-ellipsis-1 {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

/**加载圈居中*/
.el-loading-spinner {
  align-items: center;
  justify-content: center;
  display: flex;
}

/**tab-切换框*/
.el-tabs {
  .el-tabs__header {
    margin-bottom: 0;
  }
}

.el-tabs__nav-scroll {
  .el-tabs__item {
    font-size: 16px;
    height: 60px;
    line-height: 60px;
  }
}

.el-tabs__nav-wrap::after {
  height: 1px !important;
}

.el-popover {
  min-width: 60px !important;
  min-width: 60px !important;
}

.el-input-group__append,
.el-input-group__prepend {
  background-color: #fff !important;
  color: #34395E !important;
}

.el-time-spinner__wrapper {
  overflow: hidden !important;
}

// 解决element-ui级联选择器中radio和checkbox点击文字无法选中的问题
.popper .el-cascader-panel .el-radio {
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 0px;
  right: 0px;
}

.popper .el-cascader-panel .el-checkbox {
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 0px;
  right: 0px;
}

.popper .el-cascader-panel .el-radio__input {
  margin-top: 10px;
  margin-left: 8px;
}

.popper .el-cascader-panel .el-checkbox__input {
  margin-top: 2px;
  margin-left: 8px;
}

.popper .el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}

.el-tooltip__popper {
  max-width: 400px !important;
}

.indent-2em {
  text-indent: 2em;
}

.el-date-editor .el-range-separator {
  width: 22px !important;
}
