/* 改变主题色变量 */
$--color-primary: #6777ef;

/* 改变 icon 字体路径变量，必需 */
$--font-path: 'element-ui/lib/theme-chalk/fonts';

@import 'element-ui/packages/theme-chalk/src/index.scss';

.el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  /*滚动条宽度*/
  height: 8px;
  /*滚动条高度*/
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  border-radius: 10px;
  /*滚动条的背景区域的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(238, 238, 238, 0.3);
  background-color: #eeeeee;
  /*滚动条的背景颜色*/
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /*滚动条的圆角*/
  -webkit-box-shadow: inset 0 0 6px rgba(145, 143, 0143, 0.3);
  background-color: rgb(145, 143, 143);
  /*滚动条的背景颜色*/
}

.d-table.el-table--striped .el-table__body tr.el-table__row--striped:hover td.el-table__cell {
  background: #f5f7fa !important;
}
