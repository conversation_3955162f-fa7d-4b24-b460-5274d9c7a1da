/**
 * @summary 全路由
 * @example
 * import route, { router } from '@/router';
 *
 * console.log(router); // vue-router实例
 * console.log(route.admin); // 后台路由配置
 * console.log(route.app); // 前台路由配置
 */

import VueRouter from 'vue-router';
import { getExportsFromModules, getBaseInfo } from '@vpscope/dlib3-utils';
import store from '@/store';

const modules = import.meta.glob('./modules/*.js', { eager: true });
export default getExportsFromModules(modules);

export const router = new VueRouter({
  routes: [
    {
      path: '/',
      redirect: '/' + APP_CODE + '/admin-home',
    },
  ],
  mode: 'history',
});

const setDocumentTitle = async (...titleList) => {
  const infoStore = store.useInfo();
  const baseInfo = await getBaseInfo();
  const title = titleList
    .concat([infoStore.app.appName ?? '', baseInfo.orgInfo.orgName ?? ''])
    .flat()
    .map((t) => t?.trim())
    .filter((t) => t)
    .join(' - ');

  document.title = title;
  return title;
};

router.afterEach((to, from) => {
  document.querySelector('#main-container')?.scrollTo(0, 0);
  const titleList = to.meta?.breadcrumb?.map((item) => item.label ?? '')?.reverse() ?? [];

  setDocumentTitle(titleList);
});
