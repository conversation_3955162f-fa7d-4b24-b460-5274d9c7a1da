export const admin = [
  {
    path: `/${APP_CODE}/admin-home`,
    component: () => import('@/views/admin/organization/index.vue'),
    meta: {
      breadcrumb: [{ label: '机构' }],
    },
  },
  {
    path: `/${APP_CODE}/admin-search`,
    component: () => import('@/views/admin/search/index.vue'),
    meta: {
      breadcrumb: [{ label: '搜索' }],
    },
  },
  {
    path: `/${APP_CODE}/admin-subject`,
    component: () => import('@/views/admin/subject/index.vue'),
    meta: {
      breadcrumb: [{ label: '学科' }],
    },
  },
  {
    path: `/${APP_CODE}/admin-database`,
    component: () => import('@/views/admin/database/index.vue'),
    meta: {
      breadcrumb: [{ label: '数据库' }],
    },
  },
  {
    path: `/${APP_CODE}/admin-indicator-retrieval`,
    component: () => import('@/views/admin/indicator-retrieval/index.vue'),
    meta: {
      breadcrumb: [{ label: '指标检索' }],
    },
  },
  {
    path: `/${APP_CODE}/admin-database-management`,
    component: () => import('@/views/admin/database-management/index.vue'),
    meta: {
      breadcrumb: [{ label: '数据库管理' }],
    },
  },
  {
    path: `/${APP_CODE}/admin-resource-management`,
    component: () => import('@/views/admin/resource-management/index.vue'),
    meta: {
      breadcrumb: [{ label: '资源管理' }],
    },
  },
  {
    path: `/${APP_CODE}/admin-user-management`,
    component: () => import('@/views/admin/user-management/index.vue'),
    meta: {
      breadcrumb: [{ label: '用户管理' }],
    },
  },
  {
    path: `/${APP_CODE}/admin-system-settings`,
    component: () => import('@/views/admin/system-settings/index.vue'),
    meta: {
      breadcrumb: [{ label: '系统设置' }],
    },
  },
];
