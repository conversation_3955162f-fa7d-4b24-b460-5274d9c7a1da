{"name": "performance", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env VITE_APP_MODE=development vite --force --host", "dev:mock": "cross-env VITE_APP_MODE=mock vite --host --force", "build": "vite build", "deploy:build:test": "vite build && d-deploy to test", "deploy:test": "d-deploy to test"}, "devDependencies": {"@babel/eslint-parser": "^7.22.10", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue2": "^2.2.0", "@vpscope/dalib-deploy": "1.0.1", "@vpscope/replace-utils-imports-plugin": "latest", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^8.24.0", "eslint-config-alloy": "^5.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-vue": "^9.16.1", "only-allow": "^1.1.1", "signale": "^1.4.0", "terser": "^5.26.0", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-plugin-windicss": "^1.9.1", "vue-eslint-parser": "^9.3.1"}, "dependencies": {"@vpscope/dlib3-app-frame-render": "^1.2.0-beta.28", "@vpscope/dlib3-datavis": "1.0.4", "@vpscope/dlib3-ui": "1.2.3-beta.3", "@vpscope/dlib3-utils": "0.0.2", "@vueuse/core": "^10.3.0", "dayjs": "^1.11.9", "decimal.js": "^10.5.0", "element-ui": "^2.15.14", "lodash": "^4.17.21", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.11.2", "sass": "1.32.6", "vue": "^2.7.14", "vue-router": "^3", "windicss": "^3.5.6", "xlsx": "^0.18.5"}}